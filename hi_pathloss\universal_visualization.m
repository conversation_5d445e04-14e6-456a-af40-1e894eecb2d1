% 通用可视化函数 - 自动检测并适配字体显示
function universal_visualization()
    close all;
    clear;
    clc;
    
    fprintf('=== 通用可视化系统 ===\n');
    
    % 检查并加载实验结果
    if exist('algorithm_energy_comparison_results.mat', 'file')
        load('algorithm_energy_comparison_results.mat');
        fprintf('✓ 成功加载实验结果\n');
    else
        fprintf('❌ 未找到实验结果文件，请先运行 algorithm_energy_comparison\n');
        return;
    end
    
    % 提取数据
    energy_results = results.energy_results;
    energy_std = results.energy_std;
    
    % 自动检测系统语言环境并设置标签
    labels = setup_universal_labels();
    
    % 设置通用字体
    setup_universal_fonts();
    
    % 生成高质量图表
    generate_publication_quality_plots(energy_results, energy_std, labels);
    
    fprintf('✓ 通用可视化图表生成完成\n');
end

function labels = setup_universal_labels()
    % 设置通用标签系统
    
    % 检测系统语言环境
    try
        % 尝试显示中文字符
        test_fig = figure('Visible', 'off');
        text(0.5, 0.5, '测试中文显示', 'FontName', 'SimHei');
        close(test_fig);
        use_chinese = true;
        fprintf('✓ 检测到中文字体支持\n');
    catch
        use_chinese = false;
        fprintf('⚠️  使用英文标签以确保兼容性\n');
        if exist('test_fig', 'var') && ishandle(test_fig)
            close(test_fig);
        end
    end
    
    if use_chinese
        % 中文标签
        labels.scenarios = {'静态监测场景', '动态转换场景', '周期性运动场景'};
        labels.algorithms = {'固定功率', '简单DQN', '分层RL'};
        labels.title_main = '算法能耗对比分析';
        labels.xlabel = '运动场景';
        labels.ylabel = '平均能耗 (mJ)';
        labels.ylabel_std = '平均标准差 (mJ)';
        labels.ylabel_percent = '相对变化 (%)';
        labels.ylabel_diff = '算法间差异 (mJ)';
        labels.ylabel_potential = '改进潜力 (%)';
        labels.legend_title = '算法类型';
        labels.subtitles = {'(a) 能耗对比', '(b) 相对性能变化', '(c) 算法稳定性', ...
                           '(d) 场景复杂度', '(e) 算法排名', '(f) 改进潜力分析'};
        labels.heatmap_title = '算法能耗热力图 (mJ)';
    else
        % 英文标签
        labels.scenarios = {'Static Monitoring', 'Dynamic Transition', 'Periodic Motion'};
        labels.algorithms = {'Fixed Power', 'Simple DQN', 'Hierarchical RL'};
        labels.title_main = 'Algorithm Energy Consumption Comparison';
        labels.xlabel = 'Motion Scenarios';
        labels.ylabel = 'Average Energy Consumption (mJ)';
        labels.ylabel_std = 'Average Standard Deviation (mJ)';
        labels.ylabel_percent = 'Relative Change (%)';
        labels.ylabel_diff = 'Algorithm Difference (mJ)';
        labels.ylabel_potential = 'Improvement Potential (%)';
        labels.legend_title = 'Algorithm Types';
        labels.subtitles = {'(a) Energy Comparison', '(b) Relative Performance', '(c) Algorithm Stability', ...
                           '(d) Scenario Complexity', '(e) Algorithm Ranking', '(f) Improvement Potential'};
        labels.heatmap_title = 'Algorithm Energy Consumption Heatmap (mJ)';
    end
end

function setup_universal_fonts()
    % 设置通用字体系统
    
    % 按优先级尝试字体
    font_priority = {'Times New Roman', 'Arial', 'Helvetica', 'DejaVu Sans', 'Liberation Sans'};
    
    selected_font = 'Arial'; % 默认字体
    
    for i = 1:length(font_priority)
        try
            test_fig = figure('Visible', 'off');
            text(0.5, 0.5, 'Test Font', 'FontName', font_priority{i});
            selected_font = font_priority{i};
            close(test_fig);
            break;
        catch
            if exist('test_fig', 'var') && ishandle(test_fig)
                close(test_fig);
            end
            continue;
        end
    end
    
    % 设置全局字体参数
    set(0, 'DefaultAxesFontName', selected_font);
    set(0, 'DefaultTextFontName', selected_font);
    set(0, 'DefaultAxesFontSize', 12);
    set(0, 'DefaultTextFontSize', 12);
    set(0, 'DefaultAxesFontWeight', 'normal');
    set(0, 'DefaultTextFontWeight', 'normal');
    
    fprintf('✓ 使用字体: %s\n', selected_font);
end

function generate_publication_quality_plots(energy_results, energy_std, labels)
    % 生成发表级质量的图表
    
    % 设置高质量图形参数
    set(0, 'DefaultFigureRenderer', 'painters');
    set(0, 'DefaultFigurePaperPositionMode', 'auto');
    
    % 图1: 主要对比图 - 分组柱状图
    fig1 = figure('Position', [100, 100, 1000, 600]);
    set(fig1, 'Color', 'white');
    
    bar_data = energy_results';
    h = bar(bar_data, 'grouped', 'LineWidth', 1.2);
    
    % 设置专业配色方案
    colors = [0.2314, 0.4980, 0.7373;    % 专业蓝
              0.8627, 0.3725, 0.0078;    % 专业橙
              0.1725, 0.6275, 0.1725];   % 专业绿
    
    for i = 1:length(h)
        h(i).FaceColor = colors(i, :);
        h(i).EdgeColor = [0.2, 0.2, 0.2];
        h(i).LineWidth = 1.2;
    end
    
    % 添加误差棒
    hold on;
    for i = 1:size(bar_data, 1)
        for j = 1:size(bar_data, 2)
            x_pos = i + (j - 2) * 0.27;
            errorbar(x_pos, bar_data(i, j), energy_std(j, i), 'k', ...
                    'LineWidth', 1.5, 'CapSize', 6);
            
            % 添加数值标签
            text(x_pos, bar_data(i, j) + energy_std(j, i) + 0.15, ...
                sprintf('%.2f', bar_data(i, j)), ...
                'HorizontalAlignment', 'center', 'FontSize', 10, ...
                'FontWeight', 'bold', 'Color', [0.2, 0.2, 0.2]);
        end
    end
    
    % 设置图表属性
    xlabel(labels.xlabel, 'FontSize', 14, 'FontWeight', 'bold');
    ylabel(labels.ylabel, 'FontSize', 14, 'FontWeight', 'bold');
    title(labels.title_main, 'FontSize', 16, 'FontWeight', 'bold');
    
    set(gca, 'XTickLabel', labels.scenarios, 'FontSize', 12);
    set(gca, 'YGrid', 'on', 'GridAlpha', 0.3);
    set(gca, 'Box', 'on', 'LineWidth', 1);
    
    % 设置图例
    legend(labels.algorithms, 'Location', 'best', 'FontSize', 12, ...
           'Box', 'on', 'EdgeColor', [0.5, 0.5, 0.5]);
    
    % 保存高质量图片
    print(fig1, 'publication_energy_comparison.png', '-dpng', '-r300');
    print(fig1, 'publication_energy_comparison.eps', '-depsc', '-r300');
    saveas(fig1, 'publication_energy_comparison.fig');
    
    % 图2: 热力图
    fig2 = figure('Position', [200, 200, 800, 600]);
    set(fig2, 'Color', 'white');
    
    % 创建热力图
    imagesc(energy_results);
    
    % 设置颜色映射
    colormap(flipud(hot));
    c = colorbar;
    c.Label.String = labels.ylabel;
    c.Label.FontSize = 12;
    c.Label.FontWeight = 'bold';
    
    % 设置坐标轴
    set(gca, 'XTick', 1:length(labels.scenarios));
    set(gca, 'XTickLabel', labels.scenarios, 'FontSize', 11);
    set(gca, 'YTick', 1:length(labels.algorithms));
    set(gca, 'YTickLabel', labels.algorithms, 'FontSize', 11);
    
    % 添加数值标签
    for i = 1:size(energy_results, 1)
        for j = 1:size(energy_results, 2)
            text(j, i, sprintf('%.2f', energy_results(i, j)), ...
                'HorizontalAlignment', 'center', 'Color', 'white', ...
                'FontSize', 12, 'FontWeight', 'bold');
        end
    end
    
    title(labels.heatmap_title, 'FontSize', 16, 'FontWeight', 'bold');
    xlabel(labels.xlabel, 'FontSize', 14, 'FontWeight', 'bold');
    ylabel(labels.legend_title, 'FontSize', 14, 'FontWeight', 'bold');
    
    % 保存热力图
    print(fig2, 'publication_energy_heatmap.png', '-dpng', '-r300');
    print(fig2, 'publication_energy_heatmap.eps', '-depsc', '-r300');
    saveas(fig2, 'publication_energy_heatmap.fig');
    
    % 图3: 综合分析图
    fig3 = figure('Position', [300, 300, 1400, 900]);
    set(fig3, 'Color', 'white');
    
    % 子图1: 能耗对比
    subplot(2, 3, 1);
    bar(bar_data, 'grouped', 'LineWidth', 1);
    xlabel(labels.xlabel, 'FontSize', 11);
    ylabel(labels.ylabel, 'FontSize', 11);
    title(labels.subtitles{1}, 'FontSize', 12, 'FontWeight', 'bold');
    set(gca, 'XTickLabel', labels.scenarios);
    legend(labels.algorithms, 'Location', 'best', 'FontSize', 9);
    grid on; grid minor;
    
    % 子图2: 相对性能
    subplot(2, 3, 2);
    baseline_idx = 1; % 以第一个算法为基准
    relative_perf = zeros(size(bar_data));
    for i = 1:size(bar_data, 1)
        baseline = bar_data(i, baseline_idx);
        relative_perf(i, :) = (bar_data(i, :) - baseline) / baseline * 100;
    end
    bar(relative_perf, 'grouped', 'LineWidth', 1);
    xlabel(labels.xlabel, 'FontSize', 11);
    ylabel(labels.ylabel_percent, 'FontSize', 11);
    title(labels.subtitles{2}, 'FontSize', 12, 'FontWeight', 'bold');
    set(gca, 'XTickLabel', labels.scenarios);
    legend(labels.algorithms, 'Location', 'best', 'FontSize', 9);
    grid on; yline(0, 'r--', 'LineWidth', 2);
    
    % 子图3: 算法稳定性
    subplot(2, 3, 3);
    avg_std = mean(energy_std, 2);
    bar(avg_std, 'FaceColor', [0.6, 0.4, 0.8], 'LineWidth', 1);
    xlabel(labels.legend_title, 'FontSize', 11);
    ylabel(labels.ylabel_std, 'FontSize', 11);
    title(labels.subtitles{3}, 'FontSize', 12, 'FontWeight', 'bold');
    set(gca, 'XTickLabel', labels.algorithms);
    grid on;
    
    % 子图4: 场景复杂度
    subplot(2, 3, 4);
    scenario_range = max(energy_results) - min(energy_results);
    bar(scenario_range, 'FaceColor', [0.8, 0.6, 0.2], 'LineWidth', 1);
    xlabel(labels.xlabel, 'FontSize', 11);
    ylabel(labels.ylabel_diff, 'FontSize', 11);
    title(labels.subtitles{4}, 'FontSize', 12, 'FontWeight', 'bold');
    set(gca, 'XTickLabel', labels.scenarios);
    grid on;
    
    % 子图5: 算法排名
    subplot(2, 3, 5);
    avg_energy = mean(energy_results, 2);
    [sorted_energy, sort_idx] = sort(avg_energy);
    bar(sorted_energy, 'FaceColor', [0.4, 0.7, 0.6], 'LineWidth', 1);
    xlabel(labels.legend_title, 'FontSize', 11);
    ylabel(labels.ylabel, 'FontSize', 11);
    title(labels.subtitles{5}, 'FontSize', 12, 'FontWeight', 'bold');
    set(gca, 'XTickLabel', labels.algorithms(sort_idx));
    grid on;
    
    % 子图6: 改进潜力
    subplot(2, 3, 6);
    improvement_potential = (max(energy_results) - min(energy_results)) ./ max(energy_results) * 100;
    bar(improvement_potential, 'FaceColor', [0.9, 0.5, 0.3], 'LineWidth', 1);
    xlabel(labels.xlabel, 'FontSize', 11);
    ylabel(labels.ylabel_potential, 'FontSize', 11);
    title(labels.subtitles{6}, 'FontSize', 12, 'FontWeight', 'bold');
    set(gca, 'XTickLabel', labels.scenarios);
    grid on;
    
    % 总标题
    sgtitle(labels.title_main, 'FontSize', 18, 'FontWeight', 'bold');
    
    % 保存综合分析图
    print(fig3, 'publication_comprehensive_analysis.png', '-dpng', '-r300');
    print(fig3, 'publication_comprehensive_analysis.eps', '-depsc', '-r300');
    saveas(fig3, 'publication_comprehensive_analysis.fig');
    
    fprintf('✓ 发表级质量图表已生成:\n');
    fprintf('  - publication_energy_comparison.png/eps/fig\n');
    fprintf('  - publication_energy_heatmap.png/eps/fig\n');
    fprintf('  - publication_comprehensive_analysis.png/eps/fig\n');
    fprintf('  (包含PNG、EPS和FIG格式，适合不同发表需求)\n');
end
