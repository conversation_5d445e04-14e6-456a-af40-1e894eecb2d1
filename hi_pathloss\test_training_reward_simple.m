% 简化测试版本 - 验证训练奖励对比功能

function test_training_reward_simple()
    close all;
    clear;
    clc;
    
    fprintf('=== 测试训练奖励对比功能 ===\n');
    
    try
        % 设置中文字体
        try
            set(0, 'DefaultAxesFontName', 'SimHei');
            set(0, 'DefaultTextFontName', 'SimHei');
        catch
            fprintf('使用默认字体\n');
        end
        
        % 简化参数
        num_episodes = 50;  % 减少到50轮进行快速测试
        scenario_names = {'静态监测', '动态转换', '周期性运动'};
        algorithm_names = {'固定功率', 'DQN', '演员-评论家', '分层RL'};
        
        % 生成测试数据
        fprintf('生成测试数据...\n');
        all_rewards = generate_test_data(num_episodes);
        
        % 绘制图表
        fprintf('绘制训练奖励图表...\n');
        plot_simple_training_rewards(all_rewards, scenario_names, algorithm_names, num_episodes);
        
        fprintf('测试完成！\n');
        
    catch ME
        fprintf('测试过程中出现错误:\n');
        fprintf('错误信息: %s\n', ME.message);
        if ~isempty(ME.stack)
            fprintf('错误位置: %s (第 %d 行)\n', ME.stack(1).name, ME.stack(1).line);
        end
    end
end

function all_rewards = generate_test_data(num_episodes)
    % 生成测试用的奖励数据
    all_rewards = cell(4, 3);
    
    for scenario_idx = 1:3
        for alg_idx = 1:4
            % 生成不同特征的奖励曲线
            base_reward = 100 + scenario_idx * 20 + alg_idx * 10;
            
            % 添加学习趋势
            learning_trend = linspace(0, base_reward * 0.3, num_episodes);
            
            % 添加随机噪声
            noise = randn(1, num_episodes) * base_reward * 0.1;
            
            % 组合奖励
            rewards = base_reward + learning_trend + noise;
            
            % 确保分层RL表现最好
            if alg_idx == 4
                rewards = rewards + 50;
            end
            
            all_rewards{alg_idx, scenario_idx} = rewards;
        end
    end
end

function plot_simple_training_rewards(all_rewards, scenario_names, algorithm_names, num_episodes)
    % 简化的绘图函数
    
    colors = [0.2, 0.6, 0.8;    % 固定功率 - 蓝色
              0.8, 0.4, 0.2;    % DQN - 橙色
              0.9, 0.6, 0.1;    % 演员-评论家 - 黄色
              0.2, 0.8, 0.4];   % 分层RL - 绿色
    
    figure('Position', [100, 100, 1200, 800]);
    
    % 绘制每个场景
    for scenario_idx = 1:3
        subplot(2, 2, scenario_idx);
        hold on;
        
        for alg_idx = 1:4
            rewards = all_rewards{alg_idx, scenario_idx};
            
            % 简单移动平均
            window_size = 5;
            smoothed_rewards = simple_moving_average(rewards, window_size);
            
            plot(1:num_episodes, smoothed_rewards, 'Color', colors(alg_idx, :), ...
                'LineWidth', 2, 'DisplayName', algorithm_names{alg_idx});
        end
        
        xlabel('训练轮数');
        ylabel('累计奖励');
        title(sprintf('%s - 训练奖励', scenario_names{scenario_idx}));
        legend('Location', 'southeast');
        grid on;
        xlim([1, num_episodes]);
        
        hold off;
    end
    
    % 综合对比
    subplot(2, 2, 4);
    hold on;
    
    % 计算平均奖励
    for alg_idx = 1:4
        avg_rewards = zeros(1, num_episodes);
        for scenario_idx = 1:3
            avg_rewards = avg_rewards + all_rewards{alg_idx, scenario_idx};
        end
        avg_rewards = avg_rewards / 3;
        
        smoothed_avg = simple_moving_average(avg_rewards, 5);
        
        plot(1:num_episodes, smoothed_avg, 'Color', colors(alg_idx, :), ...
            'LineWidth', 2.5, 'DisplayName', algorithm_names{alg_idx});
    end
    
    xlabel('训练轮数');
    ylabel('平均累计奖励');
    title('所有场景平均 - 训练奖励对比');
    legend('Location', 'southeast');
    grid on;
    xlim([1, num_episodes]);
    
    hold off;
    
    sgtitle('四种算法训练过程奖励对比分析', 'FontSize', 14, 'FontWeight', 'bold');
    
    % 保存图表
    try
        saveas(gcf, 'test_training_reward_comparison.png');
        fprintf('图表已保存为: test_training_reward_comparison.png\n');
    catch
        fprintf('保存图表时出现问题\n');
    end
end

function smoothed = simple_moving_average(data, window_size)
    % 简单移动平均函数
    n = length(data);
    smoothed = zeros(size(data));
    
    for i = 1:n
        start_idx = max(1, i - floor(window_size/2));
        end_idx = min(n, i + floor(window_size/2));
        smoothed(i) = mean(data(start_idx:end_idx));
    end
end
