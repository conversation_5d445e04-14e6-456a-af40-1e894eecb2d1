//
// Generated file, do not edit! Created by nedtool 4.6 from src/node/communication/mac/tMac/TMacPacket.msg.
//

#ifndef _TMACPACKET_M_H_
#define _TMACPACKET_M_H_

#include <omnetpp.h>

// nedtool version check
#define MSGC_VERSION 0x0406
#if (MSGC_VERSION!=OMNETPP_VERSION)
#    error Version mismatch! Probably this file was generated by an earlier version of nedtool: 'make clean' should help.
#endif



// cplusplus {{
#include "MacPacket_m.h"
// }}

/**
 * Enum generated from <tt>src/node/communication/mac/tMac/TMacPacket.msg:19</tt> by nedtool.
 * <pre>
 * enum TmacPacket_type
 * {
 * 
 *     SYNC_TMAC_PACKET = 1;
 *     RTS_TMAC_PACKET = 2;
 *     CTS_TMAC_PACKET = 3;
 *     DS_TMAC_PACKET = 4;
 *     FRTS_TMAC_PACKET = 5;
 *     DATA_TMAC_PACKET = 6;
 *     ACK_TMAC_PACKET = 7;
 * }
 * </pre>
 */
enum TmacPacket_type {
    SYNC_TMAC_PACKET = 1,
    RTS_TMAC_PACKET = 2,
    CTS_TMAC_PACKET = 3,
    DS_TMAC_PACKET = 4,
    FRTS_TMAC_PACKET = 5,
    DATA_TMAC_PACKET = 6,
    ACK_TMAC_PACKET = 7
};

/**
 * Class generated from <tt>src/node/communication/mac/tMac/TMacPacket.msg:29</tt> by nedtool.
 * <pre>
 * packet TMacPacket extends MacPacket
 * {
 *     // This a basic field, essential for any packet
 *     // size including source and destination field (found 
 *     // in the generic MacPAcket) is 9 bytes in total
 *     int type @enum(TmacPacket_type);	// 1 byte
 * 
 *     // RTS and CTS frames also contain nav field, bringing their size to 13 bytes
 *     simtime_t nav = 0;					// 4 bytes
 * 
 * 	// Sequence number is essential for ACK and DATA frames, but they do not 
 * 	// contain NAV field, therefore the size of ACK packet and MAC 
 * 	// layer overhead in general is 11 bytes. We use the field in the
 * 	// generic MacPacket and count it as 2 bytes.
 * 
 *     // SYNC packet has only three fields: sequence number, sync value and sync ID, 
 *     // making its total size only 11 bytes (1 extra byte comes from packet type) 
 *     // In reality, SYNC id would probably be stored in source field and sync 
 *     // value in NAV field. But in this model we do not create a separate packet 
 *     // subclass for each packet type
 *     simtime_t sync = 0;					// 4 bytes
 *     int syncId = 0;						// 4 bytes
 * }
 * </pre>
 */
class TMacPacket : public ::MacPacket
{
  protected:
    int type_var;
    simtime_t nav_var;
    simtime_t sync_var;
    int syncId_var;

  private:
    void copy(const TMacPacket& other);

  protected:
    // protected and unimplemented operator==(), to prevent accidental usage
    bool operator==(const TMacPacket&);

  public:
    TMacPacket(const char *name=NULL, int kind=0);
    TMacPacket(const TMacPacket& other);
    virtual ~TMacPacket();
    TMacPacket& operator=(const TMacPacket& other);
    virtual TMacPacket *dup() const {return new TMacPacket(*this);}
    virtual void parsimPack(cCommBuffer *b);
    virtual void parsimUnpack(cCommBuffer *b);

    // field getter/setter methods
    virtual int getType() const;
    virtual void setType(int type);
    virtual simtime_t getNav() const;
    virtual void setNav(simtime_t nav);
    virtual simtime_t getSync() const;
    virtual void setSync(simtime_t sync);
    virtual int getSyncId() const;
    virtual void setSyncId(int syncId);
};

inline void doPacking(cCommBuffer *b, TMacPacket& obj) {obj.parsimPack(b);}
inline void doUnpacking(cCommBuffer *b, TMacPacket& obj) {obj.parsimUnpack(b);}


#endif // ifndef _TMACPACKET_M_H_

