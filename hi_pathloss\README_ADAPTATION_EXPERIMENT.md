# 分层强化学习算法适应性验证实验

## 实验概述

本实验旨在验证分层强化学习算法在不同人体运动场景下的适应性，通过对比分析算法在静态监测、动态转换和周期性运动三种场景下的性能表现，评估算法的鲁棒性和实用性。

## 实验场景

### 场景1：静态监测场景 (13_04.bvh)
- **运动类型**：坐在踏脚凳上，手托下巴
- **特点**：相对静止，小幅动作
- **预期行为**：低功率稳定运行
- **验证重点**：低动态环境下的能效优化

### 场景2：动态转换场景 (13_01.bvh)
- **运动类型**：从坐姿到站立的转换
- **特点**：急剧的运动变化
- **预期行为**：快速响应运动变化
- **验证重点**：突发运动变化的适应能力

### 场景3：周期性运动场景 (35_01.bvh)
- **运动类型**：规律行走运动
- **特点**：周期性的运动模式
- **预期行为**：预测性功率调整
- **验证重点**：规律性运动的学习和预测

## 文件结构

```
hi_pathloss/
├── adaptation_experiment_main.m              # 主实验程序
├── adaptation_training_module.m              # 训练模块
├── adaptation_evaluation_module.m            # 评估模块
├── adaptation_baseline_module.m              # 基线算法对比模块
├── adaptation_analysis_module.m              # 跨场景分析模块
├── adaptation_visualization_module.m         # 可视化模块
├── adaptation_environment_interface.m        # 环境接口
├── run_adaptation_experiment.m               # 完整实验启动脚本
├── demo_adaptation_experiment.m              # 演示实验脚本
└── README_ADAPTATION_EXPERIMENT.md           # 本说明文件
```

## 快速开始

### 方法1：运行演示实验（推荐）

```matlab
% 在MATLAB中运行演示实验
demo_adaptation_experiment
```

演示实验特点：
- 简化的实验流程，运行时间短（约2-3分钟）
- 使用模拟数据，不依赖BVH文件
- 生成基本的对比图表和报告
- 验证实验框架的正确性

### 方法2：运行完整实验

```matlab
% 在MATLAB中运行完整实验
run_adaptation_experiment
```

完整实验特点：
- 完整的实验流程，运行时间较长（约10-15分钟）
- 尝试加载真实BVH文件，失败时使用默认数据
- 生成详细的分析报告和可视化结果
- 包含完整的基线算法对比

## 实验输出

### 生成的文件

实验完成后，会在 `adaptation_experiment_results/` 目录下生成以下文件：

#### 图片文件
- `demo_performance_comparison.png` - 性能对比图
- `demo_training_convergence.png` - 训练收敛对比图
- `training_convergence_comparison.png` - 训练过程对比
- `performance_radar_comparison.png` - 性能雷达图
- `adaptation_timeseries_comparison.png` - 适应性时间序列图
- `baseline_algorithm_comparison.png` - 基线算法对比图
- `response_speed_comparison.png` - 响应速度对比图

#### 报告文件
- `demo_experiment_report.txt` - 演示实验报告
- `adaptation_experiment_report.txt` - 完整实验报告

#### 数据文件
- `scenario_*/` - 各场景的详细实验数据
- `*.mat` - MATLAB数据文件

### 结果解读

#### 性能指标
- **能耗 (mJ)**：越低越好，表示算法的节能效果
- **PDR**：包传输率，越高越好，表示通信可靠性
- **延迟 (ms)**：越低越好，表示通信实时性
- **响应时间 (ms)**：算法对环境变化的响应速度

#### 适应性指标
- **响应速度**：算法对运动变化的反应快慢
- **适应准确性**：功率选择与理想策略的匹配度
- **学习效率**：训练收敛速度和稳定性
- **场景特定指标**：针对不同场景的专门评估

## 实验验证要点

### 1. 静态场景验证
- 算法应选择较低功率等级
- 能耗应为三个场景中最低
- 功率选择应保持稳定

### 2. 动态场景验证
- 算法应快速响应运动变化
- 在运动转换时功率应相应调整
- 响应时间应较短

### 3. 周期性场景验证
- 算法应学会识别运动周期
- 功率选择应呈现周期性模式
- 预测准确性应较高

### 4. 跨场景对比验证
- 分层RL应优于基线算法
- 不同场景下性能差异应符合预期
- 算法应展现良好的鲁棒性

## 自定义实验

### 修改场景参数

在 `demo_adaptation_experiment.m` 中修改场景定义：

```matlab
scenarios = {
    struct('name', '自定义场景', 'file', 'custom.bvh', 'type', 'custom', ...
           'description', '自定义运动描述', 'expected_behavior', '预期行为'),
    % 添加更多场景...
};
```

### 调整算法参数

在训练模块中修改网络参数：

```matlab
agent.hidden_size = 64;        % 隐藏层大小
agent.learning_rate = 0.001;   % 学习率
agent.epsilon_decay = 0.995;   % 探索率衰减
```

### 修改评估指标

在评估模块中调整奖励权重：

```matlab
env.energy_weight = 0.5;   % 能耗权重
env.pdr_weight = 0.3;      % PDR权重
env.delay_weight = 0.2;    % 延迟权重
```

## 故障排除

### 常见问题

1. **BVH文件加载失败**
   - 解决方案：实验会自动使用默认数据，不影响实验进行

2. **内存不足**
   - 解决方案：减少训练轮数或最大步数

3. **图片无法保存**
   - 解决方案：检查目录权限，确保可以创建文件

4. **实验运行时间过长**
   - 解决方案：使用演示版本或减少实验参数

### 调试模式

在代码开头添加调试标志：

```matlab
DEBUG_MODE = true;
if DEBUG_MODE
    fprintf('调试信息: ...\n');
end
```

## 扩展实验

### 添加新的基线算法

在 `adaptation_baseline_module.m` 中添加新算法：

```matlab
case 'new_algorithm'
    agent = create_new_algorithm_agent(agent, env);
```

### 添加新的评估指标

在 `adaptation_evaluation_module.m` 中添加新指标：

```matlab
new_metric = calculate_new_metric(eval_result, scenario);
performance_results.new_metric = new_metric;
```

### 添加新的可视化

在 `adaptation_visualization_module.m` 中添加新图表：

```matlab
function generate_new_visualization(all_results, scenarios)
    % 新的可视化代码
end
```

## 实验意义

本适应性验证实验为无线体域网功率控制研究提供了重要的实验支撑：

1. **算法有效性验证**：证明分层RL算法在不同场景下的优越性
2. **鲁棒性评估**：验证算法对不同运动模式的适应能力
3. **实用性分析**：评估算法在实际应用中的可行性
4. **性能基准**：为后续算法改进提供对比基准

## 引用和参考

如果您在研究中使用了本实验框架，请引用相关论文和数据集。

## 联系信息

如有问题或建议，请联系实验设计者。
