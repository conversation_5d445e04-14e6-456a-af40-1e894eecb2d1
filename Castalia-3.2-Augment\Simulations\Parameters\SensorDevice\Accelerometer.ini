# ********************************************************************************
# *  Copyright: National ICT Australia,  2007 - 2010                             *
# *  Developed at the ATP lab, Networked Systems research theme                  *
# *  Author(s): <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>  *
# *  This file is distributed under the terms in the attached LICENSE file.      *
# *  If you do not find this file, copies can be found by writing to:            *
# *                                                                              *
# *      NICTA, Locked Bag 9013, Alexandria, NSW 1435, Australia                 *
# *      Attention:  License Inquiry.                                            *
# *                                                                              *
# *******************************************************************************/


# ===========================================================
# This file changes some parameters of sensor module to create an 
# abstraction of accelerometer sensor
# ===========================================================

SN.node[*].SensorManager.pwrConsumptionPerDevice = "0.5"	# in mW
SN.node[*].SensorManager.sensorTypes = "Acceleration"
SN.node[*].SensorManager.maxSampleRates = "10"		# 10 samples per sec
