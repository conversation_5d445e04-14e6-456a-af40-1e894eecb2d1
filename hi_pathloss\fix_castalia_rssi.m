%% 修复Castalia仿真中RSSI为0的问题
% 这个脚本分析并修复Castalia仿真配置，确保RSSI值正确记录

fprintf('=== 诊断和修复Castalia RSSI问题 ===\n');
    
    %% 1. 检查当前配置
    fprintf('\n1. 检查当前配置文件...\n');
    check_current_config();
    
    %% 2. 分析问题原因
    fprintf('\n2. 分析RSSI为0的可能原因...\n');
    analyze_rssi_issues();
    
    %% 3. 提供修复方案
    fprintf('\n3. 提供修复方案...\n');
    provide_solutions();
    
    %% 4. 创建修复后的配置文件
    fprintf('\n4. 创建修复后的配置文件...\n');
    create_fixed_config();
    
fprintf('\n=== 修复完成 ===\n');

function check_current_config()
    config_file = '../Castalia-3.2-Augment/Simulations/HIchan/omnetpp.ini';
    
    if exist(config_file, 'file')
        fprintf('✓ 找到配置文件: %s\n', config_file);
        
        % 读取配置文件
        fid = fopen(config_file, 'r');
        content = {};
        while ~feof(fid)
            line = fgetl(fid);
            if ischar(line)
                content{end+1} = line;
            end
        end
        fclose(fid);
        
        % 检查关键配置
        fprintf('检查关键配置项:\n');
        
        % 检查RSSI相关配置
        rssi_config_found = false;
        trace_enabled = false;
        
        for i = 1:length(content)
            line = content{i};
            if contains(line, 'symbolsForRSSI')
                fprintf('  ✓ symbolsForRSSI: %s\n', strtrim(line));
                rssi_config_found = true;
            elseif contains(line, 'collectTraceInfo')
                fprintf('  ✓ collectTraceInfo: %s\n', strtrim(line));
                if contains(line, 'true')
                    trace_enabled = true;
                end
            elseif contains(line, 'TxOutputPower')
                fprintf('  ✓ TxOutputPower: %s\n', strtrim(line));
            elseif contains(line, 'pathLossFile')
                fprintf('  ✓ pathLossFile: %s\n', strtrim(line));
            end
        end
        
        if ~rssi_config_found
            fprintf('  ❌ 未找到symbolsForRSSI配置\n');
        end
        
        if ~trace_enabled
            fprintf('  ❌ Radio层trace未启用\n');
        end
        
    else
        fprintf('❌ 配置文件不存在: %s\n', config_file);
    end
end

function analyze_rssi_issues()
    fprintf('RSSI为0的可能原因分析:\n');
    
    fprintf('1. ❌ RSSI积分时间问题:\n');
    fprintf('   - symbolsForRSSI设置可能不当\n');
    fprintf('   - rssiIntegrationTime计算错误\n');
    fprintf('   - 接收时间不足导致CS_NOT_VALID_YET\n\n');
    
    fprintf('2. ❌ 信号功率历史为空:\n');
    fprintf('   - totalPowerReceived列表为空\n');
    fprintf('   - 没有接收到有效信号\n');
    fprintf('   - 信号功率计算错误\n\n');
    
    fprintf('3. ❌ 无线信道配置问题:\n');
    fprintf('   - 路径损耗文件格式错误\n');
    fprintf('   - 发射功率设置问题\n');
    fprintf('   - 节点位置配置错误\n\n');
    
    fprintf('4. ❌ Radio状态问题:\n');
    fprintf('   - Radio不在RX状态\n');
    fprintf('   - 状态转换时机问题\n');
    fprintf('   - 初始化问题\n\n');
end

function provide_solutions()
    fprintf('修复方案:\n');
    
    fprintf('方案1: 修改RSSI配置参数\n');
    fprintf('  - 增加symbolsForRSSI值 (建议: 32或64)\n');
    fprintf('  - 启用carrierSenseInterruptEnabled\n');
    fprintf('  - 调整CCAthreshold\n\n');
    
    fprintf('方案2: 启用详细调试信息\n');
    fprintf('  - 设置Radio.collectTraceInfo = true\n');
    fprintf('  - 设置WirelessChannel.collectTraceInfo = true\n');
    fprintf('  - 启用record-eventlog\n\n');
    
    fprintf('方案3: 修改应用层RSSI记录\n');
    fprintf('  - 在应用层直接调用radioModule->readRSSI()\n');
    fprintf('  - 使用定时器定期记录RSSI\n');
    fprintf('  - 绕过网络层传递\n\n');
    
    fprintf('方案4: 使用模拟RSSI数据\n');
    fprintf('  - 基于路径损耗文件计算RSSI\n');
    fprintf('  - 添加真实的信道效应\n');
    fprintf('  - 生成科学的RSSI时间序列\n\n');
end

function create_fixed_config()
    fprintf('创建修复后的配置文件...\n');
    
    % 创建新的配置文件
    config_content = {
        '# 修复后的HIchan配置文件 - 解决RSSI为0问题',
        '[General]',
        '',
        'include ../Parameters/Castalia.ini',
        '',
        '**.result-recording-modes = all',
        'output-vector-file = ${resultdir}/${configname}-${runnumber}.vec',
        'output-scalar-file = ${resultdir}/${configname}-${runnumber}.sca',
        '',
        'sim-time-limit = 120s',
        'record-eventlog = true',
        '',
        'SN.numNodes = 2',
        '',
        '# 无线信道配置',
        'SN.wirelessChannel.pathLossFile = "../Parameters/WirelessChannel/BANmodels/13_04_pl.txt"',
        'SN.wirelessChannel.collectTraceInfo = true  # 启用调试',
        'SN.wirelessChannel.sigma = 4.0  # 阴影衰落标准差',
        '',
        '# Radio配置 - 修复RSSI问题',
        'SN.node[*].Communication.Radio.RadioParametersFile = "../Parameters/Radio/nRF51822.txt"',
        'SN.node[*].Communication.Radio.symbolsForRSSI = 64  # 增加RSSI积分符号数',
        'SN.node[*].Communication.Radio.TxOutputPower = "0dBm"  # 适中的发射功率',
        'SN.node[*].Communication.Radio.collectTraceInfo = true  # 启用Radio调试',
        'SN.node[*].Communication.Radio.carrierSenseInterruptEnabled = true  # 启用载波侦听',
        'SN.node[*].Communication.Radio.CCAthreshold = -85  # 设置CCA阈值',
        '',
        '# MAC和应用层配置',
        'SN.node[*].Communication.MAC.collectTraceInfo = true',
        'SN.node[*].Application.collectTraceInfo = true',
        '',
        '# 应用程序配置',
        'SN.node[*].ApplicationName = "ThroughputTestWalking"',
        'SN.node[0].Application.txFile = ""',
        'SN.node[*].Application.txFile = "../Parameters/WirelessChannel/BANmodels/13_04_pl_tx.txt"',
        'SN.node[0].Application.nextRecipient = "0"',
        'SN.node[1].Application.packet_rate = 5  # 降低发包率以便观察',
        'SN.node[1].Application.nextRecipient = "0"',
        '',
        '# 节点位置配置 - 确保合理距离',
        'SN.node[0].xCoor = 0',
        'SN.node[0].yCoor = 0',
        'SN.node[0].zCoor = 0',
        'SN.node[1].xCoor = 1  # 1米距离',
        'SN.node[1].yCoor = 0',
        'SN.node[1].zCoor = 0',
        '',
        '[Config TMAC]',
        'SN.node[*].Communication.MACProtocolName = "TMAC"',
        'SN.node[*].Communication.MAC.phyDataRate = 1024'
    };
    
    % 写入新配置文件
    new_config_file = '../Castalia-3.2-Augment/Simulations/HIchan/omnetpp_fixed.ini';
    fid = fopen(new_config_file, 'w');
    for i = 1:length(config_content)
        fprintf(fid, '%s\n', config_content{i});
    end
    fclose(fid);
    
    fprintf('✓ 创建修复配置文件: %s\n', new_config_file);
    
    % 创建测试脚本
    create_test_script();
end

function create_test_script()
    fprintf('创建RSSI测试脚本...\n');
    
    test_script = {
        '#!/bin/bash',
        '# RSSI测试脚本',
        'echo "=== 测试修复后的RSSI配置 ==="',
        '',
        'cd ../Castalia-3.2-Augment/Simulations/HIchan',
        '',
        'echo "1. 清理旧结果..."',
        'rm -f results/*.vec results/*.sca',
        '',
        'echo "2. 运行修复后的仿真..."',
        '../../bin/Castalia -c TMAC -f omnetpp_fixed.ini',
        '',
        'echo "3. 检查RSSI结果..."',
        'if [ -f "results/TMAC-0.vec" ]; then',
        '    echo "✓ 生成了.vec文件"',
        '    echo "RSSI数据统计:"',
        '    grep "^0" results/TMAC-0.vec | head -10',
        'else',
        '    echo "❌ 未生成.vec文件"',
        'fi',
        '',
        'echo "=== 测试完成 ==="'
    };
    
    test_file = '../Castalia-3.2-Augment/Simulations/HIchan/test_rssi.sh';
    fid = fopen(test_file, 'w');
    for i = 1:length(test_script)
        fprintf(fid, '%s\n', test_script{i});
    end
    fclose(fid);
    
    fprintf('✓ 创建测试脚本: %s\n', test_file);
    
    % 创建MATLAB验证脚本
    create_matlab_verification();
end

function create_matlab_verification()
    fprintf('创建MATLAB验证脚本...\n');
    
    verification_content = {
        '%% 验证修复后的RSSI数据',
        'function verify_fixed_rssi()',
        '    fprintf(''=== 验证修复后的RSSI数据 ===\\n'');',
        '    ',
        '    vec_file = ''../Castalia-3.2-Augment/Simulations/HIchan/results/TMAC-0.vec'';',
        '    ',
        '    if exist(vec_file, ''file'')',
        '        fprintf(''✓ 找到.vec文件\\n'');',
        '        ',
        '        % 读取数据',
        '        fid = fopen(vec_file, ''r'');',
        '        rssi_data = [];',
        '        time_data = [];',
        '        ',
        '        while ~feof(fid)',
        '            line = fgetl(fid);',
        '            if ischar(line) && ~isempty(line)',
        '                if ~startsWith(line, ''vector'') && ~startsWith(line, ''version'') && ...',
        '                   ~startsWith(line, ''attr'') && ~startsWith(line, ''run'') && ~startsWith(line, ''file'')',
        '                    parts = strsplit(strtrim(line));',
        '                    if length(parts) == 4',
        '                        time_val = str2double(parts{3});',
        '                        rssi_val = str2double(parts{4});',
        '                        time_data = [time_data; time_val];',
        '                        rssi_data = [rssi_data; rssi_val];',
        '                    end',
        '                end',
        '            end',
        '        end',
        '        fclose(fid);',
        '        ',
        '        % 分析结果',
        '        fprintf(''数据点数量: %d\\n'', length(rssi_data));',
        '        fprintf(''RSSI范围: %.3f 到 %.3f\\n'', min(rssi_data), max(rssi_data));',
        '        fprintf(''非零RSSI数量: %d\\n'', sum(rssi_data ~= 0));',
        '        ',
        '        if all(rssi_data == 0)',
        '            fprintf(''❌ RSSI仍然全为0，需要进一步调试\\n'');',
        '        else',
        '            fprintf(''✓ RSSI修复成功！\\n'');',
        '            ',
        '            % 绘制结果',
        '            figure;',
        '            plot(time_data, rssi_data, ''b-'', ''LineWidth'', 1.5);',
        '            xlabel(''时间 (s)'');',
        '            ylabel(''RSSI (dBm)'');',
        '            title(''修复后的RSSI时间序列'');',
        '            grid on;',
        '        end',
        '    else',
        '        fprintf(''❌ 未找到.vec文件，请先运行仿真\\n'');',
        '    end',
        'end'
    };
    
    verify_file = 'verify_fixed_rssi.m';
    fid = fopen(verify_file, 'w');
    for i = 1:length(verification_content)
        fprintf(fid, '%s\n', verification_content{i});
    end
    fclose(fid);
    
    fprintf('✓ 创建验证脚本: %s\n', verify_file);
end

% 运行修复程序
fix_castalia_rssi();
