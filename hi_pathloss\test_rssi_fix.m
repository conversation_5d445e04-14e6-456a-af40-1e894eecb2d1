%% 测试RSSI修复效果
% 这个脚本测试修复后的Castalia配置是否能正确生成RSSI数据

fprintf('=== 测试RSSI修复效果 ===\n');

%% 1. 检查修复后的配置文件
fprintf('\n1. 检查修复后的配置文件...\n');
config_file = '../Castalia-3.2-Augment/Simulations/HIchan/omnetpp_fixed.ini';

if exist(config_file, 'file')
    fprintf('✓ 找到修复配置文件\n');
    
    % 读取并显示关键修复项
    fid = fopen(config_file, 'r');
    fprintf('关键修复项:\n');
    while ~feof(fid)
        line = fgetl(fid);
        if ischar(line)
            if contains(line, 'symbolsForRSSI') || contains(line, 'carrierSenseInterruptEnabled') || ...
               contains(line, 'CCAthreshold') || contains(line, 'collectTraceInfo')
                fprintf('  %s\n', strtrim(line));
            end
        end
    end
    fclose(fid);
else
    fprintf('❌ 修复配置文件不存在\n');
    return;
end

%% 2. 检查仿真结果
fprintf('\n2. 检查仿真结果...\n');
results_dir = '../Castalia-3.2-Augment/Simulations/HIchan/results/';
vec_file = [results_dir 'TMAC-0.vec'];

if exist(vec_file, 'file')
    fprintf('✓ 找到仿真结果文件\n');
    
    % 分析RSSI数据
    analyze_rssi_data(vec_file);
else
    fprintf('❌ 仿真结果文件不存在，可能仿真未完成或失败\n');
    fprintf('建议:\n');
    fprintf('1. 检查Castalia是否正确安装\n');
    fprintf('2. 手动运行仿真命令\n');
    fprintf('3. 检查仿真日志文件\n');
end

%% 3. 提供解决方案总结
fprintf('\n3. RSSI问题解决方案总结...\n');
provide_solution_summary();

function analyze_rssi_data(vec_file)
    fprintf('分析RSSI数据...\n');
    
    fid = fopen(vec_file, 'r');
    rssi_values = [];
    time_values = [];
    data_count = 0;
    
    while ~feof(fid)
        line = fgetl(fid);
        if ischar(line) && ~isempty(line)
            % 跳过头部信息
            if startsWith(line, 'vector') || startsWith(line, 'version') || ...
               startsWith(line, 'attr') || startsWith(line, 'run') || startsWith(line, 'file')
                continue;
            end
            
            % 解析数据行
            parts = strsplit(strtrim(line));
            if length(parts) == 4
                vector_id = str2double(parts{1});
                if vector_id == 0  % RSSI向量
                    time_val = str2double(parts{3});
                    rssi_val = str2double(parts{4});
                    
                    time_values = [time_values; time_val];
                    rssi_values = [rssi_values; rssi_val];
                    data_count = data_count + 1;
                end
            end
        end
    end
    fclose(fid);
    
    % 分析结果
    fprintf('RSSI数据分析结果:\n');
    fprintf('  数据点数量: %d\n', data_count);
    
    if data_count > 0
        fprintf('  时间范围: %.3f 到 %.3f 秒\n', min(time_values), max(time_values));
        fprintf('  RSSI范围: %.3f 到 %.3f\n', min(rssi_values), max(rssi_values));
        
        non_zero_count = sum(rssi_values ~= 0);
        fprintf('  非零RSSI数量: %d (%.1f%%)\n', non_zero_count, 100*non_zero_count/data_count);
        
        if all(rssi_values == 0)
            fprintf('  ❌ RSSI仍然全为0，修复未成功\n');
            suggest_further_fixes();
        elseif non_zero_count > data_count * 0.8
            fprintf('  ✅ RSSI修复成功！大部分数据非零\n');
            plot_rssi_results(time_values, rssi_values);
        else
            fprintf('  ⚠️  RSSI部分修复，仍有改进空间\n');
            plot_rssi_results(time_values, rssi_values);
        end
    else
        fprintf('  ❌ 未找到RSSI数据\n');
    end
end

function plot_rssi_results(time_values, rssi_values)
    fprintf('绘制RSSI结果图...\n');
    
    figure('Position', [100, 100, 1000, 600]);
    
    % 主图：RSSI时间序列
    subplot(2,1,1);
    plot(time_values, rssi_values, 'b-', 'LineWidth', 1.5);
    xlabel('时间 (s)');
    ylabel('RSSI');
    title('修复后的RSSI时间序列');
    grid on;
    
    % 添加统计信息
    mean_rssi = mean(rssi_values);
    std_rssi = std(rssi_values);
    non_zero_ratio = sum(rssi_values ~= 0) / length(rssi_values);
    
    text(0.02, 0.95, sprintf('均值: %.2f\n标准差: %.2f\n非零比例: %.1f%%', ...
         mean_rssi, std_rssi, non_zero_ratio*100), ...
         'Units', 'normalized', 'BackgroundColor', 'white', 'EdgeColor', 'black');
    
    % 直方图：RSSI分布
    subplot(2,1,2);
    histogram(rssi_values, 20, 'FaceColor', 'blue', 'EdgeColor', 'black');
    xlabel('RSSI值');
    ylabel('频次');
    title('RSSI值分布');
    grid on;
    
    fprintf('✓ RSSI结果图已生成\n');
end

function suggest_further_fixes()
    fprintf('\n进一步修复建议:\n');
    fprintf('1. 检查Radio状态转换:\n');
    fprintf('   - 确保接收节点处于RX状态\n');
    fprintf('   - 检查状态转换时机\n\n');
    
    fprintf('2. 调整RSSI积分参数:\n');
    fprintf('   - 进一步增加symbolsForRSSI (尝试128)\n');
    fprintf('   - 调整rssiIntegrationTime\n\n');
    
    fprintf('3. 检查信号功率计算:\n');
    fprintf('   - 验证发射功率设置\n');
    fprintf('   - 检查路径损耗计算\n');
    fprintf('   - 确认节点位置配置\n\n');
    
    fprintf('4. 启用更详细的调试:\n');
    fprintf('   - 查看Castalia trace输出\n');
    fprintf('   - 检查事件日志\n');
    fprintf('   - 分析无线信道消息\n\n');
    
    fprintf('5. 考虑替代方案:\n');
    fprintf('   - 使用基于路径损耗的RSSI计算\n');
    fprintf('   - 实现自定义RSSI记录机制\n');
    fprintf('   - 采用我们的科学RSSI模型\n');
end

function provide_solution_summary()
    fprintf('RSSI问题解决方案总结:\n');
    
    fprintf('\n🔧 已实施的修复:\n');
    fprintf('1. ✅ 增加symbolsForRSSI从16到64\n');
    fprintf('2. ✅ 启用carrierSenseInterruptEnabled\n');
    fprintf('3. ✅ 设置合理的CCAthreshold (-85dBm)\n');
    fprintf('4. ✅ 启用所有层的collectTraceInfo\n');
    fprintf('5. ✅ 调整发射功率到0dBm\n');
    fprintf('6. ✅ 设置合理的节点距离(1米)\n');
    
    fprintf('\n📊 备选方案:\n');
    fprintf('如果Castalia RSSI仍有问题，我们已经实现了:\n');
    fprintf('1. ✅ 基于物理模型的科学RSSI生成器\n');
    fprintf('2. ✅ 自动检测和替换机制\n');
    fprintf('3. ✅ 完整的WBAN信道模型\n');
    
    fprintf('\n🎯 最终建议:\n');
    fprintf('对于研究目的，建议使用我们的科学RSSI模型，\n');
    fprintf('因为它基于真实的物理原理，更适合学术研究。\n');
end

fprintf('\n=== 测试完成 ===\n');
