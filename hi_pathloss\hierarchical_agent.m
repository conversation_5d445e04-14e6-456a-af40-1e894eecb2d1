% 分层强化学习智能体
% 实现上层策略规划和下层精细调节的分层架构
classdef hierarchical_agent < handle
    
    properties
        % 网络参数
        state_dim = 20;  % 优化后的状态维度
        action_dim = 6;

        % 改进的分层架构设计
        % 上层输出：目标导向的高级策略指令
        meta_action_dim = 8; % 扩展为更丰富的策略表示
        % [能耗优先级, QoS优先级, 适应性级别, 探索倾向,
        %  低功率偏好, 中功率偏好, 高功率偏好, 动态调整因子]
        
        % 上层智能体 (Meta-Agent)
        meta_q_network;
        meta_target_network;
        meta_memory;
        meta_epsilon = 0.7;         % 降低初始探索率
        meta_epsilon_decay = 0.99;  % 更快衰减
        meta_epsilon_min = 0.05;    % 提高最小探索率
        meta_learning_rate = 0.01;  % 提高学习率

        % 下层智能体 (Local-Agent)
        local_q_network;
        local_target_network;
        local_memory;
        local_epsilon = 0.7;        % 降低初始探索率
        local_epsilon_decay = 0.99; % 更快衰减
        local_epsilon_min = 0.05;   % 提高最小探索率
        local_learning_rate = 0.01; % 提高学习率
        
        % 改进的经验回放参数
        memory_size = 20000;     % 增加内存容量
        batch_size = 64;         % 增加批次大小
        target_update_freq = 50; % 更频繁的目标网络更新

        % 优先经验回放参数
        priority_alpha = 0.6;    % 优先级指数
        priority_beta = 0.4;     % 重要性采样指数
        priority_epsilon = 1e-6; % 避免零优先级

        % 多步学习参数
        n_step = 3;              % 3步TD学习
        
        % 训练参数
        gamma = 0.9; % 折扣因子 (降低，更关注即时奖励)
        update_counter = 0;

        % 性能跟踪
        current_step = 0;
        recent_avg_energy = 50; % 最近平均能耗
        
        % 性能统计
        episode_rewards = [];
        episode_losses = [];
        meta_action_history = [];
        local_action_history = [];
    end
    
    methods
        function obj = hierarchical_agent()
            % 构造函数
            obj.initialize_networks();
            obj.initialize_memory();
        end
        
        function initialize_networks(obj)
            % 初始化神经网络
            fprintf('初始化分层神经网络...\n');
            
            % 上层网络 (策略规划)
            obj.meta_q_network = obj.create_meta_network();
            obj.meta_target_network = obj.create_meta_network();
            
            % 下层网络 (动作选择)
            obj.local_q_network = obj.create_local_network();
            obj.local_target_network = obj.create_local_network();
            
            fprintf('网络初始化完成\n');
        end
        
        function network = create_meta_network(obj)
            % 创建改进的上层网络 (状态 -> 策略指令)
            network = struct();

            % 改进的网络结构: 33 -> 128 -> 64 -> 32 -> 8
            % 增加网络深度和宽度以提高表达能力
            hidden1_size = 128;
            hidden2_size = 64;
            hidden3_size = 32;

            % 使用He初始化 (适合ReLU激活函数)
            network.W1 = randn(hidden1_size, obj.state_dim) * sqrt(2/obj.state_dim);
            network.b1 = zeros(hidden1_size, 1);

            network.W2 = randn(hidden2_size, hidden1_size) * sqrt(2/hidden1_size);
            network.b2 = zeros(hidden2_size, 1);

            network.W3 = randn(hidden3_size, hidden2_size) * sqrt(2/hidden2_size);
            network.b3 = zeros(hidden3_size, 1);

            network.W4 = randn(obj.meta_action_dim, hidden3_size) * sqrt(2/hidden3_size);
            network.b4 = zeros(obj.meta_action_dim, 1);

            % 添加批归一化参数
            network.bn1_gamma = ones(hidden1_size, 1);
            network.bn1_beta = zeros(hidden1_size, 1);
            network.bn2_gamma = ones(hidden2_size, 1);
            network.bn2_beta = zeros(hidden2_size, 1);
            network.bn3_gamma = ones(hidden3_size, 1);
            network.bn3_beta = zeros(hidden3_size, 1);
        end
        
        function network = create_local_network(obj)
            % 创建改进的下层网络 (状态+策略指令 -> Q值)
            network = struct();

            % 输入维度: 状态(33) + 策略指令(8) = 41
            input_dim = obj.state_dim + obj.meta_action_dim;
            hidden1_size = 256;  % 增加网络容量
            hidden2_size = 128;
            hidden3_size = 64;

            % 使用He初始化
            network.W1 = randn(hidden1_size, input_dim) * sqrt(2/input_dim);
            network.b1 = zeros(hidden1_size, 1);

            network.W2 = randn(hidden2_size, hidden1_size) * sqrt(2/hidden1_size);
            network.b2 = zeros(hidden2_size, 1);

            network.W3 = randn(hidden3_size, hidden2_size) * sqrt(2/hidden2_size);
            network.b3 = zeros(hidden3_size, 1);

            network.W4 = randn(obj.action_dim, hidden3_size) * sqrt(2/hidden3_size);
            network.b4 = zeros(obj.action_dim, 1);

            % 添加批归一化参数
            network.bn1_gamma = ones(hidden1_size, 1);
            network.bn1_beta = zeros(hidden1_size, 1);
            network.bn2_gamma = ones(hidden2_size, 1);
            network.bn2_beta = zeros(hidden2_size, 1);
            network.bn3_gamma = ones(hidden3_size, 1);
            network.bn3_beta = zeros(hidden3_size, 1);
        end
        
        function initialize_memory(obj)
            % 初始化经验回放缓冲区
            obj.meta_memory = [];
            obj.local_memory = [];
        end
        
        function meta_action = select_meta_action(obj, state)
            % 上层智能体选择高级策略指令 - 重新设计版
            if rand() < obj.meta_epsilon
                % 探索: 基于环境状态的智能策略生成
                % 分析当前环境状态
                energy_state = state(1);  % 能耗相关状态
                channel_state = state(5); % 信道质量状态
                qos_state = state(15);    % QoS需求状态

                % 动态策略生成
                if obj.current_step > 10 && obj.recent_avg_energy > 70
                    % 高能耗情况：强调节能
                    meta_action = [0.8; 0.2; 0.6; 0.3; 0.7; 0.2; 0.1; 0.5];
                elseif channel_state < 0.3
                    % 信道质量差：强调可靠性
                    meta_action = [0.4; 0.8; 0.7; 0.4; 0.3; 0.4; 0.3; 0.6];
                else
                    % 平衡策略
                    meta_action = [0.6; 0.5; 0.5; 0.4; 0.5; 0.3; 0.2; 0.4];
                end

                % 添加探索噪声
                noise = 0.1 * randn(obj.meta_action_dim, 1);
                meta_action = meta_action + noise;
                meta_action = max(0.1, min(1.0, meta_action)); % 限制范围[0.1, 1.0]
            else
                % 利用: 网络预测的策略指令
                raw_output = obj.forward_meta_network(obj.meta_q_network, state);

                % 将网络输出转换为策略指令
                meta_action = sigmoid(raw_output); % 使用sigmoid确保[0,1]范围

                % 添加少量探索噪声
                if rand() < 0.1
                    noise = 0.05 * randn(size(meta_action));
                    meta_action = meta_action + noise;
                    meta_action = max(0.1, min(1.0, meta_action));
                end
            end
        end
        
        function action = select_local_action(obj, state, meta_action)
            % 下层智能体选择具体动作 - 基于策略指令的智能选择
            combined_input = [state; meta_action];
            q_values = obj.forward_local_network(obj.local_q_network, combined_input);

            if rand() < obj.local_epsilon
                % 智能探索: 基于meta策略指令的引导探索
                energy_priority = meta_action(1);    % 能耗优先级
                qos_priority = meta_action(2);       % QoS优先级
                adaptiveness = meta_action(3);       % 适应性级别
                exploration_bias = meta_action(4);   % 探索倾向
                low_power_pref = meta_action(5);     % 低功率偏好
                mid_power_pref = meta_action(6);     % 中功率偏好
                high_power_pref = meta_action(7);    % 高功率偏好

                % 基于策略指令计算动作概率
                action_probs = zeros(6, 1);

                % 低功率动作 (1-3): -20, -15, -10 dBm
                action_probs(1:3) = low_power_pref * energy_priority;

                % 中功率动作 (4-5): -5, 0 dBm
                action_probs(4:5) = mid_power_pref * (0.5 * energy_priority + 0.5 * qos_priority);

                % 高功率动作 (6): 4 dBm
                action_probs(6) = high_power_pref * qos_priority;

                % 归一化概率
                action_probs = action_probs / sum(action_probs);

                % 添加探索随机性
                if rand() < exploration_bias
                    action_probs = 0.7 * action_probs + 0.3 * ones(6,1)/6;
                end

                % 根据概率选择动作
                cumsum_probs = cumsum(action_probs);
                action = find(cumsum_probs >= rand(), 1);
            else
                % 利用: Q值引导的动作选择，结合meta策略调整

                % 基于meta策略调整Q值
                adjusted_q = q_values;

                % 根据能耗优先级调整低功率动作的Q值
                energy_boost = meta_action(1) * 2.0;
                adjusted_q(1:3) = adjusted_q(1:3) + energy_boost;

                % 根据QoS优先级调整高功率动作的Q值
                qos_boost = meta_action(2) * 1.5;
                adjusted_q(5:6) = adjusted_q(5:6) + qos_boost;

                % 使用调整后的Q值选择动作
                temperature = max(0.05, obj.local_epsilon * meta_action(3)); % 适应性温度
                exp_q = exp(adjusted_q / temperature);
                probs = exp_q / sum(exp_q);

                % 根据概率选择动作
                cumsum_probs = cumsum(probs);
                action = find(cumsum_probs >= rand(), 1);

                if isempty(action)
                    action = 1; % 默认选择最低功率
                end
            end
        end
        
        function output = forward_meta_network(obj, network, input)
            % 改进的上层网络前向传播 (支持4层网络)
            % 第一层
            z1 = network.W1 * input + network.b1;
            a1 = relu(z1);

            % 第二层
            z2 = network.W2 * a1 + network.b2;
            a2 = relu(z2);

            % 第三层
            z3 = network.W3 * a2 + network.b3;
            a3 = relu(z3);

            % 输出层
            output = network.W4 * a3 + network.b4;
        end
        
        function output = forward_local_network(obj, network, input)
            % 改进的下层网络前向传播 (支持4层网络)
            % 第一层
            z1 = network.W1 * input + network.b1;
            a1 = relu(z1);

            % 第二层
            z2 = network.W2 * a1 + network.b2;
            a2 = relu(z2);

            % 第三层
            z3 = network.W3 * a2 + network.b3;
            a3 = relu(z3);

            % 输出层
            output = network.W4 * a3 + network.b4;
        end
        
        function store_meta_experience(obj, state, meta_action, reward, next_state, done)
            % 存储上层经验 - 支持优先经验回放

            % 计算TD误差作为优先级 (简化版)
            if ~isempty(obj.meta_memory)
                current_q = sum(obj.forward_meta_network(obj.meta_q_network, state) .* meta_action);
                if ~done
                    next_q = max(obj.forward_meta_network(obj.meta_target_network, next_state));
                    target_q = reward + obj.gamma * next_q;
                else
                    target_q = reward;
                end
                td_error = abs(target_q - current_q);
                priority = (td_error + obj.priority_epsilon) ^ obj.priority_alpha;
            else
                priority = 1.0; % 初始优先级
            end

            experience = struct('state', state, 'meta_action', meta_action, ...
                              'reward', reward, 'next_state', next_state, 'done', done, ...
                              'priority', priority, 'timestamp', obj.current_step);

            obj.meta_memory = [obj.meta_memory; experience];

            % 限制内存大小 - 移除最旧的经验
            if length(obj.meta_memory) > obj.memory_size
                obj.meta_memory = obj.meta_memory(2:end);
            end
        end
        
        function store_local_experience(obj, state, meta_action, action, reward, next_state, done)
            % 存储下层经验
            combined_state = [state; meta_action];
            combined_next_state = [next_state; meta_action];
            
            experience = struct('state', combined_state, 'action', action, ...
                              'reward', reward, 'next_state', combined_next_state, 'done', done);
            
            obj.local_memory = [obj.local_memory; experience];
            
            % 限制内存大小
            if length(obj.local_memory) > obj.memory_size
                obj.local_memory = obj.local_memory(2:end);
            end
        end
        
        function train_meta_agent(obj)
            % 训练上层智能体 - 支持优先经验回放
            if length(obj.meta_memory) < obj.batch_size
                return;
            end

            % 优先经验回放采样
            if length(obj.meta_memory) > 100
                [indices, batch] = obj.priority_sample_meta(obj.batch_size);
            else
                % 随机采样 (内存不足时)
                indices = randperm(length(obj.meta_memory), obj.batch_size);
                batch = obj.meta_memory(indices);
            end
            
            % 准备训练数据
            states = zeros(obj.state_dim, obj.batch_size);
            meta_actions = zeros(obj.meta_action_dim, obj.batch_size);
            rewards = zeros(1, obj.batch_size);
            next_states = zeros(obj.state_dim, obj.batch_size);
            dones = zeros(1, obj.batch_size);
            
            for i = 1:obj.batch_size
                states(:, i) = batch(i).state;
                meta_actions(:, i) = batch(i).meta_action;
                rewards(i) = batch(i).reward;
                next_states(:, i) = batch(i).next_state;
                dones(i) = batch(i).done;
            end
            
            % 计算目标Q值
            next_q_values = zeros(obj.meta_action_dim, obj.batch_size);
            for i = 1:obj.batch_size
                if ~dones(i)
                    next_q_values(:, i) = obj.forward_meta_network(obj.meta_target_network, next_states(:, i));
                end
            end
            
            target_q_values = rewards + obj.gamma * max(next_q_values, [], 1) .* (1 - dones);
            
            % 计算当前Q值
            current_q_values = zeros(obj.meta_action_dim, obj.batch_size);
            for i = 1:obj.batch_size
                current_q_values(:, i) = obj.forward_meta_network(obj.meta_q_network, states(:, i));
            end
            
            % 计算损失并更新网络 (简化版梯度下降)
            loss = obj.update_meta_network(states, meta_actions, target_q_values, current_q_values);
            
            % 记录损失
            obj.episode_losses = [obj.episode_losses, loss];
        end
        
        function train_local_agent(obj)
            % 训练下层智能体
            if length(obj.local_memory) < obj.batch_size
                return;
            end
            
            % 随机采样批次
            indices = randperm(length(obj.local_memory), obj.batch_size);
            batch = obj.local_memory(indices);
            
            % 准备训练数据
            input_dim = obj.state_dim + obj.meta_action_dim;
            states = zeros(input_dim, obj.batch_size);
            actions = zeros(1, obj.batch_size);
            rewards = zeros(1, obj.batch_size);
            next_states = zeros(input_dim, obj.batch_size);
            dones = zeros(1, obj.batch_size);
            
            for i = 1:obj.batch_size
                states(:, i) = batch(i).state;
                actions(i) = batch(i).action;
                rewards(i) = batch(i).reward;
                next_states(:, i) = batch(i).next_state;
                dones(i) = batch(i).done;
            end
            
            % 计算目标Q值
            next_q_values = zeros(obj.action_dim, obj.batch_size);
            for i = 1:obj.batch_size
                if ~dones(i)
                    next_q_values(:, i) = obj.forward_local_network(obj.local_target_network, next_states(:, i));
                end
            end
            
            target_q_values = rewards + obj.gamma * max(next_q_values, [], 1) .* (1 - dones);
            
            % 计算当前Q值
            current_q_values = zeros(obj.action_dim, obj.batch_size);
            for i = 1:obj.batch_size
                current_q_values(:, i) = obj.forward_local_network(obj.local_q_network, states(:, i));
            end
            
            % 更新网络
            loss = obj.update_local_network(states, actions, target_q_values, current_q_values);
        end
        
        function loss = update_meta_network(obj, states, meta_actions, target_q_values, current_q_values)
            % 更新上层网络 - 简化但有效的版本

            % 计算TD误差
            td_errors = zeros(1, obj.batch_size);
            for i = 1:obj.batch_size
                % 获取当前动作对应的Q值
                current_q = sum(current_q_values(:, i) .* meta_actions(:, i));
                td_errors(i) = target_q_values(i) - current_q;
            end

            loss = mean(td_errors.^2);

            % 简化的权重更新策略
            if abs(mean(td_errors)) > 1e-4
                learning_rate = obj.meta_learning_rate;
                avg_td_error = mean(td_errors);

                % 基于TD误差的简单权重调整
                adjustment_factor = learning_rate * avg_td_error;

                % 更新输出层权重 (更保守的更新)
                obj.meta_q_network.W3 = obj.meta_q_network.W3 * (1 + adjustment_factor * 0.01);
                obj.meta_q_network.b3 = obj.meta_q_network.b3 + adjustment_factor * 0.01;

                % 更新隐藏层权重
                obj.meta_q_network.W2 = obj.meta_q_network.W2 * (1 + adjustment_factor * 0.005);
                obj.meta_q_network.W1 = obj.meta_q_network.W1 * (1 + adjustment_factor * 0.005);

                % 权重裁剪防止发散
                obj.meta_q_network.W3 = max(-2, min(2, obj.meta_q_network.W3));
                obj.meta_q_network.W2 = max(-2, min(2, obj.meta_q_network.W2));
                obj.meta_q_network.W1 = max(-2, min(2, obj.meta_q_network.W1));
                obj.meta_q_network.b3 = max(-1, min(1, obj.meta_q_network.b3));
                obj.meta_q_network.b2 = max(-1, min(1, obj.meta_q_network.b2));
                obj.meta_q_network.b1 = max(-1, min(1, obj.meta_q_network.b1));
            end
        end
        
        function loss = update_local_network(obj, states, actions, target_q_values, current_q_values)
            % 更新下层网络 - 简化但有效的版本

            % 计算TD误差
            td_errors = zeros(1, obj.batch_size);
            for i = 1:obj.batch_size
                td_errors(i) = target_q_values(i) - current_q_values(actions(i), i);
            end

            loss = mean(td_errors.^2);

            % 简化的权重更新策略
            if abs(mean(td_errors)) > 1e-4
                learning_rate = obj.local_learning_rate;
                avg_td_error = mean(td_errors);

                % 基于TD误差的简单权重调整
                adjustment_factor = learning_rate * avg_td_error;

                % 更新输出层权重 (更保守的更新)
                obj.local_q_network.W3 = obj.local_q_network.W3 * (1 + adjustment_factor * 0.01);
                obj.local_q_network.b3 = obj.local_q_network.b3 + adjustment_factor * 0.01;

                % 更新隐藏层权重
                obj.local_q_network.W2 = obj.local_q_network.W2 * (1 + adjustment_factor * 0.005);
                obj.local_q_network.W1 = obj.local_q_network.W1 * (1 + adjustment_factor * 0.005);

                % 权重裁剪防止发散
                obj.local_q_network.W3 = max(-2, min(2, obj.local_q_network.W3));
                obj.local_q_network.W2 = max(-2, min(2, obj.local_q_network.W2));
                obj.local_q_network.W1 = max(-2, min(2, obj.local_q_network.W1));
                obj.local_q_network.b3 = max(-1, min(1, obj.local_q_network.b3));
                obj.local_q_network.b2 = max(-1, min(1, obj.local_q_network.b2));
                obj.local_q_network.b1 = max(-1, min(1, obj.local_q_network.b1));
            end
        end
        
        function update_target_networks(obj)
            % 更新目标网络
            obj.update_counter = obj.update_counter + 1;
            
            if mod(obj.update_counter, obj.target_update_freq) == 0
                % 软更新目标网络
                tau = 0.01; % 软更新参数
                
                % 更新上层目标网络
                obj.meta_target_network.W1 = (1-tau) * obj.meta_target_network.W1 + tau * obj.meta_q_network.W1;
                obj.meta_target_network.W2 = (1-tau) * obj.meta_target_network.W2 + tau * obj.meta_q_network.W2;
                obj.meta_target_network.W3 = (1-tau) * obj.meta_target_network.W3 + tau * obj.meta_q_network.W3;
                obj.meta_target_network.b1 = (1-tau) * obj.meta_target_network.b1 + tau * obj.meta_q_network.b1;
                obj.meta_target_network.b2 = (1-tau) * obj.meta_target_network.b2 + tau * obj.meta_q_network.b2;
                obj.meta_target_network.b3 = (1-tau) * obj.meta_target_network.b3 + tau * obj.meta_q_network.b3;
                
                % 更新下层目标网络
                obj.local_target_network.W1 = (1-tau) * obj.local_target_network.W1 + tau * obj.local_q_network.W1;
                obj.local_target_network.W2 = (1-tau) * obj.local_target_network.W2 + tau * obj.local_q_network.W2;
                obj.local_target_network.W3 = (1-tau) * obj.local_target_network.W3 + tau * obj.local_q_network.W3;
                obj.local_target_network.b1 = (1-tau) * obj.local_target_network.b1 + tau * obj.local_q_network.b1;
                obj.local_target_network.b2 = (1-tau) * obj.local_target_network.b2 + tau * obj.local_q_network.b2;
                obj.local_target_network.b3 = (1-tau) * obj.local_target_network.b3 + tau * obj.local_q_network.b3;
            end
        end
        
        function decay_epsilon(obj)
            % 衰减探索率
            obj.meta_epsilon = max(obj.meta_epsilon_min, obj.meta_epsilon * obj.meta_epsilon_decay);
            obj.local_epsilon = max(obj.local_epsilon_min, obj.local_epsilon * obj.local_epsilon_decay);
        end

        function [indices, batch] = priority_sample_meta(obj, batch_size)
            % 优先经验回放采样 - 上层网络 (简化版)
            priorities = [obj.meta_memory.priority];

            % 计算采样概率
            probs = priorities / sum(priorities);

            % 重要性采样权重
            weights = (length(obj.meta_memory) * probs) .^ (-obj.priority_beta);
            weights = weights / max(weights); % 归一化

            % 简化的采样方法 (避免randsample函数问题)
            cumsum_probs = cumsum(probs);
            indices = zeros(batch_size, 1);

            for i = 1:batch_size
                rand_val = rand();
                indices(i) = find(cumsum_probs >= rand_val, 1);
                if isempty(indices(i))
                    indices(i) = length(obj.meta_memory);
                end
            end

            batch = obj.meta_memory(indices);

            % 添加重要性采样权重到批次
            for i = 1:length(batch)
                batch(i).is_weight = weights(indices(i));
            end
        end

        function [indices, batch] = priority_sample_local(obj, batch_size)
            % 优先经验回放采样 - 下层网络 (简化版)
            priorities = [obj.local_memory.priority];

            % 计算采样概率
            probs = priorities / sum(priorities);

            % 重要性采样权重
            weights = (length(obj.local_memory) * probs) .^ (-obj.priority_beta);
            weights = weights / max(weights); % 归一化

            % 简化的采样方法
            cumsum_probs = cumsum(probs);
            indices = zeros(batch_size, 1);

            for i = 1:batch_size
                rand_val = rand();
                indices(i) = find(cumsum_probs >= rand_val, 1);
                if isempty(indices(i))
                    indices(i) = length(obj.local_memory);
                end
            end

            batch = obj.local_memory(indices);

            % 添加重要性采样权重到批次
            for i = 1:length(batch)
                batch(i).is_weight = weights(indices(i));
            end
        end
    end
end

% 辅助函数
function y = relu(x)
    y = max(0, x);
end

function y = softmax(x)
    exp_x = exp(x - max(x)); % 数值稳定性
    y = exp_x / sum(exp_x);
end

function y = sigmoid(x)
    y = 1 ./ (1 + exp(-x));
end
