% 验证修复效果的简单脚本

function verify_fixes()
    close all;
    clear;
    clc;
    
    fprintf('=== 验证修复效果 ===\n');
    
    % 创建测试环境
    env = struct();
    env.power_levels = [5, 10, 20, 30, 50, 80]; % mW
    env.max_steps = 100;
    env.action_dim = 6;
    env.motion_intensity = 0.3 + 0.4 * rand(1, env.max_steps);
    env.channel_quality = -60 - 20 * rand(1, env.max_steps);
    
    num_episodes = 50;  % 减少轮数以快速验证
    
    fprintf('测试各算法的学习曲线特征...\n');
    
    % 测试固定功率算法
    fprintf('1. 测试固定功率算法...\n');
    fixed_rewards = simulate_fixed_power_training(env, num_episodes);
    fprintf('   - 起始奖励: %.1f\n', fixed_rewards(1));
    fprintf('   - 最终奖励: %.1f\n', fixed_rewards(end));
    fprintf('   - 是否非线性: %s\n', check_nonlinearity(fixed_rewards));
    
    % 测试DQN算法
    fprintf('2. 测试DQN算法...\n');
    dqn_rewards = train_dqn_algorithm(env, num_episodes, 'static');
    fprintf('   - 起始奖励: %.1f\n', dqn_rewards(1));
    fprintf('   - 最终奖励: %.1f\n', dqn_rewards(end));
    fprintf('   - 是否非线性: %s\n', check_nonlinearity(dqn_rewards));
    
    % 测试演员-评论家算法
    fprintf('3. 测试演员-评论家算法...\n');
    ac_rewards = train_actor_critic_algorithm(env, num_episodes, 'static');
    fprintf('   - 起始奖励: %.1f\n', ac_rewards(1));
    fprintf('   - 最终奖励: %.1f\n', ac_rewards(end));
    fprintf('   - 是否非线性: %s\n', check_nonlinearity(ac_rewards));
    
    % 测试分层RL算法
    fprintf('4. 测试分层RL算法...\n');
    hrl_rewards = train_hierarchical_rl_algorithm(env, num_episodes, 'static');
    fprintf('   - 起始奖励: %.1f\n', hrl_rewards(1));
    fprintf('   - 最终奖励: %.1f\n', hrl_rewards(end));
    fprintf('   - 是否非线性: %s\n', check_nonlinearity(hrl_rewards));
    
    % 验证性能顺序
    fprintf('\n=== 性能顺序验证 ===\n');
    final_rewards = [fixed_rewards(end), dqn_rewards(end), ac_rewards(end), hrl_rewards(end)];
    algorithm_names = {'固定功率', 'DQN', '演员-评论家', '分层RL'};
    
    [sorted_rewards, sort_idx] = sort(final_rewards, 'descend');
    
    fprintf('最终累计奖励排序:\n');
    for i = 1:4
        fprintf('%d. %s: %.1f\n', i, algorithm_names{sort_idx(i)}, sorted_rewards(i));
    end
    
    % 检查是否符合预期顺序
    expected_order = [4, 3, 2, 1]; % 分层RL > 演员-评论家 > DQN > 固定功率
    if isequal(sort_idx, expected_order)
        fprintf('\n✓ 性能顺序正确！\n');
    else
        fprintf('\n✗ 性能顺序需要调整\n');
        fprintf('预期顺序: 分层RL > 演员-评论家 > DQN > 固定功率\n');
        fprintf('实际顺序: ');
        for i = 1:4
            fprintf('%s', algorithm_names{sort_idx(i)});
            if i < 4, fprintf(' > '); end
        end
        fprintf('\n');
    end
    
    fprintf('\n验证完成！\n');
end

function result = check_nonlinearity(rewards)
    % 检查奖励曲线是否具有非线性特征
    if length(rewards) < 10
        result = '数据不足';
        return;
    end
    
    % 计算前10%和后10%的平均增长率
    early_section = rewards(1:round(length(rewards)*0.1));
    late_section = rewards(end-round(length(rewards)*0.1)+1:end);
    
    if length(early_section) < 2 || length(late_section) < 2
        result = '数据不足';
        return;
    end
    
    early_growth = mean(diff(early_section));
    late_growth = mean(diff(late_section));
    
    % 如果早期增长率明显大于后期增长率，则认为是非线性的
    if early_growth > late_growth * 1.5
        result = '是（学习曲线特征）';
    else
        result = '否（过于线性）';
    end
end
