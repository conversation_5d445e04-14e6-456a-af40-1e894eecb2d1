% 适应性实验可视化模块
% 生成各种对比图表和分析图

function generate_adaptation_visualizations(all_results, scenarios, cross_scenario_analysis)
    % 生成适应性验证实验的可视化结果
    
    fprintf('生成适应性验证实验可视化结果...\n');
    
    % 设置图形参数
    set(0, 'DefaultFigurePosition', [100, 100, 1200, 800]);
    set(0, 'DefaultAxesFontSize', 12);
    set(0, 'DefaultTextFontSize', 12);
    
    % 1. 训练过程对比图
    generate_training_comparison_plots(all_results, scenarios);
    
    % 2. 性能指标雷达图
    generate_performance_radar_plots(all_results, scenarios, cross_scenario_analysis);
    
    % 3. 适应性时间序列图
    generate_adaptation_timeseries_plots(all_results, scenarios);
    
    % 4. 基线算法对比图
    generate_baseline_comparison_plots(all_results, scenarios, cross_scenario_analysis);
    
    % 5. 响应速度对比图
    generate_response_speed_plots(all_results, scenarios);
    
    % 6. 综合性能对比表格
    generate_performance_comparison_table(cross_scenario_analysis);
    
    fprintf('可视化结果生成完成\n');
end

function generate_training_comparison_plots(all_results, scenarios)
    % 生成训练过程对比图
    
    figure('Name', '三种场景下的训练收敛对比');
    
    colors = {'b-', 'r-', 'g-'};
    line_styles = {'-', '--', ':'};
    
    hold on;
    legend_entries = cell(length(scenarios), 1);
    
    for i = 1:length(scenarios)
        training_results = all_results{i}.training_results;
        episodes = 1:length(training_results.episode_rewards);
        
        % 平滑处理
        smoothed_rewards = smooth_data(training_results.episode_rewards, 10);
        
        plot(episodes, smoothed_rewards, colors{i}, 'LineWidth', 2, 'MarkerSize', 6);
        legend_entries{i} = sprintf('%s (%s)', scenarios{i}.name, scenarios{i}.type);
        
        % 标记收敛点
        if training_results.convergence_episode > 0
            plot(training_results.convergence_episode, ...
                 smoothed_rewards(training_results.convergence_episode), ...
                 'o', 'Color', colors{i}(1), 'MarkerSize', 8, 'MarkerFaceColor', colors{i}(1));
        end
    end
    
    xlabel('训练轮数');
    ylabel('累积奖励');
    title('三种场景下的训练收敛对比');
    legend(legend_entries, 'Location', 'southeast');
    grid on;
    
    % 保存图片
    saveas(gcf, 'adaptation_experiment_results/training_convergence_comparison.png');
    saveas(gcf, 'adaptation_experiment_results/training_convergence_comparison.fig');
    
    fprintf('训练收敛对比图已生成\n');
end

function generate_performance_radar_plots(all_results, scenarios, cross_scenario_analysis)
    % 生成性能指标雷达图
    
    figure('Name', '三种场景下的综合性能雷达图');
    
    % 准备雷达图数据
    num_scenarios = length(scenarios);
    metrics = {'能效', 'PDR', '延迟', '响应速度', '稳定性'};
    num_metrics = length(metrics);
    
    % 归一化性能数据
    performance_data = zeros(num_scenarios, num_metrics);
    
    for i = 1:num_scenarios
        perf = all_results{i}.performance_results;
        adapt = all_results{i}.adaptation_metrics;
        
        % 能效 (越高越好)
        energy_efficiency = 1 / (1 + perf.energy.mean / 50);
        performance_data(i, 1) = energy_efficiency;
        
        % PDR (越高越好)
        performance_data(i, 2) = perf.pdr.mean;
        
        % 延迟 (越低越好，转换为越高越好)
        delay_score = 1 / (1 + perf.delay.mean / 20);
        performance_data(i, 3) = delay_score;
        
        % 响应速度 (越低越好，转换为越高越好)
        response_score = 1 / (1 + perf.response_time.mean / 100);
        performance_data(i, 4) = response_score;
        
        % 稳定性
        performance_data(i, 5) = perf.stability.overall_stability;
    end
    
    % 绘制雷达图
    colors = {'b', 'r', 'g'};
    
    for i = 1:num_scenarios
        subplot(2, 2, i);
        
        % 创建雷达图数据
        data = [performance_data(i, :), performance_data(i, 1)]; % 闭合图形
        angles = linspace(0, 2*pi, num_metrics + 1);
        
        % 绘制雷达图
        polarplot(angles, data, colors{i}, 'LineWidth', 2, 'Marker', 'o', 'MarkerSize', 6);
        hold on;
        
        % 填充区域
        polarplot(angles, data, colors{i}, 'LineWidth', 1);
        
        title(sprintf('%s性能轮廓', scenarios{i}.name));
        
        % 设置角度标签
        thetaticks(angles(1:end-1) * 180 / pi);
        thetaticklabels(metrics);
        rlim([0, 1]);
    end
    
    % 综合对比
    subplot(2, 2, 4);
    hold on;
    
    for i = 1:num_scenarios
        data = [performance_data(i, :), performance_data(i, 1)];
        angles = linspace(0, 2*pi, num_metrics + 1);
        
        polarplot(angles, data, colors{i}, 'LineWidth', 2, 'Marker', 'o', 'MarkerSize', 4);
    end
    
    title('三种场景性能对比');
    legend({scenarios.name}, 'Location', 'best');
    thetaticks(angles(1:end-1) * 180 / pi);
    thetaticklabels(metrics);
    rlim([0, 1]);
    
    % 保存图片
    saveas(gcf, 'adaptation_experiment_results/performance_radar_comparison.png');
    saveas(gcf, 'adaptation_experiment_results/performance_radar_comparison.fig');
    
    fprintf('性能雷达图已生成\n');
end

function generate_adaptation_timeseries_plots(all_results, scenarios)
    % 生成适应性时间序列图
    
    figure('Name', '运动强度与功率调整的时间序列对比');
    
    for i = 1:length(scenarios)
        subplot(3, 1, i);
        
        % 获取详细评估结果
        detailed_results = all_results{i}.performance_results.detailed_results;
        if ~isempty(detailed_results)
            eval_result = detailed_results{1}; % 使用第一次运行的结果
            
            % 生成时间轴
            time_steps = 1:length(eval_result.power_levels);
            time_seconds = time_steps * 0.1; % 假设每步0.1秒
            
            % 上子图：运动强度
            yyaxis left;
            motion_intensity = generate_motion_intensity_for_scenario(scenarios{i}, length(time_steps));
            plot(time_seconds, motion_intensity, 'b-', 'LineWidth', 1.5);
            ylabel('运动强度');
            ylim([0, max(motion_intensity) * 1.1]);
            
            % 下子图：功率等级选择
            yyaxis right;
            plot(time_seconds, eval_result.power_levels, 'r-', 'LineWidth', 2, 'Marker', 'o', 'MarkerSize', 3);
            ylabel('功率等级');
            ylim([0.5, 4.5]);
            
            xlabel('时间 (秒)');
            title(sprintf('场景%d: %s - %s', i, scenarios{i}.name, scenarios{i}.description));
            grid on;
            
            % 添加预期行为注释
            text(0.02, 0.95, sprintf('预期: %s', scenarios{i}.expected_behavior), ...
                 'Units', 'normalized', 'FontSize', 10, 'BackgroundColor', 'white');
        end
    end
    
    % 保存图片
    saveas(gcf, 'adaptation_experiment_results/adaptation_timeseries_comparison.png');
    saveas(gcf, 'adaptation_experiment_results/adaptation_timeseries_comparison.fig');
    
    fprintf('适应性时间序列图已生成\n');
end

function motion_intensity = generate_motion_intensity_for_scenario(scenario, num_steps)
    % 为场景生成运动强度数据
    
    time = linspace(0, num_steps * 0.1, num_steps);
    
    switch scenario.type
        case 'static'
            % 静态场景：低且稳定的运动强度
            motion_intensity = 0.1 + 0.05 * sin(2*pi*0.1*time) + 0.02 * randn(size(time));
            motion_intensity = max(0, motion_intensity);
            
        case 'dynamic'
            % 动态转换场景：坐→站转换
            transition_point = num_steps * 0.6;
            motion_intensity = zeros(size(time));
            motion_intensity(1:round(transition_point)) = 0.1 + 0.05 * randn(1, round(transition_point));
            motion_intensity(round(transition_point)+1:end) = 2.0 + 0.3 * randn(1, num_steps - round(transition_point));
            motion_intensity = max(0, motion_intensity);
            
        case 'periodic'
            % 周期性运动场景：规律行走
            step_freq = 1.2; % Hz
            motion_intensity = 1.0 + 0.8 * sin(2*pi*step_freq*time) + 0.1 * randn(size(time));
            motion_intensity = max(0, motion_intensity);
            
        otherwise
            motion_intensity = 0.5 * ones(size(time));
    end
end

function generate_baseline_comparison_plots(all_results, scenarios, cross_scenario_analysis)
    % 生成基线算法对比图
    
    baseline_comp = cross_scenario_analysis.baseline_comparison;
    
    % 能耗对比柱状图
    figure('Name', '基线算法能耗对比');
    
    subplot(2, 2, 1);
    bar(baseline_comp.energy_matrix');
    xlabel('场景');
    ylabel('平均能耗 (mJ)');
    title('各算法能耗对比');
    legend(baseline_comp.algorithm_names, 'Location', 'best');
    set(gca, 'XTickLabel', baseline_comp.scenario_names);
    grid on;
    
    % PDR对比柱状图
    subplot(2, 2, 2);
    bar(baseline_comp.pdr_matrix');
    xlabel('场景');
    ylabel('平均PDR');
    title('各算法PDR对比');
    legend(baseline_comp.algorithm_names, 'Location', 'best');
    set(gca, 'XTickLabel', baseline_comp.scenario_names);
    grid on;
    
    % 延迟对比柱状图
    subplot(2, 2, 3);
    bar(baseline_comp.delay_matrix');
    xlabel('场景');
    ylabel('平均延迟 (ms)');
    title('各算法延迟对比');
    legend(baseline_comp.algorithm_names, 'Location', 'best');
    set(gca, 'XTickLabel', baseline_comp.scenario_names);
    grid on;
    
    % 综合评分对比
    subplot(2, 2, 4);
    % 计算综合评分
    energy_scores = 1 ./ (1 + baseline_comp.energy_matrix / 100);
    pdr_scores = baseline_comp.pdr_matrix;
    delay_scores = 1 ./ (1 + baseline_comp.delay_matrix / 50);
    overall_scores = (energy_scores + pdr_scores + delay_scores) / 3;
    
    bar(overall_scores');
    xlabel('场景');
    ylabel('综合评分');
    title('各算法综合评分对比');
    legend(baseline_comp.algorithm_names, 'Location', 'best');
    set(gca, 'XTickLabel', baseline_comp.scenario_names);
    grid on;
    
    % 保存图片
    saveas(gcf, 'adaptation_experiment_results/baseline_algorithm_comparison.png');
    saveas(gcf, 'adaptation_experiment_results/baseline_algorithm_comparison.fig');
    
    fprintf('基线算法对比图已生成\n');
end

function generate_response_speed_plots(all_results, scenarios)
    % 生成响应速度对比图
    
    figure('Name', '响应速度对比');
    
    % 提取响应时间数据
    num_scenarios = length(scenarios);
    response_times = zeros(num_scenarios, 1);
    response_stds = zeros(num_scenarios, 1);
    scenario_names = cell(num_scenarios, 1);
    
    for i = 1:num_scenarios
        perf = all_results{i}.performance_results;
        response_times(i) = perf.response_time.mean;
        response_stds(i) = perf.response_time.std;
        scenario_names{i} = scenarios{i}.name;
    end
    
    % 绘制箱线图风格的误差条图
    subplot(1, 2, 1);
    errorbar(1:num_scenarios, response_times, response_stds, 'bo-', 'LineWidth', 2, 'MarkerSize', 8);
    xlabel('场景');
    ylabel('响应时间 (ms)');
    title('各场景响应速度对比');
    set(gca, 'XTick', 1:num_scenarios, 'XTickLabel', scenario_names);
    grid on;
    
    % 响应速度排名
    subplot(1, 2, 2);
    [sorted_times, sort_idx] = sort(response_times);
    sorted_names = scenario_names(sort_idx);
    
    bar(sorted_times, 'FaceColor', [0.3, 0.7, 0.9]);
    xlabel('场景 (按响应速度排序)');
    ylabel('响应时间 (ms)');
    title('响应速度排名');
    set(gca, 'XTick', 1:num_scenarios, 'XTickLabel', sorted_names);
    
    % 添加数值标签
    for i = 1:num_scenarios
        text(i, sorted_times(i) + max(sorted_times) * 0.02, ...
             sprintf('%.1f ms', sorted_times(i)), ...
             'HorizontalAlignment', 'center', 'FontSize', 10);
    end
    
    grid on;
    
    % 保存图片
    saveas(gcf, 'adaptation_experiment_results/response_speed_comparison.png');
    saveas(gcf, 'adaptation_experiment_results/response_speed_comparison.fig');
    
    fprintf('响应速度对比图已生成\n');
end

function generate_performance_comparison_table(cross_scenario_analysis)
    % 生成性能对比表格
    
    baseline_comp = cross_scenario_analysis.baseline_comparison;
    
    % 创建表格图
    figure('Name', '算法性能对比表格');
    
    % 准备表格数据
    num_algorithms = length(baseline_comp.algorithm_names);
    num_scenarios = length(baseline_comp.scenario_names);
    
    % 创建能耗表格
    subplot(2, 2, 1);
    axis off;
    
    % 表格标题
    title('能耗对比 (mJ)', 'FontSize', 14, 'FontWeight', 'bold');
    
    % 创建表格数据
    table_data = cell(num_algorithms + 1, num_scenarios + 1);
    table_data{1, 1} = '算法\场景';
    
    % 填充表头
    for j = 1:num_scenarios
        table_data{1, j+1} = baseline_comp.scenario_names{j};
    end
    
    % 填充数据
    for i = 1:num_algorithms
        table_data{i+1, 1} = baseline_comp.algorithm_names{i};
        for j = 1:num_scenarios
            table_data{i+1, j+1} = sprintf('%.3f', baseline_comp.energy_matrix(i, j));
        end
    end
    
    % 绘制表格
    table_handle = uitable('Data', table_data(2:end, 2:end), ...
                          'ColumnName', table_data(1, 2:end), ...
                          'RowName', table_data(2:end, 1), ...
                          'Position', [0.05, 0.55, 0.4, 0.35]);
    
    % PDR表格
    subplot(2, 2, 2);
    axis off;
    title('PDR对比', 'FontSize', 14, 'FontWeight', 'bold');
    
    pdr_data = cell(num_algorithms, num_scenarios);
    for i = 1:num_algorithms
        for j = 1:num_scenarios
            pdr_data{i, j} = sprintf('%.3f', baseline_comp.pdr_matrix(i, j));
        end
    end
    
    uitable('Data', pdr_data, ...
           'ColumnName', baseline_comp.scenario_names, ...
           'RowName', baseline_comp.algorithm_names, ...
           'Position', [0.55, 0.55, 0.4, 0.35]);
    
    % 延迟表格
    subplot(2, 2, 3);
    axis off;
    title('延迟对比 (ms)', 'FontSize', 14, 'FontWeight', 'bold');
    
    delay_data = cell(num_algorithms, num_scenarios);
    for i = 1:num_algorithms
        for j = 1:num_scenarios
            delay_data{i, j} = sprintf('%.3f', baseline_comp.delay_matrix(i, j));
        end
    end
    
    uitable('Data', delay_data, ...
           'ColumnName', baseline_comp.scenario_names, ...
           'RowName', baseline_comp.algorithm_names, ...
           'Position', [0.05, 0.05, 0.4, 0.35]);
    
    % 改进幅度表格
    subplot(2, 2, 4);
    axis off;
    title('能耗改进幅度 (%)', 'FontSize', 14, 'FontWeight', 'bold');
    
    improvement_data = cell(num_algorithms, num_scenarios);
    for i = 1:num_algorithms
        for j = 1:num_scenarios
            improvement_data{i, j} = sprintf('%.1f%%', baseline_comp.energy_improvement(i, j));
        end
    end
    
    uitable('Data', improvement_data, ...
           'ColumnName', baseline_comp.scenario_names, ...
           'RowName', baseline_comp.algorithm_names, ...
           'Position', [0.55, 0.05, 0.4, 0.35]);
    
    % 保存图片
    saveas(gcf, 'adaptation_experiment_results/performance_comparison_table.png');
    saveas(gcf, 'adaptation_experiment_results/performance_comparison_table.fig');
    
    fprintf('性能对比表格已生成\n');
end

function smoothed_data = smooth_data(data, window_size)
    % 数据平滑处理
    
    if length(data) < window_size
        smoothed_data = data;
        return;
    end
    
    smoothed_data = zeros(size(data));
    half_window = floor(window_size / 2);
    
    for i = 1:length(data)
        start_idx = max(1, i - half_window);
        end_idx = min(length(data), i + half_window);
        smoothed_data(i) = mean(data(start_idx:end_idx));
    end
end
