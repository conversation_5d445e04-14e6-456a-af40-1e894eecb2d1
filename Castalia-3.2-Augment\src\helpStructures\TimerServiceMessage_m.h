//
// Generated file, do not edit! Created by nedtool 4.6 from src/helpStructures/TimerServiceMessage.msg.
//

#ifndef _TIMERSERVICEMESSAGE_M_H_
#define _TIMERSERVICEMESSAGE_M_H_

#include <omnetpp.h>

// nedtool version check
#define MSGC_VERSION 0x0406
#if (MSGC_VERSION!=OMNETPP_VERSION)
#    error Version mismatch! Probably this file was generated by an earlier version of nedtool: 'make clean' should help.
#endif



/**
 * Class generated from <tt>src/helpStructures/TimerServiceMessage.msg:13</tt> by nedtool.
 * <pre>
 * message TimerServiceMessage
 * {
 *     int timerIndex;
 * }
 * </pre>
 */
class TimerServiceMessage : public ::cMessage
{
  protected:
    int timerIndex_var;

  private:
    void copy(const TimerServiceMessage& other);

  protected:
    // protected and unimplemented operator==(), to prevent accidental usage
    bool operator==(const TimerServiceMessage&);

  public:
    TimerServiceMessage(const char *name=NULL, int kind=0);
    TimerServiceMessage(const TimerServiceMessage& other);
    virtual ~TimerServiceMessage();
    TimerServiceMessage& operator=(const TimerServiceMessage& other);
    virtual TimerServiceMessage *dup() const {return new TimerServiceMessage(*this);}
    virtual void parsimPack(cCommBuffer *b);
    virtual void parsimUnpack(cCommBuffer *b);

    // field getter/setter methods
    virtual int getTimerIndex() const;
    virtual void setTimerIndex(int timerIndex);
};

inline void doPacking(cCommBuffer *b, TimerServiceMessage& obj) {obj.parsimPack(b);}
inline void doUnpacking(cCommBuffer *b, TimerServiceMessage& obj) {obj.parsimUnpack(b);}


#endif // ifndef _TIMERSERVICEMESSAGE_M_H_

