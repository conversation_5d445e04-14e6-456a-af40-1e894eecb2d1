% 适应性实验评估模块
% 评估算法性能和计算适应性指标

function performance_results = evaluate_algorithm_performance(agent, env, scenario)
    % 评估算法性能
    
    fprintf('评估算法性能 - 场景: %s\n', scenario.name);
    
    % 运行多次评估获得稳定结果
    num_eval_runs = 5;
    eval_results = cell(num_eval_runs, 1);
    
    for run = 1:num_eval_runs
        eval_results{run} = run_single_evaluation(agent, env, scenario);
    end
    
    % 汇总评估结果
    performance_results = aggregate_evaluation_results(eval_results, scenario);
    
    fprintf('性能评估完成 - 场景: %s\n', scenario.name);
end

function eval_result = run_single_evaluation(agent, env, scenario)
    % 运行单次评估
    
    % 重置环境
    state = env.reset();
    
    % 初始化记录
    eval_result = struct();
    eval_result.states = [];
    eval_result.actions = [];
    eval_result.rewards = [];
    eval_result.power_levels = [];
    eval_result.energy_consumption = [];
    eval_result.pdr_values = [];
    eval_result.delay_values = [];
    eval_result.response_times = [];
    
    % 运行评估
    total_reward = 0;
    total_energy = 0;
    total_pdr = 0;
    total_delay = 0;
    step_count = 0;
    
    % 禁用探索（epsilon = 0）
    original_epsilon = agent.epsilon;
    agent.epsilon = 0;
    
    for step = 1:env.max_steps
        
        % 记录状态
        eval_result.states = [eval_result.states, state];
        
        % 选择动作
        action = get_best_action(agent, state);
        eval_result.actions = [eval_result.actions, action];
        eval_result.power_levels = [eval_result.power_levels, env.power_levels(action)];
        
        % 执行动作
        [next_state, reward, done, info] = env.step(action);
        
        % 记录结果
        eval_result.rewards = [eval_result.rewards, reward];
        eval_result.energy_consumption = [eval_result.energy_consumption, info.energy];
        eval_result.pdr_values = [eval_result.pdr_values, info.pdr];
        eval_result.delay_values = [eval_result.delay_values, info.delay];
        
        % 计算响应时间（简化版）
        response_time = calculate_response_time(eval_result, step, scenario);
        eval_result.response_times = [eval_result.response_times, response_time];
        
        % 更新统计
        total_reward = total_reward + reward;
        total_energy = total_energy + info.energy;
        total_pdr = total_pdr + info.pdr;
        total_delay = total_delay + info.delay;
        step_count = step_count + 1;
        
        % 更新状态
        state = next_state;
        
        if done
            break;
        end
    end
    
    % 恢复探索率
    agent.epsilon = original_epsilon;
    
    % 计算总体指标
    eval_result.total_reward = total_reward;
    eval_result.avg_energy = total_energy / step_count;
    eval_result.avg_pdr = total_pdr / step_count;
    eval_result.avg_delay = total_delay / step_count;
    eval_result.total_steps = step_count;
end

function response_time = calculate_response_time(eval_result, current_step, scenario)
    % 计算响应时间
    
    if current_step < 5
        response_time = 0;
        return;
    end
    
    % 检测状态变化
    recent_states = eval_result.states(:, max(1, current_step-4):current_step);
    
    % 计算状态变化幅度
    if size(recent_states, 2) > 1
        state_changes = diff(recent_states, 1, 2);
        change_magnitude = sqrt(sum(state_changes.^2, 1));
        
        % 检测显著变化
        change_threshold = 0.5;
        significant_changes = find(change_magnitude > change_threshold);
        
        if ~isempty(significant_changes)
            % 计算功率调整的响应时间
            change_step = significant_changes(end);
            power_changes = diff(eval_result.power_levels);
            
            % 查找变化后的功率调整
            response_steps = 0;
            for i = change_step:min(length(power_changes), current_step-1)
                if abs(power_changes(i)) > 0.1 % 功率变化阈值
                    response_steps = i - change_step;
                    break;
                end
            end
            
            % 转换为毫秒（假设每步0.1秒）
            response_time = response_steps * 100; % ms
        else
            response_time = 0;
        end
    else
        response_time = 0;
    end
    
    % 限制在合理范围内
    response_time = max(0, min(response_time, 1000));
end

function performance_results = aggregate_evaluation_results(eval_results, scenario)
    % 汇总多次评估结果
    
    num_runs = length(eval_results);
    
    % 提取各次运行的指标
    total_rewards = zeros(num_runs, 1);
    avg_energies = zeros(num_runs, 1);
    avg_pdrs = zeros(num_runs, 1);
    avg_delays = zeros(num_runs, 1);
    avg_response_times = zeros(num_runs, 1);
    
    for i = 1:num_runs
        total_rewards(i) = eval_results{i}.total_reward;
        avg_energies(i) = eval_results{i}.avg_energy;
        avg_pdrs(i) = eval_results{i}.avg_pdr;
        avg_delays(i) = eval_results{i}.avg_delay;
        avg_response_times(i) = mean(eval_results{i}.response_times);
    end
    
    % 计算统计量
    performance_results = struct();
    performance_results.scenario = scenario;
    
    % 基础性能指标
    performance_results.reward = struct('mean', mean(total_rewards), 'std', std(total_rewards));
    performance_results.energy = struct('mean', mean(avg_energies), 'std', std(avg_energies));
    performance_results.pdr = struct('mean', mean(avg_pdrs), 'std', std(avg_pdrs));
    performance_results.delay = struct('mean', mean(avg_delays), 'std', std(avg_delays));
    performance_results.response_time = struct('mean', mean(avg_response_times), 'std', std(avg_response_times));
    
    % 详细数据（用于后续分析）
    performance_results.detailed_results = eval_results;
    
    % 计算能效比
    energy_efficiency = avg_pdrs ./ avg_energies;
    performance_results.energy_efficiency = struct('mean', mean(energy_efficiency), 'std', std(energy_efficiency));
    
    % 计算稳定性指标
    performance_results.stability = calculate_stability_metrics(eval_results);
    
    fprintf('性能汇总完成 - 平均能耗: %.3f mJ, 平均PDR: %.3f, 平均延迟: %.3f ms\n', ...
           performance_results.energy.mean, performance_results.pdr.mean, performance_results.delay.mean);
end

function stability = calculate_stability_metrics(eval_results)
    % 计算稳定性指标
    
    % 收集所有运行的功率选择序列
    all_power_sequences = [];
    all_energy_sequences = [];
    
    for i = 1:length(eval_results)
        all_power_sequences = [all_power_sequences; eval_results{i}.power_levels'];
        all_energy_sequences = [all_energy_sequences; eval_results{i}.energy_consumption'];
    end
    
    % 计算功率选择的稳定性
    power_changes = sum(abs(diff(all_power_sequences, 1, 2)), 2);
    power_stability = 1 / (1 + mean(power_changes));
    
    % 计算能耗的稳定性
    energy_variance = var(all_energy_sequences, 0, 2);
    energy_stability = 1 / (1 + mean(energy_variance));
    
    % 综合稳定性指标
    overall_stability = (power_stability + energy_stability) / 2;
    
    stability = struct();
    stability.power_stability = power_stability;
    stability.energy_stability = energy_stability;
    stability.overall_stability = overall_stability;
end

function adaptation_metrics = calculate_adaptation_metrics(agent, env, scenario, training_results)
    % 计算适应性指标
    
    fprintf('计算适应性指标 - 场景: %s\n', scenario.name);
    
    adaptation_metrics = struct();
    adaptation_metrics.scenario = scenario;
    
    % 1. 响应速度指标
    adaptation_metrics.response_speed = calculate_response_speed_metrics(agent, env, scenario);
    
    % 2. 适应准确性指标
    adaptation_metrics.adaptation_accuracy = calculate_adaptation_accuracy_metrics(agent, env, scenario);
    
    % 3. 学习效率指标
    adaptation_metrics.learning_efficiency = calculate_learning_efficiency_metrics(training_results, scenario);
    
    % 4. 场景特定指标
    adaptation_metrics.scenario_specific = calculate_scenario_specific_metrics(agent, env, scenario);
    
    fprintf('适应性指标计算完成\n');
end

function response_speed = calculate_response_speed_metrics(agent, env, scenario)
    % 计算响应速度指标
    
    % 模拟环境变化并测量响应时间
    num_tests = 10;
    response_times = zeros(num_tests, 1);
    
    for test = 1:num_tests
        % 重置环境
        state = env.reset();
        
        % 运行到中间状态
        for step = 1:50
            action = get_best_action(agent, state);
            [state, ~, done, ~] = env.step(action);
            if done, break; end
        end
        
        % 记录变化前的功率
        pre_change_action = get_best_action(agent, state);
        
        % 模拟环境突变
        original_imu = env.imu_data(env.current_step);
        env.imu_data(env.current_step) = original_imu * 3; % 增加运动强度
        
        % 测量响应时间
        response_time = 0;
        for response_step = 1:20
            new_state = env.get_current_state();
            new_action = get_best_action(agent, new_state);
            
            if abs(new_action - pre_change_action) > 0
                response_time = response_step * 100; % 转换为毫秒
                break;
            end
            
            % 继续执行
            [new_state, ~, done, ~] = env.step(new_action);
            if done, break; end
        end
        
        response_times(test) = response_time;
        
        % 恢复原始数据
        env.imu_data(env.current_step) = original_imu;
    end
    
    response_speed = struct();
    response_speed.mean_response_time = mean(response_times);
    response_speed.std_response_time = std(response_times);
    response_speed.max_response_time = max(response_times);
    response_speed.response_rate = sum(response_times > 0) / num_tests; % 响应成功率
end

function adaptation_accuracy = calculate_adaptation_accuracy_metrics(agent, env, scenario)
    % 计算适应准确性指标
    
    % 运行完整评估
    eval_result = run_single_evaluation(agent, env, scenario);
    
    % 计算功率选择与理想功率的匹配度
    optimal_powers = calculate_optimal_power_sequence(env, scenario);
    actual_powers = eval_result.power_levels;
    
    % 确保长度一致
    min_length = min(length(optimal_powers), length(actual_powers));
    optimal_powers = optimal_powers(1:min_length);
    actual_powers = actual_powers(1:min_length);
    
    % 计算相关性
    if length(optimal_powers) > 1 && std(optimal_powers) > 0
        correlation = corrcoef(optimal_powers, actual_powers);
        power_correlation = correlation(1, 2);
    else
        power_correlation = 0;
    end
    
    % 计算平均绝对误差
    power_mae = mean(abs(optimal_powers - actual_powers));
    
    % 计算预测准确性（针对周期性运动）
    prediction_accuracy = 0;
    if strcmp(scenario.type, 'periodic')
        prediction_accuracy = calculate_prediction_accuracy(eval_result, scenario);
    end
    
    adaptation_accuracy = struct();
    adaptation_accuracy.power_correlation = power_correlation;
    adaptation_accuracy.power_mae = power_mae;
    adaptation_accuracy.prediction_accuracy = prediction_accuracy;
    adaptation_accuracy.overall_accuracy = (abs(power_correlation) + (1 - power_mae/10) + prediction_accuracy) / 3;
end

function optimal_powers = calculate_optimal_power_sequence(env, scenario)
    % 计算理想功率序列
    
    % 基于运动强度和信道状态计算理想功率
    optimal_powers = zeros(1, env.max_steps);
    
    for step = 1:env.max_steps
        if step > length(env.imu_data)
            break;
        end
        
        % 获取当前运动强度和信道状态
        motion_intensity = abs(env.imu_data(step));
        channel_quality = env.rssi_data(step);
        
        % 根据场景类型调整策略
        switch scenario.type
            case 'static'
                % 静态场景：优先节能
                if channel_quality > -70
                    optimal_power = 1; % 最低功率
                else
                    optimal_power = 2; % 稍高功率
                end
                
            case 'dynamic'
                % 动态场景：根据运动强度调整
                if motion_intensity > 2
                    optimal_power = 4; % 高功率保证通信
                elseif motion_intensity > 1
                    optimal_power = 3; % 中等功率
                else
                    optimal_power = 2; % 低功率
                end
                
            case 'periodic'
                % 周期性场景：预测性调整
                cycle_phase = mod(step * 0.1, 2*pi);
                if sin(cycle_phase) > 0.5
                    optimal_power = 3; % 运动期高功率
                else
                    optimal_power = 2; % 静止期低功率
                end
                
            otherwise
                optimal_power = 2; % 默认中等功率
        end
        
        optimal_powers(step) = optimal_power;
    end
end

function prediction_accuracy = calculate_prediction_accuracy(eval_result, scenario)
    % 计算预测准确性（针对周期性运动）
    
    if ~strcmp(scenario.type, 'periodic')
        prediction_accuracy = 0;
        return;
    end
    
    % 分析功率选择的周期性
    power_sequence = eval_result.power_levels;
    
    if length(power_sequence) < 20
        prediction_accuracy = 0;
        return;
    end
    
    % 使用FFT分析周期性
    try
        Y = fft(power_sequence);
        P = abs(Y).^2;
        
        % 找到主频率
        [max_power, max_idx] = max(P(2:end/2));
        total_power = sum(P);
        
        % 周期性强度
        periodicity_strength = max_power / total_power;
        
        % 预测准确性基于周期性强度
        prediction_accuracy = min(1, periodicity_strength * 2);
        
    catch
        prediction_accuracy = 0;
    end
end

function learning_efficiency = calculate_learning_efficiency_metrics(training_results, scenario)
    % 计算学习效率指标
    
    learning_efficiency = struct();
    
    % 收敛速度
    if training_results.convergence_episode > 0
        learning_efficiency.convergence_speed = 1 / training_results.convergence_episode;
    else
        learning_efficiency.convergence_speed = 0;
    end
    
    % 学习稳定性
    if length(training_results.episode_rewards) > 20
        final_rewards = training_results.episode_rewards(end-19:end);
        learning_efficiency.learning_stability = 1 / (1 + std(final_rewards));
    else
        learning_efficiency.learning_stability = 0;
    end
    
    % 训练效率
    learning_efficiency.training_efficiency = training_results.final_avg_reward / training_results.training_time;
    
    % 场景适应性评分
    switch scenario.type
        case 'static'
            % 静态场景：重视稳定性
            learning_efficiency.scenario_score = learning_efficiency.learning_stability;
        case 'dynamic'
            % 动态场景：重视收敛速度
            learning_efficiency.scenario_score = learning_efficiency.convergence_speed * 100;
        case 'periodic'
            % 周期性场景：平衡考虑
            learning_efficiency.scenario_score = (learning_efficiency.convergence_speed * 100 + learning_efficiency.learning_stability) / 2;
        otherwise
            learning_efficiency.scenario_score = 0.5;
    end
end

function scenario_specific = calculate_scenario_specific_metrics(agent, env, scenario)
    % 计算场景特定指标
    
    scenario_specific = struct();
    scenario_specific.scenario_type = scenario.type;
    
    switch scenario.type
        case 'static'
            % 静态场景特定指标
            scenario_specific = calculate_static_metrics(agent, env, scenario);
            
        case 'dynamic'
            % 动态场景特定指标
            scenario_specific = calculate_dynamic_metrics(agent, env, scenario);
            
        case 'periodic'
            % 周期性场景特定指标
            scenario_specific = calculate_periodic_metrics(agent, env, scenario);
            
        otherwise
            scenario_specific.custom_metric = 0;
    end
end

function static_metrics = calculate_static_metrics(agent, env, scenario)
    % 静态场景特定指标
    
    eval_result = run_single_evaluation(agent, env, scenario);
    
    static_metrics = struct();
    static_metrics.scenario_type = 'static';
    
    % 功率稳定性
    power_changes = abs(diff(eval_result.power_levels));
    static_metrics.power_stability = 1 / (1 + mean(power_changes));
    
    % 能耗一致性
    energy_variance = var(eval_result.energy_consumption);
    static_metrics.energy_consistency = 1 / (1 + energy_variance);
    
    % 低功率偏好
    avg_power_level = mean(eval_result.power_levels);
    static_metrics.low_power_preference = 1 / (1 + avg_power_level);
    
    % 综合静态适应性评分
    static_metrics.static_adaptation_score = (static_metrics.power_stability + ...
                                            static_metrics.energy_consistency + ...
                                            static_metrics.low_power_preference) / 3;
end

function dynamic_metrics = calculate_dynamic_metrics(agent, env, scenario)
    % 动态场景特定指标
    
    dynamic_metrics = struct();
    dynamic_metrics.scenario_type = 'dynamic';
    
    % 响应敏感性
    dynamic_metrics.response_sensitivity = calculate_response_speed_metrics(agent, env, scenario);
    
    % 功率调整幅度
    eval_result = run_single_evaluation(agent, env, scenario);
    power_adjustments = abs(diff(eval_result.power_levels));
    dynamic_metrics.adjustment_magnitude = mean(power_adjustments);
    
    % 适应性灵活性
    dynamic_metrics.adaptation_flexibility = std(eval_result.power_levels) / mean(eval_result.power_levels);
    
    % 综合动态适应性评分
    dynamic_metrics.dynamic_adaptation_score = (dynamic_metrics.response_sensitivity.response_rate + ...
                                              min(1, dynamic_metrics.adjustment_magnitude) + ...
                                              min(1, dynamic_metrics.adaptation_flexibility)) / 3;
end

function periodic_metrics = calculate_periodic_metrics(agent, env, scenario)
    % 周期性场景特定指标
    
    eval_result = run_single_evaluation(agent, env, scenario);
    
    periodic_metrics = struct();
    periodic_metrics.scenario_type = 'periodic';
    
    % 周期性识别能力
    periodic_metrics.periodicity_recognition = calculate_prediction_accuracy(eval_result, scenario);
    
    % 预测性调整
    power_sequence = eval_result.power_levels;
    if length(power_sequence) > 10
        % 计算功率序列的自相关
        autocorr_result = xcorr(power_sequence, 'normalized');
        periodic_metrics.predictive_adjustment = max(autocorr_result(length(power_sequence)+1:end));
    else
        periodic_metrics.predictive_adjustment = 0;
    end
    
    % 周期性稳定性
    if length(power_sequence) > 20
        % 分段分析稳定性
        segment_size = floor(length(power_sequence) / 4);
        segment_means = zeros(4, 1);
        for i = 1:4
            start_idx = (i-1) * segment_size + 1;
            end_idx = min(i * segment_size, length(power_sequence));
            segment_means(i) = mean(power_sequence(start_idx:end_idx));
        end
        periodic_metrics.periodic_stability = 1 / (1 + std(segment_means));
    else
        periodic_metrics.periodic_stability = 0;
    end
    
    % 综合周期性适应性评分
    periodic_metrics.periodic_adaptation_score = (periodic_metrics.periodicity_recognition + ...
                                                periodic_metrics.predictive_adjustment + ...
                                                periodic_metrics.periodic_stability) / 3;
end
