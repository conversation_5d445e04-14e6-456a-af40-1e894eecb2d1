function [agent, results] = train_dqn(env, params)
% TRAIN_DQN  简化版 DQN 训练，仅下层网络，无分层。用于消融实验对比。
%   env   - 强化学习环境，需实现 reset/step, 并给出 state_dim, action_dim
%   params - 结构体，需包含 num_episodes, max_steps_per_episode, batch_size,
%            update_frequency, learning_rate, gamma, epsilon 等字段。

% 初始化 agent 结构
agent = struct();
agent.state_dim  = env.state_dim;
agent.action_dim = env.action_dim;
agent.hidden_size = 64;
agent.learning_rate = params.learning_rate;
agent.gamma = params.gamma;
agent.epsilon = params.epsilon;
agent.epsilon_decay = params.epsilon_decay;
agent.epsilon_min = params.epsilon_min;
agent.memory_size = 10000;
agent.q_weights = init_weights(agent.state_dim, agent.hidden_size, agent.action_dim);
agent.memory = cell(0);
agent.memory_idx = 1;

results.episode_rewards = zeros(params.num_episodes,1);
results.episode_losses  = zeros(params.num_episodes,1);

for ep = 1:params.num_episodes
    s = env.reset();
    ep_reward = 0; losses = [];
    for step = 1:params.max_steps_per_episode
        % epsilon-greedy
        if rand()<agent.epsilon
            a = randi(agent.action_dim);
        else
            q = forward_q(agent.q_weights, s);
            [~,a] = max(q);
        end
        [s2,r,done,~] = env.step(a);
        store_exp(s,a,r,s2,done);
        if mod(step, params.update_frequency)==0
            loss_val = update_net();
            losses(end+1)=loss_val; %#ok<AGROW>
        end
        ep_reward = ep_reward + r;
        s = s2;
        if done, break; end
    end
    results.episode_rewards(ep)=ep_reward;
    results.episode_losses(ep)=mean(losses);
    agent.epsilon = max(agent.epsilon_min, agent.epsilon*agent.epsilon_decay);
end

results.final_avg_reward = mean(results.episode_rewards(max(1,end-9):end));

    function store_exp(s,a,r,s2,d)
        exp = struct('s',s,'a',a,'r',r,'s2',s2,'d',d);
        if numel(agent.memory)<agent.memory_size
            agent.memory{end+1}=exp;
        else
            agent.memory{agent.memory_idx}=exp;
            agent.memory_idx = mod(agent.memory_idx,agent.memory_size)+1;
        end
    end

    function loss_val = update_net()
        if numel(agent.memory)<params.batch_size, loss_val=0;return;end
        idx = randperm(numel(agent.memory),params.batch_size);
        grad = zero_like(agent.q_weights); loss_sum=0;
        for i=1:params.batch_size
            e=agent.memory{idx(i)};
            q_pred = forward_q(agent.q_weights, e.s);
            q_val = q_pred(e.a);
            if e.d
                tgt = e.r;
            else
                tgt = e.r + agent.gamma*max(forward_q(agent.q_weights,e.s2));
            end
            diff = q_val - tgt; loss_sum = loss_sum + diff^2;
            grad_out = zeros(agent.action_dim,1); grad_out(e.a)=2*diff;
            [g,~]=backward_fc(agent.q_weights,e.s,grad_out);
            grad = add_grad(grad,g);
        end
        grad = scale_grad(grad,1/params.batch_size);
        agent.q_weights = apply_grad(agent.q_weights, grad, agent.learning_rate);
        loss_val = loss_sum/params.batch_size;
    end
end

% ---------- Helper functions ----------
function W = init_weights(in_dim, hid_dim, out_dim)
W.W1 = randn(hid_dim,in_dim)*sqrt(2/in_dim);
W.b1 = zeros(hid_dim,1);
W.W2 = randn(out_dim,hid_dim)*sqrt(2/hid_dim);
W.b2 = zeros(out_dim,1);
end

function q = forward_q(W,s)
 a1 = relu(W.W1*s + W.b1);
 q = W.W2*a1 + W.b2;
end

function [grads, grad_in] = backward_fc(W, s, grad_out)
 z1 = W.W1*s + W.b1; a1 = relu(z1);
 grads.W2 = grad_out * a1'; grads.b2 = grad_out;
 grad_a1 = W.W2' * grad_out; grad_z1 = grad_a1 .* (z1>0);
 grads.W1 = grad_z1 * s'; grads.b1 = grad_z1;
 grad_in = W.W1' * grad_z1;
end
function y = relu(x), y=max(0,x); end

function z = zero_like(W)
 f=fieldnames(W); for k=1:numel(f), z.(f{k})=zeros(size(W.(f{k}))); end
end
function out=add_grad(a,b)
 f=fieldnames(a); for k=1:numel(f), out.(f{k})=a.(f{k})+b.(f{k}); end
end
function out=scale_grad(g,fct)
 f=fieldnames(g); for k=1:numel(f), out.(f{k})=g.(f{k})*fct; end
end
function W=apply_grad(W,g,lr)
 f=fieldnames(W); for k=1:numel(f), W.(f{k}) = W.(f{k}) - lr * g.(f{k}); end
end 