% 调试RL算法性能问题
% 深入分析为什么RL算法性能不如预期

clear; clc; close all;

fprintf('=== 调试RL算法性能问题 ===\n');

%% 1. 初始化系统
fprintf('1. 初始化系统...\n');
env = rl_environment();
agent = hierarchical_agent();

%% 2. 分析初始状态
fprintf('2. 分析初始状态...\n');
state = env.reset();
fprintf('初始功率等级: %d (%.1f dBm)\n', env.current_power, env.power_levels(env.current_power));

% 测试不同功率等级的性能
fprintf('\n测试不同功率等级的性能:\n');
power_levels = env.power_levels;
for i = 1:length(power_levels)
    env_test = rl_environment();
    env_test.reset();
    
    total_energy = 0;
    total_pdr = 0;
    total_delay = 0;
    
    for step = 1:50
        [~, ~, done, info] = env_test.step(i);
        if done
            break;
        end
    end
    
    fprintf('  功率 %.1f dBm: 能耗=%.1f mJ, PDR=%.3f, 延迟=%.1f ms\n', ...
            power_levels(i), env_test.energy_consumption, env_test.pdr, env_test.avg_delay);
end

%% 3. 训练过程分析
fprintf('\n3. 详细训练过程分析...\n');

% 记录详细训练数据
training_details = struct();
training_details.episode_rewards = [];
training_details.episode_energies = [];
training_details.episode_pdrs = [];
training_details.power_choices = [];
training_details.meta_actions = [];

num_episodes = 30;
for episode = 1:num_episodes
    state = env.reset();
    episode_reward = 0;
    episode_power_choices = [];
    episode_meta_actions = [];
    
    for step = 1:100
        % 选择动作
        meta_action = agent.select_meta_action(state);
        action = agent.select_local_action(state, meta_action);
        
        % 记录选择
        episode_power_choices = [episode_power_choices, action];
        episode_meta_actions = [episode_meta_actions; meta_action];
        
        % 执行动作
        [next_state, reward, done, info] = env.step(action);
        
        % 存储经验
        agent.store_meta_experience(state, meta_action, reward, next_state, done);
        agent.store_local_experience(state, meta_action, action, reward, next_state, done);
        
        % 训练
        if step > 20
            agent.train_meta_agent();
            agent.train_local_agent();
        end
        
        % 更新
        state = next_state;
        episode_reward = episode_reward + reward;
        
        if done
            break;
        end
        
        % 更新目标网络
        agent.update_target_networks();
    end
    
    % 衰减探索率
    agent.decay_epsilon();
    
    % 记录数据
    training_details.episode_rewards = [training_details.episode_rewards, episode_reward];
    training_details.episode_energies = [training_details.episode_energies, env.energy_consumption];
    training_details.episode_pdrs = [training_details.episode_pdrs, env.pdr];
    training_details.power_choices = [training_details.power_choices; episode_power_choices];
    
    if mod(episode, 5) == 0
        avg_power = mean(power_levels(episode_power_choices));
        low_power_ratio = sum(episode_power_choices <= 2) / length(episode_power_choices);
        
        fprintf('  Episode %d: 奖励=%.1f, 能耗=%.1f, PDR=%.3f, 平均功率=%.1f dBm, 低功率比例=%.1f%%\n', ...
                episode, episode_reward, env.energy_consumption, env.pdr, avg_power, low_power_ratio*100);
    end
end

%% 4. 最终测试 - 纯利用模式
fprintf('\n4. 最终测试 (纯利用模式)...\n');

% 关闭探索
agent.meta_epsilon = 0;
agent.local_epsilon = 0;

% 测试多次取平均
num_tests = 5;
test_results = struct();
test_results.energies = [];
test_results.pdrs = [];
test_results.delays = [];
test_results.power_choices = [];

for test = 1:num_tests
    env_test = rl_environment();
    state = env_test.reset();
    test_power_choices = [];
    
    for step = 1:100
        meta_action = agent.select_meta_action(state);
        action = agent.select_local_action(state, meta_action);
        test_power_choices = [test_power_choices, action];
        
        [next_state, ~, done, ~] = env_test.step(action);
        state = next_state;
        
        if done
            break;
        end
    end
    
    test_results.energies = [test_results.energies, env_test.energy_consumption];
    test_results.pdrs = [test_results.pdrs, env_test.pdr];
    test_results.delays = [test_results.delays, env_test.avg_delay];
    test_results.power_choices = [test_results.power_choices; test_power_choices];
end

% 计算平均性能
avg_energy = mean(test_results.energies);
avg_pdr = mean(test_results.pdrs);
avg_delay = mean(test_results.delays);

fprintf('平均性能 (5次测试):\n');
fprintf('  能耗: %.1f ± %.1f mJ\n', avg_energy, std(test_results.energies));
fprintf('  PDR: %.3f ± %.3f\n', avg_pdr, std(test_results.pdrs));
fprintf('  延迟: %.1f ± %.1f ms\n', avg_delay, std(test_results.delays));

% 分析功率选择
all_power_choices = test_results.power_choices(:);
power_distribution = histcounts(all_power_choices, 1:7) / length(all_power_choices);
avg_power = mean(power_levels(all_power_choices));

fprintf('\n功率选择分析:\n');
fprintf('  平均功率: %.1f dBm\n', avg_power);
for i = 1:6
    fprintf('  %.1f dBm: %.1f%%\n', power_levels(i), power_distribution(i)*100);
end

%% 5. 与基准比较
fprintf('\n5. 与基准算法比较...\n');

% 固定功率基准
env_baseline = rl_environment();
env_baseline.reset();
for step = 1:100
    [~, ~, done, ~] = env_baseline.step(5); % 0dBm
    if done; break; end
end

baseline_energy = env_baseline.energy_consumption;
baseline_pdr = env_baseline.pdr;
baseline_delay = env_baseline.avg_delay;

% 计算改进
energy_improvement = (baseline_energy - avg_energy) / baseline_energy * 100;
pdr_improvement = (avg_pdr - baseline_pdr) / baseline_pdr * 100;
delay_improvement = (baseline_delay - avg_delay) / baseline_delay * 100;

fprintf('与固定功率(0dBm)比较:\n');
fprintf('  基准: 能耗=%.1f, PDR=%.3f, 延迟=%.1f\n', baseline_energy, baseline_pdr, baseline_delay);
fprintf('  RL:   能耗=%.1f, PDR=%.3f, 延迟=%.1f\n', avg_energy, avg_pdr, avg_delay);
fprintf('  改进: 能耗%.1f%%, PDR%.1f%%, 延迟%.1f%%\n', energy_improvement, pdr_improvement, delay_improvement);

%% 6. 可视化分析
fprintf('\n6. 生成分析图表...\n');

figure('Position', [100, 100, 1200, 800]);

% 训练过程
subplot(2,3,1);
plot(training_details.episode_energies, 'b-o', 'LineWidth', 2);
title('训练过程 - 能耗');
xlabel('Episode');
ylabel('能耗 (mJ)');
grid on;

subplot(2,3,2);
plot(training_details.episode_pdrs, 'r-s', 'LineWidth', 2);
title('训练过程 - PDR');
xlabel('Episode');
ylabel('PDR');
grid on;

subplot(2,3,3);
plot(training_details.episode_rewards, 'g-^', 'LineWidth', 2);
title('训练过程 - 奖励');
xlabel('Episode');
ylabel('累积奖励');
grid on;

% 功率选择分布
subplot(2,3,4);
histogram(all_power_choices, 1:7, 'FaceColor', 'cyan', 'EdgeColor', 'black');
title('功率选择分布');
xlabel('功率等级');
ylabel('选择次数');
xticks(1:6);
xticklabels(arrayfun(@(x) sprintf('%.0fdBm', x), power_levels, 'UniformOutput', false));
grid on;

% 性能对比
subplot(2,3,5);
algorithms = {'固定功率', 'RL算法'};
energies = [baseline_energy, avg_energy];
bar(energies, 'FaceColor', [0.7, 0.3, 0.3]);
set(gca, 'XTickLabel', algorithms);
title('能耗对比');
ylabel('能耗 (mJ)');
grid on;

subplot(2,3,6);
pdrs = [baseline_pdr, avg_pdr];
bar(pdrs, 'FaceColor', [0.3, 0.7, 0.3]);
set(gca, 'XTickLabel', algorithms);
title('PDR对比');
ylabel('PDR');
grid on;

sgtitle('RL算法调试分析结果', 'FontSize', 16);
saveas(gcf, 'debug_rl_analysis.png');

fprintf('✓ 调试完成\n');

%% 7. 问题诊断
fprintf('\n=== 问题诊断 ===\n');
if energy_improvement > 5
    fprintf('✓ 算法工作正常，实现了显著节能\n');
elseif energy_improvement > 0
    fprintf('⚠ 算法有轻微改进，但可能需要更多训练\n');
else
    fprintf('✗ 算法存在问题，需要进一步调试\n');
    fprintf('可能原因:\n');
    fprintf('1. 奖励函数设计不当\n');
    fprintf('2. 网络结构或学习率问题\n');
    fprintf('3. 探索策略不合适\n');
    fprintf('4. 训练步数不足\n');
end

if avg_power < -10
    fprintf('✓ 算法学会了选择低功率\n');
else
    fprintf('⚠ 算法倾向于选择较高功率\n');
end
