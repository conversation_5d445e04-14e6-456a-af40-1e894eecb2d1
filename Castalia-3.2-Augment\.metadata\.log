!SESSION 2025-06-08 09:23:42.324 -----------------------------------------------
eclipse.buildId=unknown
java.version=1.7.0_51
java.vendor=Oracle Corporation
BootLoader constants: OS=win32, ARCH=x86, WS=win32, NL=zh_CN
Command-line arguments:  -os win32 -ws win32 -arch x86

!ENTRY org.omnetpp.main 1 0 2025-06-08 09:24:07.816
!MESSAGE OMNeT++ IDE 4.6.0.141202-f785492 started.

!ENTRY org.omnetpp.main 4 0 2025-06-08 09:24:34.238
!MESSAGE Cannot download project description from http://omnetpp.org/download/contrib/descriptors/omnetpp-4.6/inet.xml
!STACK 0
java.io.IOException: Server returned HTTP response code: 403 for URL: http://omnetpp.org/download/contrib/descriptors/omnetpp-4.6/inet.xml
	at sun.net.www.protocol.http.HttpURLConnection.getInputStream(Unknown Source)
	at java.net.URL.openStream(Unknown Source)
	at org.apache.commons.io.FileUtils.copyURLToFile(FileUtils.java:1460)
	at org.omnetpp.ide.installer.InstallProjectTask.downloadProjectDescription(InstallProjectTask.java:59)
	at org.omnetpp.ide.installer.InstallProjectTask.run(InstallProjectTask.java:46)
	at org.omnetpp.ide.installer.InstallProjectJob.run(InstallProjectJob.java:32)
	at org.eclipse.core.internal.jobs.Worker.run(Worker.java:54)

!ENTRY org.eclipse.cdt.core 1 0 2025-06-08 09:25:15.976
!MESSAGE Indexed 'tictoc' (20 sources, 336 headers) in 4.43 sec: 43,175 declarations; 68,843 references; 0 unresolved inclusions; 0 syntax errors; 8 unresolved names (0.0071%)

!ENTRY org.eclipse.e4.ui.workbench.swt 4 2 2025-06-08 09:25:33.785
!MESSAGE Problems occurred when invoking code from plug-in: "org.eclipse.e4.ui.workbench.swt".
!STACK 0
org.eclipse.e4.core.di.InjectionException: org.eclipse.swt.SWTException: Widget is disposed
	at org.eclipse.e4.core.internal.di.MethodRequestor.execute(MethodRequestor.java:62)
	at org.eclipse.e4.core.internal.di.InjectorImpl.processAnnotated(InjectorImpl.java:888)
	at org.eclipse.e4.core.internal.di.InjectorImpl.disposed(InjectorImpl.java:390)
	at org.eclipse.e4.core.internal.di.Requestor.disposed(Requestor.java:143)
	at org.eclipse.e4.core.internal.contexts.ContextObjectSupplier$ContextInjectionListener.update(ContextObjectSupplier.java:76)
	at org.eclipse.e4.core.internal.contexts.TrackableComputationExt.update(TrackableComputationExt.java:107)
	at org.eclipse.e4.core.internal.contexts.TrackableComputationExt.handleInvalid(TrackableComputationExt.java:70)
	at org.eclipse.e4.core.internal.contexts.EclipseContext.dispose(EclipseContext.java:175)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.clearContext(PartRenderingEngine.java:974)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.safeRemoveGui(PartRenderingEngine.java:954)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.access$3(PartRenderingEngine.java:862)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine$8.run(PartRenderingEngine.java:857)
	at org.eclipse.core.runtime.SafeRunner.run(SafeRunner.java:42)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.removeGui(PartRenderingEngine.java:841)
	at org.eclipse.ui.internal.WorkbenchWindow.hardClose(WorkbenchWindow.java:1924)
	at org.eclipse.ui.internal.WorkbenchWindow.busyClose(WorkbenchWindow.java:1547)
	at org.eclipse.ui.internal.WorkbenchWindow.access$15(WorkbenchWindow.java:1514)
	at org.eclipse.ui.internal.WorkbenchWindow$10.run(WorkbenchWindow.java:1579)
	at org.eclipse.swt.custom.BusyIndicator.showWhile(BusyIndicator.java:70)
	at org.eclipse.ui.internal.WorkbenchWindow.close(WorkbenchWindow.java:1576)
	at org.eclipse.ui.internal.Workbench$14.run(Workbench.java:1155)
	at org.eclipse.core.runtime.SafeRunner.run(SafeRunner.java:42)
	at org.eclipse.ui.internal.Workbench.busyClose(Workbench.java:1137)
	at org.eclipse.ui.internal.Workbench.access$21(Workbench.java:1079)
	at org.eclipse.ui.internal.Workbench$19.run(Workbench.java:1410)
	at org.eclipse.swt.custom.BusyIndicator.showWhile(BusyIndicator.java:70)
	at org.eclipse.ui.internal.Workbench.close(Workbench.java:1407)
	at org.eclipse.ui.internal.Workbench.close(Workbench.java:1380)
	at org.eclipse.ui.internal.WorkbenchWindow.busyClose(WorkbenchWindow.java:1543)
	at org.eclipse.ui.internal.WorkbenchWindow.access$15(WorkbenchWindow.java:1514)
	at org.eclipse.ui.internal.WorkbenchWindow$10.run(WorkbenchWindow.java:1579)
	at org.eclipse.swt.custom.BusyIndicator.showWhile(BusyIndicator.java:70)
	at org.eclipse.ui.internal.WorkbenchWindow.close(WorkbenchWindow.java:1576)
	at org.eclipse.ui.internal.WorkbenchWindow.close(WorkbenchWindow.java:1590)
	at org.eclipse.ui.internal.WorkbenchWindow$6.close(WorkbenchWindow.java:520)
	at org.eclipse.e4.ui.workbench.renderers.swt.WBWRenderer$11.shellClosed(WBWRenderer.java:564)
	at org.eclipse.swt.widgets.TypedListener.handleEvent(TypedListener.java:98)
	at org.eclipse.swt.widgets.EventTable.sendEvent(EventTable.java:84)
	at org.eclipse.swt.widgets.Display.sendEvent(Display.java:4353)
	at org.eclipse.swt.widgets.Widget.sendEvent(Widget.java:1061)
	at org.eclipse.swt.widgets.Widget.sendEvent(Widget.java:1085)
	at org.eclipse.swt.widgets.Widget.sendEvent(Widget.java:1070)
	at org.eclipse.swt.widgets.Decorations.closeWidget(Decorations.java:308)
	at org.eclipse.swt.widgets.Decorations.WM_CLOSE(Decorations.java:1696)
	at org.eclipse.swt.widgets.Control.windowProc(Control.java:4612)
	at org.eclipse.swt.widgets.Canvas.windowProc(Canvas.java:339)
	at org.eclipse.swt.widgets.Decorations.windowProc(Decorations.java:1626)
	at org.eclipse.swt.widgets.Shell.windowProc(Shell.java:2075)
	at org.eclipse.swt.widgets.Display.windowProc(Display.java:5023)
	at org.eclipse.swt.internal.win32.OS.DefWindowProcW(Native Method)
	at org.eclipse.swt.internal.win32.OS.DefWindowProc(OS.java:2544)
	at org.eclipse.swt.widgets.Shell.callWindowProc(Shell.java:498)
	at org.eclipse.swt.widgets.Control.windowProc(Control.java:4705)
	at org.eclipse.swt.widgets.Canvas.windowProc(Canvas.java:339)
	at org.eclipse.swt.widgets.Decorations.windowProc(Decorations.java:1626)
	at org.eclipse.swt.widgets.Shell.windowProc(Shell.java:2075)
	at org.eclipse.swt.widgets.Display.windowProc(Display.java:5023)
	at org.eclipse.swt.internal.win32.OS.DefWindowProcW(Native Method)
	at org.eclipse.swt.internal.win32.OS.DefWindowProc(OS.java:2544)
	at org.eclipse.swt.widgets.Shell.callWindowProc(Shell.java:498)
	at org.eclipse.swt.widgets.Control.windowProc(Control.java:4705)
	at org.eclipse.swt.widgets.Canvas.windowProc(Canvas.java:339)
	at org.eclipse.swt.widgets.Decorations.windowProc(Decorations.java:1626)
	at org.eclipse.swt.widgets.Shell.windowProc(Shell.java:2075)
	at org.eclipse.swt.widgets.Display.windowProc(Display.java:5023)
	at org.eclipse.swt.internal.win32.OS.DispatchMessageW(Native Method)
	at org.eclipse.swt.internal.win32.OS.DispatchMessage(OS.java:2549)
	at org.eclipse.swt.widgets.Display.readAndDispatch(Display.java:3759)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine$9.run(PartRenderingEngine.java:1151)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:332)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.run(PartRenderingEngine.java:1032)
	at org.eclipse.e4.ui.internal.workbench.E4Workbench.createAndRunUI(E4Workbench.java:148)
	at org.eclipse.ui.internal.Workbench$5.run(Workbench.java:636)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:332)
	at org.eclipse.ui.internal.Workbench.createAndRunWorkbench(Workbench.java:579)
	at org.eclipse.ui.PlatformUI.createAndRunWorkbench(PlatformUI.java:150)
	at org.eclipse.ui.internal.ide.application.IDEApplication.start(IDEApplication.java:135)
	at org.eclipse.equinox.internal.app.EclipseAppHandle.run(EclipseAppHandle.java:196)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.runApplication(EclipseAppLauncher.java:134)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.start(EclipseAppLauncher.java:104)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:382)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:236)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.eclipse.equinox.launcher.Main.invokeFramework(Main.java:648)
	at org.eclipse.equinox.launcher.Main.basicRun(Main.java:603)
	at org.eclipse.equinox.launcher.Main.run(Main.java:1465)
Caused by: org.eclipse.swt.SWTException: Widget is disposed
	at org.eclipse.swt.SWT.error(SWT.java:4441)
	at org.eclipse.swt.SWT.error(SWT.java:4356)
	at org.eclipse.swt.SWT.error(SWT.java:4327)
	at org.eclipse.swt.widgets.Widget.error(Widget.java:476)
	at org.eclipse.swt.widgets.Widget.checkWidget(Widget.java:348)
	at org.eclipse.swt.widgets.Shell.getSize(Shell.java:1092)
	at org.eclipse.ui.internal.quickaccess.SearchField.storeDialog(SearchField.java:580)
	at org.eclipse.ui.internal.quickaccess.SearchField.dispose(SearchField.java:557)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.eclipse.e4.core.internal.di.MethodRequestor.execute(MethodRequestor.java:55)
	... 88 more

!ENTRY org.eclipse.e4.ui.workbench 4 0 2025-06-08 09:25:33.786
!MESSAGE Exception occurred while unrendering: org.eclipse.e4.ui.model.application.ui.basic.impl.TrimmedWindowImpl@1d304ac (elementId: IDEWindow, tags: [topLevel, shellMaximized], contributorURI: platform:/plugin/org.eclipse.ui.workbench) (widget: null, renderer: null, toBeRendered: true, onTop: false, visible: true, containerData: null, accessibilityPhrase: null) (label: %trimmedwindow.label.eclipseSDK, iconURI: null, tooltip: null, context: null, variables: [], x: 64, y: 64, width: 1024, height: 768)
!STACK 0
org.eclipse.e4.core.di.InjectionException: org.eclipse.swt.SWTException: Widget is disposed
	at org.eclipse.e4.core.internal.di.MethodRequestor.execute(MethodRequestor.java:62)
	at org.eclipse.e4.core.internal.di.InjectorImpl.processAnnotated(InjectorImpl.java:888)
	at org.eclipse.e4.core.internal.di.InjectorImpl.disposed(InjectorImpl.java:390)
	at org.eclipse.e4.core.internal.di.Requestor.disposed(Requestor.java:143)
	at org.eclipse.e4.core.internal.contexts.ContextObjectSupplier$ContextInjectionListener.update(ContextObjectSupplier.java:76)
	at org.eclipse.e4.core.internal.contexts.TrackableComputationExt.update(TrackableComputationExt.java:107)
	at org.eclipse.e4.core.internal.contexts.TrackableComputationExt.handleInvalid(TrackableComputationExt.java:70)
	at org.eclipse.e4.core.internal.contexts.EclipseContext.dispose(EclipseContext.java:175)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.clearContext(PartRenderingEngine.java:974)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.safeRemoveGui(PartRenderingEngine.java:954)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.access$3(PartRenderingEngine.java:862)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine$8.run(PartRenderingEngine.java:857)
	at org.eclipse.core.runtime.SafeRunner.run(SafeRunner.java:42)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.removeGui(PartRenderingEngine.java:841)
	at org.eclipse.ui.internal.WorkbenchWindow.hardClose(WorkbenchWindow.java:1924)
	at org.eclipse.ui.internal.WorkbenchWindow.busyClose(WorkbenchWindow.java:1547)
	at org.eclipse.ui.internal.WorkbenchWindow.access$15(WorkbenchWindow.java:1514)
	at org.eclipse.ui.internal.WorkbenchWindow$10.run(WorkbenchWindow.java:1579)
	at org.eclipse.swt.custom.BusyIndicator.showWhile(BusyIndicator.java:70)
	at org.eclipse.ui.internal.WorkbenchWindow.close(WorkbenchWindow.java:1576)
	at org.eclipse.ui.internal.Workbench$14.run(Workbench.java:1155)
	at org.eclipse.core.runtime.SafeRunner.run(SafeRunner.java:42)
	at org.eclipse.ui.internal.Workbench.busyClose(Workbench.java:1137)
	at org.eclipse.ui.internal.Workbench.access$21(Workbench.java:1079)
	at org.eclipse.ui.internal.Workbench$19.run(Workbench.java:1410)
	at org.eclipse.swt.custom.BusyIndicator.showWhile(BusyIndicator.java:70)
	at org.eclipse.ui.internal.Workbench.close(Workbench.java:1407)
	at org.eclipse.ui.internal.Workbench.close(Workbench.java:1380)
	at org.eclipse.ui.internal.WorkbenchWindow.busyClose(WorkbenchWindow.java:1543)
	at org.eclipse.ui.internal.WorkbenchWindow.access$15(WorkbenchWindow.java:1514)
	at org.eclipse.ui.internal.WorkbenchWindow$10.run(WorkbenchWindow.java:1579)
	at org.eclipse.swt.custom.BusyIndicator.showWhile(BusyIndicator.java:70)
	at org.eclipse.ui.internal.WorkbenchWindow.close(WorkbenchWindow.java:1576)
	at org.eclipse.ui.internal.WorkbenchWindow.close(WorkbenchWindow.java:1590)
	at org.eclipse.ui.internal.WorkbenchWindow$6.close(WorkbenchWindow.java:520)
	at org.eclipse.e4.ui.workbench.renderers.swt.WBWRenderer$11.shellClosed(WBWRenderer.java:564)
	at org.eclipse.swt.widgets.TypedListener.handleEvent(TypedListener.java:98)
	at org.eclipse.swt.widgets.EventTable.sendEvent(EventTable.java:84)
	at org.eclipse.swt.widgets.Display.sendEvent(Display.java:4353)
	at org.eclipse.swt.widgets.Widget.sendEvent(Widget.java:1061)
	at org.eclipse.swt.widgets.Widget.sendEvent(Widget.java:1085)
	at org.eclipse.swt.widgets.Widget.sendEvent(Widget.java:1070)
	at org.eclipse.swt.widgets.Decorations.closeWidget(Decorations.java:308)
	at org.eclipse.swt.widgets.Decorations.WM_CLOSE(Decorations.java:1696)
	at org.eclipse.swt.widgets.Control.windowProc(Control.java:4612)
	at org.eclipse.swt.widgets.Canvas.windowProc(Canvas.java:339)
	at org.eclipse.swt.widgets.Decorations.windowProc(Decorations.java:1626)
	at org.eclipse.swt.widgets.Shell.windowProc(Shell.java:2075)
	at org.eclipse.swt.widgets.Display.windowProc(Display.java:5023)
	at org.eclipse.swt.internal.win32.OS.DefWindowProcW(Native Method)
	at org.eclipse.swt.internal.win32.OS.DefWindowProc(OS.java:2544)
	at org.eclipse.swt.widgets.Shell.callWindowProc(Shell.java:498)
	at org.eclipse.swt.widgets.Control.windowProc(Control.java:4705)
	at org.eclipse.swt.widgets.Canvas.windowProc(Canvas.java:339)
	at org.eclipse.swt.widgets.Decorations.windowProc(Decorations.java:1626)
	at org.eclipse.swt.widgets.Shell.windowProc(Shell.java:2075)
	at org.eclipse.swt.widgets.Display.windowProc(Display.java:5023)
	at org.eclipse.swt.internal.win32.OS.DefWindowProcW(Native Method)
	at org.eclipse.swt.internal.win32.OS.DefWindowProc(OS.java:2544)
	at org.eclipse.swt.widgets.Shell.callWindowProc(Shell.java:498)
	at org.eclipse.swt.widgets.Control.windowProc(Control.java:4705)
	at org.eclipse.swt.widgets.Canvas.windowProc(Canvas.java:339)
	at org.eclipse.swt.widgets.Decorations.windowProc(Decorations.java:1626)
	at org.eclipse.swt.widgets.Shell.windowProc(Shell.java:2075)
	at org.eclipse.swt.widgets.Display.windowProc(Display.java:5023)
	at org.eclipse.swt.internal.win32.OS.DispatchMessageW(Native Method)
	at org.eclipse.swt.internal.win32.OS.DispatchMessage(OS.java:2549)
	at org.eclipse.swt.widgets.Display.readAndDispatch(Display.java:3759)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine$9.run(PartRenderingEngine.java:1151)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:332)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.run(PartRenderingEngine.java:1032)
	at org.eclipse.e4.ui.internal.workbench.E4Workbench.createAndRunUI(E4Workbench.java:148)
	at org.eclipse.ui.internal.Workbench$5.run(Workbench.java:636)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:332)
	at org.eclipse.ui.internal.Workbench.createAndRunWorkbench(Workbench.java:579)
	at org.eclipse.ui.PlatformUI.createAndRunWorkbench(PlatformUI.java:150)
	at org.eclipse.ui.internal.ide.application.IDEApplication.start(IDEApplication.java:135)
	at org.eclipse.equinox.internal.app.EclipseAppHandle.run(EclipseAppHandle.java:196)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.runApplication(EclipseAppLauncher.java:134)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.start(EclipseAppLauncher.java:104)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:382)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:236)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.eclipse.equinox.launcher.Main.invokeFramework(Main.java:648)
	at org.eclipse.equinox.launcher.Main.basicRun(Main.java:603)
	at org.eclipse.equinox.launcher.Main.run(Main.java:1465)
Caused by: org.eclipse.swt.SWTException: Widget is disposed
	at org.eclipse.swt.SWT.error(SWT.java:4441)
	at org.eclipse.swt.SWT.error(SWT.java:4356)
	at org.eclipse.swt.SWT.error(SWT.java:4327)
	at org.eclipse.swt.widgets.Widget.error(Widget.java:476)
	at org.eclipse.swt.widgets.Widget.checkWidget(Widget.java:348)
	at org.eclipse.swt.widgets.Shell.getSize(Shell.java:1092)
	at org.eclipse.ui.internal.quickaccess.SearchField.storeDialog(SearchField.java:580)
	at org.eclipse.ui.internal.quickaccess.SearchField.dispose(SearchField.java:557)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.eclipse.e4.core.internal.di.MethodRequestor.execute(MethodRequestor.java:55)
	... 88 more
!SESSION 2025-06-08 09:25:40.154 -----------------------------------------------
eclipse.buildId=unknown
java.version=1.7.0_51
java.vendor=Oracle Corporation
BootLoader constants: OS=win32, ARCH=x86, WS=win32, NL=zh_CN
Command-line arguments:  -os win32 -ws win32 -arch x86

!ENTRY org.omnetpp.main 1 0 2025-06-08 09:26:04.151
!MESSAGE OMNeT++ IDE 4.6.0.141202-f785492 started.
