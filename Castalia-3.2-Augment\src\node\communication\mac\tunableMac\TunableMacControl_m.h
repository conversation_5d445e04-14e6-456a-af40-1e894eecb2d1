//
// Generated file, do not edit! Created by nedtool 4.6 from src/node/communication/mac/tunableMac/TunableMacControl.msg.
//

#ifndef _TUNABLEMACCONTROL_M_H_
#define _TUNABLEMACCONTROL_M_H_

#include <omnetpp.h>

// nedtool version check
#define MSGC_VERSION 0x0406
#if (MSGC_VERSION!=OMNETPP_VERSION)
#    error Version mismatch! Probably this file was generated by an earlier version of nedtool: 'make clean' should help.
#endif



/**
 * Enum generated from <tt>src/node/communication/mac/tunableMac/TunableMacControl.msg:13</tt> by nedtool.
 * <pre>
 * enum TunableMacCommandDef
 * {
 * 
 *     SET_DUTY_CYCLE = 1;
 *     SET_LISTEN_INTERVAL = 2;
 *     SET_BEACON_INTERVAL_FRACTION = 3;
 *     SET_PROB_TX = 4;
 *     SET_NUM_TX = 5;
 *     SET_RANDOM_TX_OFFSET = 6;
 *     SET_RETX_INTERVAL = 7;
 *     SET_BACKOFF_TYPE = 8;
 *     SET_BACKOFF_BASE_VALUE = 9;
 * }
 * </pre>
 */
enum TunableMacCommandDef {
    SET_DUTY_CYCLE = 1,
    SET_LISTEN_INTERVAL = 2,
    SET_BEACON_INTERVAL_FRACTION = 3,
    SET_PROB_TX = 4,
    SET_NUM_TX = 5,
    SET_RANDOM_TX_OFFSET = 6,
    SET_RETX_INTERVAL = 7,
    SET_BACKOFF_TYPE = 8,
    SET_BACKOFF_BASE_VALUE = 9
};

/**
 * Class generated from <tt>src/node/communication/mac/tunableMac/TunableMacControl.msg:25</tt> by nedtool.
 * <pre>
 * message TunableMacControlCommand
 * {
 *     int tunableMacCommandKind @enum(TunableMacCommandDef);
 *     double parameter = 0;
 * }
 * </pre>
 */
class TunableMacControlCommand : public ::cMessage
{
  protected:
    int tunableMacCommandKind_var;
    double parameter_var;

  private:
    void copy(const TunableMacControlCommand& other);

  protected:
    // protected and unimplemented operator==(), to prevent accidental usage
    bool operator==(const TunableMacControlCommand&);

  public:
    TunableMacControlCommand(const char *name=NULL, int kind=0);
    TunableMacControlCommand(const TunableMacControlCommand& other);
    virtual ~TunableMacControlCommand();
    TunableMacControlCommand& operator=(const TunableMacControlCommand& other);
    virtual TunableMacControlCommand *dup() const {return new TunableMacControlCommand(*this);}
    virtual void parsimPack(cCommBuffer *b);
    virtual void parsimUnpack(cCommBuffer *b);

    // field getter/setter methods
    virtual int getTunableMacCommandKind() const;
    virtual void setTunableMacCommandKind(int tunableMacCommandKind);
    virtual double getParameter() const;
    virtual void setParameter(double parameter);
};

inline void doPacking(cCommBuffer *b, TunableMacControlCommand& obj) {obj.parsimPack(b);}
inline void doUnpacking(cCommBuffer *b, TunableMacControlCommand& obj) {obj.parsimUnpack(b);}


#endif // ifndef _TUNABLEMACCONTROL_M_H_

