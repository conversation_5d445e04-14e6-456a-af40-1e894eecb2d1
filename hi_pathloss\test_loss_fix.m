% 测试损失修复的脚本
function test_loss_fix()
    fprintf('=== 测试损失修复 ===\n');
    
    % 创建环境和场景
    env = rl_environment();
    scenario = struct('name', 'test_loss', 'type', 'static');
    
    fprintf('开始训练（修复后）...\n');
    
    % 运行训练
    [agent, results] = train_hierarchical_rl(env, scenario);
    
    % 分析结果
    analyze_loss_results(results);
    
    % 绘制损失曲线
    plot_loss_curves(results);
    
    fprintf('损失修复测试完成!\n');
end

function analyze_loss_results(results)
    fprintf('\n=== 损失分析结果 ===\n');
    
    if isfield(results, 'episode_losses') && ~isempty(results.episode_losses)
        losses = results.episode_losses;
        
        % 基本统计
        fprintf('损失统计:\n');
        fprintf('  总轮数: %d\n', length(losses));
        fprintf('  初始损失: %.4f\n', losses(1));
        fprintf('  最终损失: %.4f\n', losses(end));
        fprintf('  平均损失: %.4f\n', mean(losses));
        fprintf('  最大损失: %.4f\n', max(losses));
        fprintf('  最小损失: %.4f\n', min(losses));
        
        % 检查损失趋势
        if length(losses) > 10
            early_avg = mean(losses(1:5));
            late_avg = mean(losses(end-4:end));
            improvement = (early_avg - late_avg) / early_avg * 100;
            
            fprintf('  前5轮平均: %.4f\n', early_avg);
            fprintf('  后5轮平均: %.4f\n', late_avg);
            fprintf('  改进百分比: %.2f%%\n', improvement);
            
            if improvement > 0
                fprintf('  ✓ 损失呈下降趋势\n');
            else
                fprintf('  ⚠ 损失未明显下降\n');
            end
        end
        
        % 检查零损失问题
        zero_count = sum(losses == 0);
        if zero_count > 0
            fprintf('  ⚠ 发现 %d 轮损失为0\n', zero_count);
        else
            fprintf('  ✓ 无零损失问题\n');
        end
        
    else
        fprintf('⚠ 未找到损失数据\n');
    end
    
    % 奖励分析
    if isfield(results, 'episode_rewards') && ~isempty(results.episode_rewards)
        rewards = results.episode_rewards;
        
        fprintf('\n奖励统计:\n');
        fprintf('  初始奖励: %.2f\n', rewards(1));
        fprintf('  最终奖励: %.2f\n', rewards(end));
        fprintf('  平均奖励: %.2f\n', mean(rewards));
        
        if length(rewards) > 10
            early_reward = mean(rewards(1:5));
            late_reward = mean(rewards(end-4:end));
            reward_improvement = (late_reward - early_reward) / abs(early_reward) * 100;
            
            fprintf('  奖励改进: %.2f%%\n', reward_improvement);
            
            if reward_improvement > 0
                fprintf('  ✓ 奖励呈上升趋势\n');
            else
                fprintf('  ⚠ 奖励未明显提升\n');
            end
        end
    end
end

function plot_loss_curves(results)
    % 绘制损失和奖励曲线
    
    figure('Name', '损失修复验证', 'Position', [100, 100, 1000, 400]);
    
    % 损失曲线
    subplot(1, 2, 1);
    if isfield(results, 'episode_losses') && ~isempty(results.episode_losses)
        losses = results.episode_losses;
        plot(1:length(losses), losses, 'b-', 'LineWidth', 2);
        title('训练损失曲线');
        xlabel('Episode');
        ylabel('Loss');
        grid on;
        
        % 添加趋势线
        if length(losses) > 5
            x = 1:length(losses);
            p = polyfit(x, losses, 1);
            trend = polyval(p, x);
            hold on;
            plot(x, trend, 'r--', 'LineWidth', 1.5, 'DisplayName', '趋势线');
            legend('实际损失', '趋势线');
            
            if p(1) < 0
                text(0.7*length(losses), 0.8*max(losses), '✓ 下降趋势', ...
                     'FontSize', 12, 'Color', 'green', 'FontWeight', 'bold');
            else
                text(0.7*length(losses), 0.8*max(losses), '⚠ 上升趋势', ...
                     'FontSize', 12, 'Color', 'red', 'FontWeight', 'bold');
            end
        end
    else
        text(0.5, 0.5, '无损失数据', 'HorizontalAlignment', 'center');
        title('训练损失曲线 (无数据)');
    end
    
    % 奖励曲线
    subplot(1, 2, 2);
    if isfield(results, 'episode_rewards') && ~isempty(results.episode_rewards)
        rewards = results.episode_rewards;
        plot(1:length(rewards), rewards, 'g-', 'LineWidth', 2);
        title('训练奖励曲线');
        xlabel('Episode');
        ylabel('Reward');
        grid on;
        
        % 添加趋势线
        if length(rewards) > 5
            x = 1:length(rewards);
            p = polyfit(x, rewards, 1);
            trend = polyval(p, x);
            hold on;
            plot(x, trend, 'r--', 'LineWidth', 1.5, 'DisplayName', '趋势线');
            legend('实际奖励', '趋势线');
            
            if p(1) > 0
                text(0.7*length(rewards), 0.8*max(rewards), '✓ 上升趋势', ...
                     'FontSize', 12, 'Color', 'green', 'FontWeight', 'bold');
            else
                text(0.7*length(rewards), 0.8*max(rewards), '⚠ 下降趋势', ...
                     'FontSize', 12, 'Color', 'red', 'FontWeight', 'bold');
            end
        end
    else
        text(0.5, 0.5, '无奖励数据', 'HorizontalAlignment', 'center');
        title('训练奖励曲线 (无数据)');
    end
    
    % 保存图片
    saveas(gcf, 'loss_fix_verification.png');
    fprintf('损失曲线已保存为 loss_fix_verification.png\n');
end
