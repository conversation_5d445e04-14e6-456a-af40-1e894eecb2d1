% 测试优化后的可视化效果
function test_visualization_optimization()
    close all;
    clear;
    clc;
    
    fprintf('=== 测试优化后的可视化效果 ===\n');
    
    % 模拟实际的实验数据
    results = struct();
    results.energy_results = [
        40.0, 50.0, 40.0;  % 固定功率
        38.0, 33.0, 36.0;  % DQN
        34.6, 35.0, 35.4;  % 演员-评论家
        20.0, 28.9, 24.4   % 分层RL
    ];
    
    results.scenario_names = {'静态监测场景', '动态转换场景', '周期性运动场景'};
    results.algorithm_names = {'固定功率', 'DQN', '演员-评论家', '分层RL'};
    
    % 生成优化后的可视化
    generate_optimized_visualization(results);
    
    fprintf('✓ 优化后的可视化效果已生成\n');
    fprintf('  - 数值标签位置优化\n');
    fprintf('  - 颜色对比度增强\n');
    fprintf('  - 图表样式美化\n');
    fprintf('  - 图例和网格优化\n');
end

function generate_optimized_visualization(results)
    % 生成优化后的可视化
    
    energy_results = results.energy_results;
    scenario_names = results.scenario_names;
    algorithm_names = results.algorithm_names;
    
    % 设置中文字体
    try
        set(0, 'DefaultAxesFontName', 'SimHei');
        set(0, 'DefaultTextFontName', 'SimHei');
    catch
        % 使用默认字体
    end
    
    figure('Position', [100, 100, 1200, 800]);
    
    % 创建分组柱状图
    bar_data = energy_results';
    h = bar(bar_data, 'grouped');
    
    % 设置颜色
    colors = [0.2, 0.6, 0.8;    % 固定功率 - 蓝色
              0.8, 0.4, 0.2;    % DQN - 橙色
              0.9, 0.6, 0.1;    % 演员-评论家 - 黄色
              0.2, 0.8, 0.4];   % 分层RL - 绿色
    
    for i = 1:length(h)
        h(i).FaceColor = colors(i, :);
        h(i).EdgeColor = 'black';
        h(i).LineWidth = 1.2;
    end
    
    % 添加优化的数值标签
    hold on;
    
    % 定义每个算法对应的颜色（用于数值标签）
    label_colors = [0.1, 0.3, 0.6;    % 固定功率 - 深蓝色
                    0.6, 0.2, 0.1;    % 简单DQN - 深橙色
                    0.7, 0.4, 0.05;   % 演员-评论家 - 深黄色
                    0.1, 0.5, 0.2];   % 分层RL - 深绿色
    
    for i = 1:size(bar_data, 1)
        for j = 1:size(bar_data, 2)
            % 计算柱状图的精确位置
            x_pos = i + (j - 2.5) * 0.2;
            y_pos = bar_data(i, j);
            
            % 根据柱子高度调整标签位置
            if y_pos > 45
                % 高柱子：标签放在柱子内部
                label_y = y_pos - 2.5;
                label_color = 'white';
                font_weight = 'bold';
            elseif y_pos > 25
                % 中等柱子：标签放在柱子顶部
                label_y = y_pos + 1.2;
                label_color = label_colors(j, :);
                font_weight = 'bold';
            else
                % 低柱子：标签放在柱子顶部，增加间距
                label_y = y_pos + 1.8;
                label_color = label_colors(j, :);
                font_weight = 'bold';
            end
            
            % 添加数值标签
            text(x_pos, label_y, sprintf('%.1f', y_pos), ...
                'HorizontalAlignment', 'center', ...
                'VerticalAlignment', 'middle', ...
                'FontSize', 10, ...
                'FontWeight', font_weight, ...
                'Color', label_color, ...
                'BackgroundColor', 'none', ...
                'EdgeColor', 'none');
        end
    end
    
    % 优化图表样式
    xlabel('运动场景', 'FontSize', 14, 'FontWeight', 'bold');
    ylabel('平均能耗 (mJ)', 'FontSize', 14, 'FontWeight', 'bold');
    title('最终优化后的算法能耗对比', 'FontSize', 16, 'FontWeight', 'bold');
    
    % 设置X轴标签
    set(gca, 'XTickLabel', scenario_names, 'FontSize', 12);
    
    % 优化图例
    legend(algorithm_names, 'Location', 'northeast', 'FontSize', 11, ...
           'Box', 'on', 'EdgeColor', [0.3, 0.3, 0.3]);
    
    % 设置网格样式
    grid on;
    set(gca, 'GridAlpha', 0.3, 'GridLineStyle', '-', 'GridColor', [0.7, 0.7, 0.7]);
    
    % 设置坐标轴样式
    set(gca, 'FontSize', 11);
    set(gca, 'LineWidth', 1.2);
    
    % 设置Y轴范围，确保所有数值标签可见
    ylim([0, max(bar_data(:)) + 5]);
    
    % 优化整体外观
    set(gca, 'Box', 'on');
    set(gcf, 'Color', 'white');
    
    % 保存图表
    saveas(gcf, 'test_visualization_optimization.png');
    saveas(gcf, 'test_visualization_optimization.fig');
    
    fprintf('  ✓ 优化后的图表已保存到 test_visualization_optimization.png\n');
end
