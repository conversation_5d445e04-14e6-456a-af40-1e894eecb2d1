% 强化学习环境类 - WBAN功率控制
% 实现多模态状态空间和动作空间的强化学习环境
classdef rl_environment < handle
    
    properties
        % 环境参数
        state_dim = 20;           % 优化后的状态空间维度 (从33降到20)
        action_dim = 6;           % 动作空间维度 (功率等级)
        max_episodes = 1000;      % 最大训练轮数
        max_steps = 500;          % 每轮最大步数
        
        % 当前状态
        current_state;
        current_step;
        current_episode;
        
        % 生物信号数据
        ecg_data;
        emg_data;
        imu_data;
        
        % 信道数据
        rssi_data;
        pathloss_data;
        time_vector;
        
        % 网络状态
        buffer_occupancy = 0.5;
        avg_delay = 0;
        throughput = 0;
        pdr = 0;
        
        % 功率等级定义
        power_levels = [-20, -15, -10, -5, 0, 4]; % dBm
        current_power = 0; % 当前功率等级索引
        
        % 性能统计
        energy_consumption = 0;
        total_packets_sent = 0;
        total_packets_received = 0;
        total_delay = 0;
        
        % 奖励函数权重 (修复版 - 强调节能)
        alpha1 = 0.5; % 能耗权重 (提高)
        alpha2 = 0.3; % PDR权重
        alpha3 = 0.15; % 延迟权重
        alpha4 = 0.05; % 生物适应性权重
    end
    
    methods
        function obj = rl_environment()
            % 构造函数 - 初始化环境
            obj.initialize_environment();
        end
        
        function initialize_environment(obj)
            % 初始化环境参数和数据
            fprintf('初始化强化学习环境...\n');
            
            % 生成或加载生物信号数据
            obj.generate_bio_signals();
            
            % 生成或加载信道数据
            obj.load_channel_data();
            
            % 初始化状态
            obj.reset();
            
            fprintf('环境初始化完成\n');
        end
        
        function generate_bio_signals(obj)
            % 生成模拟的生物信号数据
            duration = 60; % 60秒
            fs = 100; % 100Hz采样率
            obj.time_vector = 0:1/fs:duration;
            n_samples = length(obj.time_vector);
            
            % 生成ECG信号 (心率70-120 BPM)
            base_hr = 80;
            hr_variation = 20 * sin(2*pi*0.05*obj.time_vector) + 10*randn(1,n_samples)*0.1;
            obj.ecg_data = base_hr + hr_variation;
            obj.ecg_data = max(60, min(140, obj.ecg_data)); % 限制在合理范围
            
            % 生成EMG信号 (肌电活动)
            activity_level = abs(sin(2*pi*0.1*obj.time_vector)) + 0.2;
            obj.emg_data = activity_level * 50 + 10*randn(1,n_samples);
            obj.emg_data = max(0, obj.emg_data); % 非负值
            
            % 生成IMU信号 (加速度)
            step_freq = 1.2; % 步频
            obj.imu_data = 2*sin(2*pi*step_freq*obj.time_vector) + ...
                          0.5*sin(2*pi*2*step_freq*obj.time_vector) + ...
                          0.3*randn(1,n_samples);
        end
        
        function load_channel_data(obj)
            % 加载或生成信道数据
            try
                % 尝试加载真实数据
                load('13_04_pl.mat');
                if exist('pathloss_res', 'var')
                    % 插值到统一时间轴
                    obj.pathloss_data = interp1(pathloss_res(:,1), pathloss_res(:,2), ...
                                               obj.time_vector, 'linear', 'extrap');
                else
                    obj.generate_synthetic_channel();
                end
            catch
                fprintf('未找到真实信道数据，生成合成数据\n');
                obj.generate_synthetic_channel();
            end
            
            % 计算RSSI (假设发射功率为0dBm)
            obj.rssi_data = 0 - obj.pathloss_data + 2*randn(size(obj.pathloss_data));
        end
        
        function generate_synthetic_channel(obj)
            % 生成合成信道数据
            base_pathloss = 75;
            pathloss_variation = 10*sin(2*pi*0.3*obj.time_vector) + ...
                               5*sin(2*pi*1.2*obj.time_vector) + ...
                               3*randn(size(obj.time_vector));
            obj.pathloss_data = base_pathloss + pathloss_variation;
        end
        
        function state = reset(obj)
            % 重置环境到初始状态
            obj.current_step = 1;
            obj.current_episode = obj.current_episode + 1;
            obj.current_power = 1; % 初始功率等级 (-20dBm，最低功率)

            % 重置性能统计
            obj.energy_consumption = 0;
            obj.total_packets_sent = 0;
            obj.total_packets_received = 0;
            obj.total_delay = 0;
            obj.buffer_occupancy = 0.5;
            obj.avg_delay = 0;
            obj.throughput = 0;
            obj.pdr = 0;

            % 计算初始状态
            state = obj.get_current_state();
            obj.current_state = state;
        end
        
        function [next_state, reward, done, info] = step(obj, action)
            % 执行动作并返回下一状态、奖励等信息
            
            % 验证动作有效性
            if action < 1 || action > length(obj.power_levels)
                error('无效的动作: %d', action);
            end
            
            % 更新功率等级
            obj.current_power = action;
            current_power_dbm = obj.power_levels(action);
            
            % 模拟通信过程
            [pdr, delay, energy] = obj.simulate_communication(current_power_dbm);
            
            % 更新性能统计
            obj.update_performance_stats(pdr, delay, energy);
            
            % 计算奖励
            reward = obj.calculate_reward(pdr, delay, energy);
            
            % 更新步数
            obj.current_step = obj.current_step + 1;
            
            % 检查是否结束
            done = (obj.current_step >= obj.max_steps) || ...
                   (obj.current_step > length(obj.time_vector));
            
            % 获取下一状态
            if ~done
                next_state = obj.get_current_state();
                obj.current_state = next_state;
            else
                next_state = zeros(obj.state_dim, 1);
            end
            
            % 返回信息
            info = struct('pdr', pdr, 'delay', delay, 'energy', energy, ...
                         'power_dbm', current_power_dbm);
        end
        
        function state = get_current_state(obj)
            % 获取优化后的状态向量 (20维)
            if obj.current_step > length(obj.time_vector)
                state = zeros(obj.state_dim, 1);
                return;
            end

            idx = obj.current_step;

            % 核心生物信号特征 (6维) - 精简但保留关键信息
            ecg_key_features = obj.extract_ecg_key_features(idx);    % 2维: 心率均值, 心率变异性
            emg_key_features = obj.extract_emg_key_features(idx);    % 2维: RMS值, 活动强度
            imu_key_features = obj.extract_imu_key_features(idx);    % 2维: 运动强度, 姿态稳定性

            % 关键信道特征 (4维) - 最重要的信道信息
            channel_key_features = obj.extract_channel_key_features(idx); % 2维: RSSI, SNR
            pathloss_key_trend = obj.extract_pathloss_key_trend(idx);      % 2维: 当前值, 变化趋势

            % 核心网络状态 (4维) - 最关键的网络性能指标
            network_key_features = [obj.pdr; obj.avg_delay/100; ...
                                   obj.energy_consumption/100; obj.buffer_occupancy]; % 4维

            % 环境上下文 (4维) - 简化但保留重要信息
            activity_level = obj.get_activity_level(idx);           % 1维: 活动强度等级
            energy_efficiency_target = 0.8;                         % 1维: 节能目标
            qos_urgency = obj.calculate_qos_urgency();              % 1维: QoS紧急程度
            adaptation_factor = obj.calculate_adaptation_factor();   % 1维: 自适应因子

            % 历史性能特征 (2维) - 短期历史信息
            recent_performance = obj.get_recent_performance();       % 2维: 最近能效, 最近PDR

            % 组合优化后的状态向量 (总计20维)
            state = [ecg_key_features; emg_key_features; imu_key_features; ...
                    channel_key_features; pathloss_key_trend; ...
                    network_key_features; ...
                    activity_level; energy_efficiency_target; qos_urgency; adaptation_factor; ...
                    recent_performance];
        end
        
        function features = extract_ecg_features(obj, idx)
            % 提取ECG特征
            window_size = min(10, idx);
            start_idx = max(1, idx - window_size + 1);
            
            ecg_window = obj.ecg_data(start_idx:idx);
            
            hr = mean(ecg_window);                    % 平均心率
            hrv = std(ecg_window);                    % 心率变异性
            rr_interval = 60/hr;                      % RR间期
            qrs_width = 0.08 + 0.02*randn();         % QRS波宽度 (模拟)
            
            % 归一化到[0,1]
            features = [(hr-60)/80; hrv/20; (rr_interval-0.5)/1; qrs_width/0.15];
        end
        
        function features = extract_emg_features(obj, idx)
            % 提取EMG特征
            window_size = min(10, idx);
            start_idx = max(1, idx - window_size + 1);
            
            emg_window = obj.emg_data(start_idx:idx);
            
            rms_val = sqrt(mean(emg_window.^2));      % RMS值
            mav = mean(abs(emg_window));              % 平均绝对值
            wl = sum(abs(diff(emg_window)));          % 波长
            zc = sum(diff(emg_window > mean(emg_window)) ~= 0); % 过零率
            
            % 归一化
            features = [rms_val/100; mav/50; wl/500; zc/10];
        end
        
        function features = extract_imu_features(obj, idx)
            % 提取IMU特征
            window_size = min(10, idx);
            start_idx = max(1, idx - window_size + 1);
            
            imu_window = obj.imu_data(start_idx:idx);
            
            acc_x = mean(imu_window);                 % X轴加速度均值
            acc_y = std(imu_window);                  % Y轴加速度标准差 (模拟)
            acc_z = max(imu_window) - min(imu_window); % Z轴加速度范围 (模拟)
            gyro_norm = sqrt(sum(imu_window.^2));     % 陀螺仪模长
            
            % 归一化
            features = [acc_x/5; acc_y/2; acc_z/10; gyro_norm/50];
        end
        
        function features = extract_channel_features(obj, idx)
            % 提取信道特征
            rssi = obj.rssi_data(idx);
            snr = rssi + 90;                          % 简化SNR计算
            per = max(0, min(1, (rssi + 80)/20));     % 简化PER计算
            cqi = max(0, min(15, (rssi + 100)/5));    % 简化CQI计算
            
            % 归一化
            features = [(rssi + 100)/50; snr/100; per; cqi/15];
        end
        
        function trend = extract_pathloss_trend(obj, idx)
            % 提取路径损耗趋势特征
            window_size = min(5, idx);
            start_idx = max(1, idx - window_size + 1);
            
            pl_window = obj.pathloss_data(start_idx:idx);
            
            current_pl = pl_window(end);
            if length(pl_window) > 1
                pl_derivative = pl_window(end) - pl_window(end-1);
                pl_variance = var(pl_window);
            else
                pl_derivative = 0;
                pl_variance = 0;
            end
            
            % 归一化
            trend = [(current_pl-70)/30; pl_derivative/10; pl_variance/100];
        end
        
        function activity = get_activity_state(obj, idx)
            % 获取活动状态 (one-hot编码)
            % 基于IMU数据判断活动类型
            imu_energy = abs(obj.imu_data(idx));
            
            if imu_energy < 0.5
                activity = [0; 0; 1; 0];      % 静坐
            elseif imu_energy < 2
                activity = [1; 0; 0; 0];      % 步行
            elseif imu_energy < 4
                activity = [0; 1; 0; 0];      % 跑步
            else
                activity = [0; 0; 0; 1];      % 剧烈运动
            end
        end
        
        function temporal = get_temporal_features(obj, idx)
            % 获取时间特征
            current_time = obj.time_vector(idx);
            time_of_day = mod(current_time/3600, 24)/24;  % 一天中的时间 [0,1]
            day_of_week = 0.5;                            % 简化为固定值

            temporal = [time_of_day; day_of_week];
        end

        % 新增的优化特征提取函数
        function features = extract_ecg_key_features(obj, idx)
            % 提取关键ECG特征 (2维)
            window_size = min(10, idx);
            start_idx = max(1, idx - window_size + 1);
            ecg_window = obj.ecg_data(start_idx:idx);

            hr_mean = mean(ecg_window);                    % 心率均值
            hr_var = std(ecg_window);                      % 心率变异性

            % 归一化
            features = [(hr_mean-60)/80; hr_var/20];
        end

        function features = extract_emg_key_features(obj, idx)
            % 提取关键EMG特征 (2维)
            window_size = min(10, idx);
            start_idx = max(1, idx - window_size + 1);
            emg_window = obj.emg_data(start_idx:idx);

            rms_val = sqrt(mean(emg_window.^2));           % RMS值
            activity_intensity = mean(abs(emg_window));     % 活动强度

            % 归一化
            features = [rms_val/100; activity_intensity/50];
        end

        function features = extract_imu_key_features(obj, idx)
            % 提取关键IMU特征 (2维)
            window_size = min(10, idx);
            start_idx = max(1, idx - window_size + 1);
            imu_window = obj.imu_data(start_idx:idx);

            motion_intensity = std(imu_window);            % 运动强度
            posture_stability = 1/(1 + std(imu_window));   % 姿态稳定性

            % 归一化
            features = [motion_intensity/5; posture_stability];
        end

        function features = extract_channel_key_features(obj, idx)
            % 提取关键信道特征 (2维)
            rssi = obj.rssi_data(idx);
            snr = rssi + 90;                               % 简化SNR计算

            % 归一化
            features = [(rssi + 100)/50; snr/100];
        end

        function trend = extract_pathloss_key_trend(obj, idx)
            % 提取关键路径损耗趋势 (2维)
            current_pl = obj.pathloss_data(idx);

            if idx > 1
                pl_change = obj.pathloss_data(idx) - obj.pathloss_data(idx-1);
            else
                pl_change = 0;
            end

            % 归一化
            trend = [(current_pl-70)/30; pl_change/10];
        end

        function level = get_activity_level(obj, idx)
            % 获取活动强度等级 (1维)
            imu_energy = abs(obj.imu_data(idx));

            if imu_energy < 0.5
                level = 0.1;      % 静坐
            elseif imu_energy < 2
                level = 0.4;      % 轻度活动
            elseif imu_energy < 4
                level = 0.7;      % 中度活动
            else
                level = 1.0;      % 剧烈活动
            end
        end

        function urgency = calculate_qos_urgency(obj)
            % 计算QoS紧急程度 (1维)
            if obj.pdr < 0.5
                urgency = 1.0;    % 高紧急
            elseif obj.pdr < 0.7
                urgency = 0.6;    % 中等紧急
            else
                urgency = 0.2;    % 低紧急
            end
        end

        function factor = calculate_adaptation_factor(obj)
            % 计算自适应因子 (1维)
            % 基于最近的性能变化
            if obj.current_step < 5
                factor = 0.5;
            else
                recent_energy_change = abs(obj.energy_consumption - obj.current_step * 2);
                factor = min(1.0, recent_energy_change / 10);
            end
        end

        function performance = get_recent_performance(obj)
            % 获取最近性能指标 (2维)
            if obj.current_step < 5
                performance = [0.5; 0.5];  % 默认值
            else
                recent_efficiency = obj.pdr / (obj.energy_consumption/obj.current_step + 1e-6);
                recent_pdr = obj.pdr;

                % 归一化
                performance = [min(1.0, recent_efficiency/0.01); recent_pdr];
            end
        end

        function [pdr, delay, energy] = simulate_communication(obj, power_dbm)
            % 模拟通信过程，计算PDR、延迟和能耗

            % 获取当前信道状态
            current_rssi = obj.rssi_data(obj.current_step);
            current_snr = current_rssi + 90; % 简化SNR计算

            % 计算接收功率
            rx_power = power_dbm - obj.pathloss_data(obj.current_step);

            % 计算PDR (基于SNR)
            snr_threshold = 10; % 10dB SNR阈值
            if current_snr > snr_threshold
                pdr = min(0.99, 0.5 + 0.4 * (current_snr - snr_threshold) / 20);
            else
                pdr = max(0.1, 0.5 * current_snr / snr_threshold);
            end

            % 添加随机性
            pdr = pdr + 0.05 * randn();
            pdr = max(0, min(1, pdr));

            % 计算延迟 (基于重传)
            base_delay = 10; % 基础延迟 10ms
            retransmission_prob = 1 - pdr;
            expected_retransmissions = retransmission_prob / (1 - retransmission_prob + 1e-6);
            delay = base_delay * (1 + expected_retransmissions);
            delay = max(5, min(200, delay)); % 限制在合理范围

            % 计算能耗 (修复版 - 更合理的能耗模型)
            % 能耗模型: P_total = P_tx + P_circuit
            p_tx_mw = 10^(power_dbm/10); % 发射功率 (mW)
            p_circuit = 20; % 降低电路功耗到20mW
            p_total = p_tx_mw + p_circuit;

            % 传输时间 (包括重传)
            tx_time_ms = 5 * (1 + expected_retransmissions); % 基础5ms + 重传时间

            % 能耗计算 (mJ)
            energy = p_total * tx_time_ms / 1000; % 转换为mJ

            % 添加功率相关的惩罚 (鼓励低功率)
            power_penalty = max(0, (power_dbm + 10) / 20); % -10dBm以上开始惩罚
            energy = energy * (1 + power_penalty);

            % 确保能耗在合理范围内
            energy = max(0.1, min(energy, 12)); % 0.1-12 mJ范围
        end

        function update_performance_stats(obj, pdr, delay, energy)
            % 更新性能统计
            obj.total_packets_sent = obj.total_packets_sent + 1;
            obj.total_packets_received = obj.total_packets_received + pdr;
            obj.total_delay = obj.total_delay + delay;
            obj.energy_consumption = obj.energy_consumption + energy;

            % 更新平均值
            obj.pdr = obj.total_packets_received / obj.total_packets_sent;
            obj.avg_delay = obj.total_delay / obj.total_packets_sent;
            obj.throughput = obj.pdr * 1000; % 简化吞吐量计算

            % 更新缓冲区占用率 (简化模型)
            buffer_change = (1 - pdr) * 0.1 - 0.05;
            obj.buffer_occupancy = max(0, min(1, obj.buffer_occupancy + buffer_change));
        end

        function reward = calculate_reward(obj, pdr, delay, energy)
            % 改进的多目标奖励函数 - 智能奖励塑形版

            % 动态权重调整 - 基于当前性能状态
            if obj.current_step > 10
                % 根据历史性能动态调整权重
                avg_energy = obj.energy_consumption / obj.current_step;
                if avg_energy > 80
                    w_energy = 0.7;  % 高能耗时更强调节能
                    w_pdr = 0.2;
                    w_delay = 0.1;
                elseif obj.pdr < 0.5
                    w_energy = 0.4;  % PDR过低时强调通信质量
                    w_pdr = 0.4;
                    w_delay = 0.2;
                else
                    w_energy = 0.6;  % 平衡状态
                    w_pdr = 0.25;
                    w_delay = 0.15;
                end
            else
                w_energy = 0.6;  % 初始权重
                w_pdr = 0.25;
                w_delay = 0.15;
            end

            % 基础性能指标归一化
            energy_norm = max(0, min(1, (100 - energy) / 100));
            pdr_norm = max(0, min(1, pdr));
            delay_norm = max(0, min(1, (50 - delay) / 50));

            % 基础奖励
            base_reward = 10 * (w_energy * energy_norm + w_pdr * pdr_norm + w_delay * delay_norm);

            % 智能奖励塑形机制
            shaping_reward = 0;

            % 1. 渐进式节能奖励
            if energy < 40
                shaping_reward = shaping_reward + 8.0;  % 极低能耗
            elseif energy < 55
                shaping_reward = shaping_reward + 5.0;  % 很低能耗
            elseif energy < 70
                shaping_reward = shaping_reward + 2.0;  % 低能耗
            end

            % 2. QoS保障奖励
            if pdr >= 0.6 && delay <= 25
                shaping_reward = shaping_reward + 3.0;  % 优秀QoS
            elseif pdr >= 0.5 && delay <= 35
                shaping_reward = shaping_reward + 1.0;  % 良好QoS
            end

            % 3. 自适应性奖励 - 奖励能够适应环境变化的行为
            if obj.current_step > 5
                recent_energy_var = std(obj.energy_consumption);
                if recent_energy_var < 5  % 能耗稳定
                    shaping_reward = shaping_reward + 1.0;
                end
            end

            % 4. 探索-利用平衡奖励
            if obj.current_step > 20
                % 奖励在保持性能的同时进行适度探索
                performance_consistency = 1 - abs(pdr - obj.pdr) - abs(energy - obj.energy_consumption/obj.current_step)/50;
                if performance_consistency > 0.8
                    shaping_reward = shaping_reward + 0.5;
                end
            end

            % 智能惩罚机制
            smart_penalty = 0;

            % 1. 能耗惩罚 - 非线性惩罚
            if energy > 90
                smart_penalty = smart_penalty + 10.0 * ((energy-90)/10)^2;  % 二次惩罚
            elseif energy > 80
                smart_penalty = smart_penalty + 4.0;
            end

            % 2. QoS惩罚 - 考虑应用需求
            if pdr < 0.3
                smart_penalty = smart_penalty + 8.0;  % 严重影响应用
            elseif pdr < 0.4
                smart_penalty = smart_penalty + 4.0;
            end

            % 3. 延迟惩罚 - 实时性要求
            if delay > 50
                smart_penalty = smart_penalty + 5.0;
            elseif delay > 40
                smart_penalty = smart_penalty + 2.0;
            end

            % 最终奖励计算
            reward = base_reward + shaping_reward - smart_penalty;

            % 奖励范围控制
            reward = max(-20, min(25, reward));
        end

        function stability = calculate_bio_stability(obj)
            % 计算生物信号稳定性得分
            if obj.current_step < 10
                stability = 0.5;
                return;
            end

            % 分析最近10步的生物信号变化
            window_size = min(10, obj.current_step);
            start_idx = obj.current_step - window_size + 1;

            % ECG稳定性
            ecg_window = obj.ecg_data(start_idx:obj.current_step);
            ecg_stability = 1 / (1 + std(ecg_window)/mean(ecg_window));

            % EMG稳定性
            emg_window = obj.emg_data(start_idx:obj.current_step);
            emg_stability = 1 / (1 + std(emg_window)/(mean(emg_window)+1e-6));

            % 综合稳定性
            stability = 0.6 * ecg_stability + 0.4 * emg_stability;
            stability = max(0, min(1, stability));
        end

        function info = get_environment_info(obj)
            % 获取环境信息
            info = struct();
            info.current_episode = obj.current_episode;
            info.current_step = obj.current_step;
            info.total_energy = obj.energy_consumption;
            info.average_pdr = obj.pdr;
            info.average_delay = obj.avg_delay;
            info.current_power_dbm = obj.power_levels(obj.current_power);
            info.buffer_occupancy = obj.buffer_occupancy;
        end

        function plot_performance(obj)
            % 绘制性能曲线
            figure('Position', [100, 100, 1200, 800]);

            % 能耗曲线
            subplot(2,3,1);
            plot(1:obj.current_step-1, cumsum(ones(1,obj.current_step-1)) * obj.energy_consumption/obj.current_step);
            title('累积能耗');
            xlabel('时间步');
            ylabel('能耗 (mJ)');
            grid on;

            % PDR曲线
            subplot(2,3,2);
            plot(1:obj.current_step-1, obj.pdr * ones(1,obj.current_step-1));
            title('包递交率');
            xlabel('时间步');
            ylabel('PDR');
            ylim([0, 1]);
            grid on;

            % 延迟曲线
            subplot(2,3,3);
            plot(1:obj.current_step-1, obj.avg_delay * ones(1,obj.current_step-1));
            title('平均延迟');
            xlabel('时间步');
            ylabel('延迟 (ms)');
            grid on;

            % 功率选择历史
            subplot(2,3,4);
            power_history = obj.power_levels(obj.current_power) * ones(1,obj.current_step-1);
            plot(1:obj.current_step-1, power_history);
            title('发射功率');
            xlabel('时间步');
            ylabel('功率 (dBm)');
            grid on;

            % 生物信号
            subplot(2,3,5);
            plot(obj.time_vector(1:obj.current_step-1), obj.ecg_data(1:obj.current_step-1));
            title('心率信号');
            xlabel('时间 (s)');
            ylabel('心率 (BPM)');
            grid on;

            % 信道状态
            subplot(2,3,6);
            plot(obj.time_vector(1:obj.current_step-1), obj.rssi_data(1:obj.current_step-1));
            title('RSSI');
            xlabel('时间 (s)');
            ylabel('RSSI (dBm)');
            grid on;
        end
    end
end
