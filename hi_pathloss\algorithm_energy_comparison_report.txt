=== 算法能耗对比实验报告 ===
生成时间: 24-Jun-2025 15:38:16

实验概述:
对比算法: 固定功率, 简单DQN, 分层RL
测试场景: 静态监测场景, 动态转换场景, 周期性运动场景

详细结果 (平均能耗 ± 标准差, 单位: mJ):

静态监测场景:
  固定功率: 2.204 ± 0.009 mJ
  简单DQN: 2.442 ± 0.580 mJ
  分层RL: 2.313 ± 0.717 mJ

动态转换场景:
  固定功率: 4.228 ± 0.009 mJ
  简单DQN: 3.831 ± 0.460 mJ
  分层RL: 4.144 ± 0.561 mJ

周期性运动场景:
  固定功率: 4.012 ± 0.007 mJ
  简单DQN: 4.603 ± 0.509 mJ
  分层RL: 4.009 ± 0.584 mJ

性能分析:
静态监测场景 - 最佳算法: 固定功率 (2.204 mJ)
动态转换场景 - 最佳算法: 简单DQN (3.831 mJ)
周期性运动场景 - 最佳算法: 分层RL (4.009 mJ)

算法总体排名 (按平均能耗):
1. 固定功率: 3.482 mJ
2. 分层RL: 3.489 mJ
3. 简单DQN: 3.625 mJ

结论:
1. 分层RL算法在静态监测场景下表现最佳
2. 不同算法在不同场景下的适应性存在差异
3. 场景特征对算法性能有显著影响
