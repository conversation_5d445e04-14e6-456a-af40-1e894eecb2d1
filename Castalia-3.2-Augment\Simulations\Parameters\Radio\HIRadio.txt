# ****************************************************************************
# *  Copyright: National ICT Australia,  2009 - 2010                         *
# *  Developed at the ATP lab, Networked Systems research theme              *
# *  Author(s): <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>                        *
# *  This file is distributed under the terms in the attached LICENSE file.  *
# *  If you do not find this file, copies can be found by writing to:        *
# *                                                                          *
# *      NICTA, Locked Bag 9013, Alexandria, NSW 1435, Australia             *
# *      Attention:  License Inquiry.                                        *
# *                                                                          *
# ***************************************************************************/

RX MODES
# Name, dataRate(kbps), modulationType, bitsPerSymbol, bandwidth(MHz), noiseBandwidth(MHz), noiseFloor(dBm), sensitivity(dBm), powerConsumed(mW) sense=-96
IDEAL, 1024, IDEAL, 2, 20, 1000, -104, -87, 0

TX LEVELS
Tx_dBm 4 0 -4 -8 -12 -16 -20 -25 -40
Tx_mW 22.5 15.9 12.6 11.4 10.5 9.9 9.6 9.2 8.1

DELAY TRANSITION MATRIX
# State switching times (time to switch from column state to row state, in msec)
#	RX	TX	SLEEP
RX	-	0.02	0.194
TX	0.02	-	0.194
SLEEP	0.02	0.05	-

POWER TRANSITION MATRIX
#       RX      TX      SLEEP
RX	-	3.0	3.0
TX	3.0	-	3.0
SLEEP	1.5	1.5	-

SLEEP LEVELS
idle 0.05, -, -, -, -
