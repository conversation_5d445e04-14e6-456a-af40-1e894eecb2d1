# ********************************************************************************
# *  Copyright: National ICT Australia, 2009, 2010                               *
# *  Developed at the ATP lab, Networked Systems research theme                  *
# *  Author(s): <PERSON><PERSON>                                                *
# *  This file is distributed under the terms in the attached LICENSE file.      *
# *  If you do not find this file, copies can be found by writing to:            *
# *                                                                              *
# *      NICTA, Locked Bag 9013, Alexandria, NSW 1435, Australia                 *
# *      Attention:  License Inquiry.                                            *
# *                                                                              *
# *******************************************************************************/

[General]

include ../Parameters/Castalia.ini

sim-time-limit = 100s

include ../Parameters/SensorDevice/Accelerometer.ini

SN.physicalProcessName = "CarsPhysicalProcess"
SN.physicalProcess[*].car_interarrival = 5

SN.node[*].Communication.Radio.RadioParametersFile = "../Parameters/Radio/CC2420.txt"

SN.node[*].ApplicationName = "BridgeTest"
SN.node[0].Application.isSink = true
SN.node[*].Application.reportDestination = "SINK"

SN.node[*].Communication.RoutingProtocolName = "MultipathRingsRouting"

[Config 40mBridge]
SN.field_x = 40
SN.field_y = 10
SN.deployment = "[0]->center;[1..6]->3x2"
SN.numNodes = 7

SN.physicalProcess[0].point1_x_coord = 0
SN.physicalProcess[0].point1_y_coord = 5
SN.physicalProcess[0].point2_x_coord = 40
SN.physicalProcess[0].point2_y_coord = 5

[Config 100mBridge]
SN.field_x = 100
SN.field_y = 20
SN.deployment = "[0]->center;[1..18]->6x3"
SN.numNodes = 19

SN.physicalProcess[0].point1_x_coord = 0
SN.physicalProcess[0].point1_y_coord = 10
SN.physicalProcess[0].point2_x_coord = 100
SN.physicalProcess[0].point2_y_coord = 10

[Config 200mBridge]
SN.field_x = 200
SN.field_y = 20
SN.deployment = "[0]->center;[1..34]->11x3"
SN.numNodes = 34

SN.physicalProcess[0].point1_x_coord = 0
SN.physicalProcess[0].point1_y_coord = 10
SN.physicalProcess[0].point2_x_coord = 200
SN.physicalProcess[0].point2_y_coord = 10

[Config idealComms]
SN.wirelessChannel.sigma = 0
SN.wirelessChannel.bidirectionalSigma = 0
SN.node[*].Communication.Radio.mode = "IDEAL"
SN.node[*].Communication.Radio.collisionModel = 0

[Config TMAC]
SN.node[*].Communication.MACProtocolName = "TMAC"

[Config SMAC]
SN.node[*].Communication.MACProtocolName = "TMAC"
SN.node[*].Communication.MAC.listenTimeout = 61
SN.node[*].Communication.MAC.disableTAextension = true
SN.node[*].Communication.MAC.conservativeTA = false
SN.node[*].Communication.MAC.collisionResolution = 0

[Config justCarrierSenseMAC]
SN.node[*].Communication.MACProtocolName = "TunableMAC"

[Config varyDutyCycleMAC]
SN.node[*].Communication.MACProtocolName = "TunableMAC"
SN.node[*].Communication.MAC.dutyCycle = ${dutyCycle=0.03,0.05,0.1,0.3}	# listening / (sleeping+listening)

[Config varySampleRate]
SN.node[*].Application.sampleInterval = ${sampleInterval=10,20,50,200,1000}

[Config noRouting]
SN.node[*].Communication.RoutingProtocolName = "BypassRouting"
SN.node[*].Application.reportDestination = "0"
