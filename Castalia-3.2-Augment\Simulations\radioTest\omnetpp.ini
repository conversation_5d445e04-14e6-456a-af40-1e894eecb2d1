# ****************************************************************************
# *  Copyright: National ICT Australia,  2007 - 2010                         *
# *  Developed at the ATP lab, Networked Systems research theme              *
# *  Author(s): <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>                        *
# *  This file is distributed under the terms in the attached LICENSE file.  *
# *  If you do not find this file, copies can be found by writing to:        *
# *                                                                          *
# *      NICTA, Locked Bag 9013, Alexandria, NSW 1435, Australia             *
# *      Attention:  License Inquiry.                                        *
# *                                                                          *
# ***************************************************************************/

[General]

# =============================================
# Always include the main Castalia.ini file
# =============================================
include ../Parameters/Castalia.ini


sim-time-limit = 100s
record-eventlog = true

SN.field_x = 200	# meters
SN.field_y = 200	# meters

# These tests include 3 nodes each, coordinates will be specified manually
SN.numNodes = 3

# important wireless channel switch to allow mobility
SN.wirelessChannel.onlyStaticNodes = false
SN.wirelessChannel.sigma = 0
SN.wirelessChannel.bidirectionalSigma = 0

# Choose a radio and set the Tx power to a low value so
# that node's mobility has a better effect on connectivity
SN.node[*].Communication.Radio.RadioParametersFile = "../Parameters/Radio/CC2420.txt"
SN.node[*].Communication.Radio.TxOutputPower = "-5dBm"


# These tests use big packets (2kb) to show interference clearly
# This requires to set the max pkt size in all communication layers
SN.node[*].Communication.Routing.maxNetFrameSize = 2500
SN.node[*].Communication.MAC.maxMACFrameSize = 2500
SN.node[*].Communication.Radio.maxPhyFrameSize = 2500

# Throughput test application is used to send 2000-byte
# packets to node 0 (which by default is the receiving 
# node for this app).5 packets per second will be send 
SN.node[*].ApplicationName = "ThroughputTest"
SN.node[*].Application.packet_rate = 5
SN.node[*].Application.constantDataPayload = 2000
# application's trace info for node 0 (receiving node)
# is turned on, to show some interesting patterns
SN.node[0].Application.collectTraceInfo = true

# Nodes are moving, so seeing their location may be useful
# SN.node[*].MobilityManager.collectTraceInfo = true

# ==========================================================
# The default configuration sets node 0 to be mobile, while 
# sending nodes 1 and 2 are static. Receiving node 0 will 
# move in diagonal pattern, allowing it to receive packets 
# from node 1 and some time later from node 2. This will be
# clearly seen on the trace file and the collected output.
# ==========================================================

SN.node[0].xCoor = 0
SN.node[0].yCoor = 0
SN.node[1].xCoor = 50
SN.node[1].yCoor = 50
SN.node[2].xCoor = 150
SN.node[2].yCoor = 150

SN.node[0].MobilityManagerName = "LineMobilityManager"

SN.node[0].MobilityManager.updateInterval = 100
SN.node[0].MobilityManager.xCoorDestination = 200
SN.node[0].MobilityManager.yCoorDestination = 200
SN.node[0].MobilityManager.speed = 15


# =========================================================
# InterferenceTest1 shows how communication link between
# static nodes 0 and 1 is disrupted when mobile node 2
# passes between them.
# =========================================================

[Config InterferenceTest1]

SN.node[0].MobilityManagerName = "NoMobilityManager"
SN.node[1].MobilityManagerName = "NoMobilityManager"
SN.node[2].MobilityManagerName = "LineMobilityManager"

SN.node[0].xCoor = 10
SN.node[0].yCoor = 50

SN.node[1].xCoor = 0
SN.node[1].yCoor = 50

SN.node[2].xCoor = 5
SN.node[2].yCoor = 0

SN.node[2].MobilityManager.updateInterval = 100
SN.node[2].MobilityManager.xCoorDestination = 5
SN.node[2].MobilityManager.yCoorDestination = 100
SN.node[2].MobilityManager.speed = 5


# ===========================================================
# InterferenceTest2 is similar to InterferenceTest1, but
# this time node 2 approaches much closer to receiving node 0
# while node 1 is located further away. While node 1 is still 
# able to reach node 0 most of the time, when node 2 moves
# very close to the receiver, it's transmissions become
# stronger and node 0 is able to receive packets from node 2
# in spite of the fact that node 1 is still transmitting
# ===========================================================

[Config InterferenceTest2]

SN.node[0].MobilityManagerName = "NoMobilityManager"
SN.node[1].MobilityManagerName = "NoMobilityManager"
SN.node[2].MobilityManagerName = "LineMobilityManager"

SN.node[0].xCoor = 15
SN.node[0].yCoor = 50

SN.node[1].xCoor = 0
SN.node[1].yCoor = 50

SN.node[2].xCoor = 22
SN.node[2].yCoor = 0

SN.node[2].MobilityManager.updateInterval = 100
SN.node[2].MobilityManager.xCoorDestination = 22
SN.node[2].MobilityManager.yCoorDestination = 100
SN.node[2].MobilityManager.speed = 5


# =====================================================
# This cofiguration tests the CSInterrupt feature
# of the radio, as well as reads the RSSI value.
# In the trace output we see when the Carrier Sense 
# interrupt is received for different values of
# TX power and CCA threshold. Just two static nodes.
# =====================================================
[Config CSinterruptTest]

sim-time-limit = 1s

SN.node[*].Communication.Radio.carrierSenseInterruptEnabled = true

SN.numNodes = 2

SN.wirelessChannel.onlyStaticNodes = true
SN.node[0].MobilityManagerName = "NoMobilityManager"
SN.node[1].MobilityManagerName = "NoMobilityManager"

SN.node[0].xCoor = 10
SN.node[0].yCoor = 50
SN.node[0].zCoor = 0

SN.node[1].xCoor = 0
SN.node[1].yCoor = 50
SN.node[1].zCoor = 0

SN.node[*].nodeApplication.constantDataPayload = 200

SN.node[1].Communication.Radio.TxOutputPower = ${TxPower="-5dBm", "-10dBm", "-15dBm"}
SN.node[0].Communication.Radio.CCAthreshold = ${CCAthreshold=-95, -90, -85}


[Config varyInterferenceModel]
SN.node[*].Communication.Radio.collisionModel = ${InterfModel=0,1,2}
