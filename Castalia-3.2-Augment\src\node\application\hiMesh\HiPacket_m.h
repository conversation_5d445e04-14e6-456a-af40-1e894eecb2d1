//
// Generated file, do not edit! Created by nedtool 4.6 from src/node/application/hiMesh/HiPacket.msg.
//

#ifndef _HIPACKET_M_H_
#define _HIPACKET_M_H_

#include <omnetpp.h>

// nedtool version check
#define MSGC_VERSION 0x0406
#if (MSGC_VERSION!=OMNETPP_VERSION)
#    error Version mismatch! Probably this file was generated by an earlier version of nedtool: 'make clean' should help.
#endif



// cplusplus {{
#include "ApplicationPacket_m.h"
// }}

/**
 * Struct generated from src/node/application/hiMesh/HiPacket.msg:7 by nedtool.
 */
struct addr
{
    addr();
    unsigned short src;
    unsigned short dst;
    unsigned short rt_hstr[10];
    unsigned short ttl;
};

void doPacking(cCommBuffer *b, addr& a);
void doUnpacking(cCommBuffer *b, addr& a);

/**
 * Class generated from <tt>src/node/application/hiMesh/HiPacket.msg:14</tt> by nedtool.
 * <pre>
 * packet HiPacket extends ApplicationPacket
 * {
 *     addr address;
 * }
 * </pre>
 */
class HiPacket : public ::ApplicationPacket
{
  protected:
    addr address_var;

  private:
    void copy(const HiPacket& other);

  protected:
    // protected and unimplemented operator==(), to prevent accidental usage
    bool operator==(const HiPacket&);

  public:
    HiPacket(const char *name=NULL, int kind=0);
    HiPacket(const HiPacket& other);
    virtual ~HiPacket();
    HiPacket& operator=(const HiPacket& other);
    virtual HiPacket *dup() const {return new HiPacket(*this);}
    virtual void parsimPack(cCommBuffer *b);
    virtual void parsimUnpack(cCommBuffer *b);

    // field getter/setter methods
    virtual addr& getAddress();
    virtual const addr& getAddress() const {return const_cast<HiPacket*>(this)->getAddress();}
    virtual void setAddress(const addr& address);
};

inline void doPacking(cCommBuffer *b, HiPacket& obj) {obj.parsimPack(b);}
inline void doUnpacking(cCommBuffer *b, HiPacket& obj) {obj.parsimUnpack(b);}


#endif // ifndef _HIPACKET_M_H_

