[General]

include ../Parameters/Castalia.ini

sim-time-limit = 605s

SN.numNodes = 6


SN.wirelessChannel.pathLossMapFile = "../Parameters/WirelessChannel/BANmodels/pathLossMap.txt"
SN.wirelessChannel.temporalModelParametersFile = "../Parameters/WirelessChannel/BANmodels/TemporalModel.txt"

# Path Loss coeffs
#SN.wirelessChannel.pathLossExponent = 0.66
#SN.wirelessChannel.PLd0 = 36.1
#SN.wirelessChannel.d0 = 1
#SN.WirelessChannel.sigma = 3.8

# Allow mobility
SN.wirelessChannel.onlyStaticNodes = true
SN.node[*].MobilityManagerName = "NoMobilityManager"

# Radio PHY:
SN.node[*].Communication.Radio.RadioParametersFile = "../Parameters/Radio/CC2650.txt"
SN.node[*].Communication.Radio.mode = "IDEAL"
SN.node[*].Communication.Radio.symbolsForRSSI = 16
SN.node[*].Communication.Radio.TxOutputPower = "0dBm"
#SN.node[*].Communication.Radio.bufferSize = 96   ### default=16
#SN.node[*].Communication.Radio.phyFrameOverhead = 0

# MAC Layer
SN.node[*].Communication.MACProtocolName = "HiTdmaMac"
SN.node[*].Communication.MAC.macPacketOverhead = 0 ### default (9)
SN.node[*].Communication.MAC.macBufferSize = 10000 ### default (32)


# Routing (Network) layer
#SN.node[*].Communication.Routing.netBufferSize = 96   ### default=32
SN.node[*].Communication.Routing.netDataFrameOverhead = 5 ### default=10 bytes


#SN.wirelessChannel.collectTraceInfo = true
#SN.node[*].ResourceManager.collectTraceInfo = true
#SN.node[*].Communication.Radio.collectTraceInfo = true
#SN.node[*].Communication.MAC.collectTraceInfo = true
#SN.node[*].Communication.Routing.collectTraceInfo = true
#SN.node[*].Application.collectTraceInfo = true

SN.node[*].ResourceManager.baselineNodePower = 0.1 #0.1

SN.node[*].ApplicationName = "HiMesh"
SN.node[*].Application.hubAddress = 0
SN.node[*].Application.startupDelay = 0.0
SN.node[*].Application.packet_rate = 10	###5
#SN.node[*].Application.finishTime = 180


#SN.node[*].Application.constantDataPayload = 100


[Config numNodes]
SN.numNodes = ${n=3,4,5,6}


[Config CSMA]

#SN.node[*].ResourceManager.baselineNodePower = 0
SN.node[*].Communication.MACProtocolName = "TunableMAC"
SN.node[*].Communication.MAC.phyDataRate = 1024.0
SN.node[*].Communication.MAC.listenInterval = 10
SN.node[*].Communication.MAC.dutyCycle = 1.0
SN.node[*].Communication.MAC.beaconIntervalFraction = 1.0
SN.node[*].Communication.MAC.numTx = 1
SN.node[*].Communication.MAC.backoffType = 1
SN.node[*].Communication.MAC.backoffBaseValue = 2
SN.node[*].Communication.MAC.randomTxOffset = 2
SN.node[*].Communication.MAC.txAllPacketsInFreeChannel = false
SN.node[*].Communication.MAC.macBufferSize = 2000

SN.node[0].Application.startupDelay = 0.0  	#wait for 1sec before starting sending packets
SN.node[1].Application.startupDelay = 0.1
SN.node[2].Application.startupDelay = 0.2
SN.node[3].Application.startupDelay = 0.3 
SN.node[4].Application.startupDelay = 0.4 
SN.node[5].Application.startupDelay = 0.5 

[Config star]
SN.node[*].ApplicationName = "HiStar"

[Config hubAddress]
SN.node[*].Application.hubAddress = ${hub=0,1,2,3,4,5}

[Config varyPacketRate]

SN.node[*].Application.packet_rate = ${rate=1,5,10,20,30}

[Config varyTxPower]

SN.node[*].Communication.Radio.TxOutputPower = ${power="0dBm","-10dBm","-20dBm"}

