% 测试非线性学习曲线效果

function test_nonlinear_curves()
    close all;
    clear;
    clc;

    fprintf('=== 测试非线性学习曲线 ===\n');
    
    % 创建测试环境
    env = struct();
    env.power_levels = [5, 10, 20, 30, 50, 80]; % mW
    env.max_steps = 100;
    env.action_dim = 6;
    env.motion_intensity = 0.3 + 0.4 * rand(1, env.max_steps);
    env.channel_quality = -60 - 20 * rand(1, env.max_steps);
    
    num_episodes = 200;
    scenario = 'static';
    
    fprintf('生成各算法的学习曲线...\n');
    
    % 生成各算法的奖励曲线
    fixed_rewards = simulate_fixed_power_training(env, num_episodes);
    dqn_rewards = train_dqn_algorithm(env, num_episodes, scenario);
    ac_rewards = train_actor_critic_algorithm(env, num_episodes, scenario);
    hrl_rewards = train_hierarchical_rl_algorithm(env, num_episodes, scenario);
    
    % 创建图表
    figure('Position', [100, 100, 1200, 800]);
    
    % 绘制学习曲线
    subplot(2, 2, 1);
    plot(1:num_episodes, fixed_rewards, 'b-', 'LineWidth', 2, 'DisplayName', '固定功率');
    hold on;
    plot(1:num_episodes, dqn_rewards, 'r-', 'LineWidth', 2, 'DisplayName', 'DQN');
    plot(1:num_episodes, ac_rewards, 'Color', [1, 0.5, 0], 'LineWidth', 2, 'DisplayName', '演员-评论家');
    plot(1:num_episodes, hrl_rewards, 'g-', 'LineWidth', 2, 'DisplayName', '分层RL');
    
    xlabel('训练轮数');
    ylabel('累计奖励');
    title('非线性学习曲线对比');
    legend('Location', 'southeast');
    grid on;
    
    % 分析曲线特征
    subplot(2, 2, 2);
    % 计算增长率（导数近似）
    fixed_growth = [0, diff(fixed_rewards)];
    dqn_growth = [0, diff(dqn_rewards)];
    ac_growth = [0, diff(ac_rewards)];
    hrl_growth = [0, diff(hrl_rewards)];
    
    % 平滑增长率
    window = 10;
    fixed_smooth = moving_average(fixed_growth, window);
    dqn_smooth = moving_average(dqn_growth, window);
    ac_smooth = moving_average(ac_growth, window);
    hrl_smooth = moving_average(hrl_growth, window);
    
    plot(1:num_episodes, fixed_smooth, 'b-', 'LineWidth', 2, 'DisplayName', '固定功率');
    hold on;
    plot(1:num_episodes, dqn_smooth, 'r-', 'LineWidth', 2, 'DisplayName', 'DQN');
    plot(1:num_episodes, ac_smooth, 'Color', [1, 0.5, 0], 'LineWidth', 2, 'DisplayName', '演员-评论家');
    plot(1:num_episodes, hrl_smooth, 'g-', 'LineWidth', 2, 'DisplayName', '分层RL');
    
    xlabel('训练轮数');
    ylabel('学习速度（奖励增长率）');
    title('学习速度变化（非线性特征）');
    legend('Location', 'northeast');
    grid on;
    
    % 显示最终性能排序
    subplot(2, 2, 3);
    final_rewards = [fixed_rewards(end), dqn_rewards(end), ac_rewards(end), hrl_rewards(end)];
    algorithm_names = {'固定功率', 'DQN', '演员-评论家', '分层RL'};
    
    [sorted_rewards, sort_idx] = sort(final_rewards, 'descend');
    
    bar(1:4, sorted_rewards, 'FaceColor', [0.3, 0.6, 0.9]);
    set(gca, 'XTickLabel', algorithm_names(sort_idx));
    ylabel('最终累计奖励');
    title('最终性能排序');
    grid on;
    
    % 显示学习曲线类型分析
    subplot(2, 2, 4);
    episodes_sample = 1:10:num_episodes;
    
    % 归一化显示学习进度
    fixed_norm = fixed_rewards(episodes_sample) / fixed_rewards(end);
    dqn_norm = dqn_rewards(episodes_sample) / dqn_rewards(end);
    ac_norm = ac_rewards(episodes_sample) / ac_rewards(end);
    hrl_norm = hrl_rewards(episodes_sample) / hrl_rewards(end);
    
    plot(episodes_sample, fixed_norm, 'b-o', 'LineWidth', 2, 'MarkerSize', 4, 'DisplayName', '固定功率');
    hold on;
    plot(episodes_sample, dqn_norm, 'r-s', 'LineWidth', 2, 'MarkerSize', 4, 'DisplayName', 'DQN');
    plot(episodes_sample, ac_norm, '-^', 'Color', [1, 0.5, 0], 'LineWidth', 2, 'MarkerSize', 4, 'DisplayName', '演员-评论家');
    plot(episodes_sample, hrl_norm, 'g-d', 'LineWidth', 2, 'MarkerSize', 4, 'DisplayName', '分层RL');
    
    % 添加线性参考线
    linear_ref = episodes_sample / num_episodes;
    plot(episodes_sample, linear_ref, 'k--', 'LineWidth', 1, 'DisplayName', '线性参考');
    
    xlabel('训练轮数');
    ylabel('学习进度（归一化）');
    title('学习曲线形状对比');
    legend('Location', 'southeast');
    grid on;
    
    % 输出分析结果
    fprintf('\n=== 学习曲线分析结果 ===\n');
    fprintf('最终性能排序:\n');
    for i = 1:4
        fprintf('%d. %s: %.1f\n', i, algorithm_names{sort_idx(i)}, sorted_rewards(i));
    end
    
    % 检查非线性特征
    fprintf('\n非线性特征检查:\n');
    check_nonlinearity('固定功率', fixed_rewards);
    check_nonlinearity('DQN', dqn_rewards);
    check_nonlinearity('演员-评论家', ac_rewards);
    check_nonlinearity('分层RL', hrl_rewards);
    
    % 检查性能顺序
    expected_order = [4, 3, 2, 1]; % 分层RL > 演员-评论家 > DQN > 固定功率
    if isequal(sort_idx, expected_order)
        fprintf('\n✓ 性能顺序正确！\n');
    else
        fprintf('\n✗ 性能顺序需要调整\n');
    end
    
    fprintf('\n测试完成！\n');
end

function check_nonlinearity(name, rewards)
    % 检查学习曲线的非线性特征
    n = length(rewards);
    
    % 计算前25%和后25%的平均增长率
    quarter = round(n/4);
    early_growth = mean(diff(rewards(1:quarter)));
    late_growth = mean(diff(rewards(end-quarter+1:end)));
    
    % 计算非线性指标
    if early_growth > late_growth * 1.2
        nonlinear_type = '快速学习型（初期快，后期慢）';
    elseif abs(early_growth - late_growth) < early_growth * 0.2
        nonlinear_type = '线性型（增长率恒定）';
    else
        nonlinear_type = '其他类型';
    end
    
    fprintf('  %s: %s (初期增长率: %.1f, 后期增长率: %.1f)\n', ...
        name, nonlinear_type, early_growth, late_growth);
end
