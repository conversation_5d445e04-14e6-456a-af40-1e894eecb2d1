%% 生成基于物理模型的科学RSSI数据
% 这个脚本展示了科学的RSSI应该是什么样的
% 与之前的简单正弦波模拟相比，这个版本基于真实的物理模型

fprintf('=== 生成基于物理模型的科学RSSI数据 ===\n');
    
    % 基于真实人体运动的RSSI模拟
    t = 0:0.01:10;  % 10秒，10ms采样间隔（更真实的时间尺度）
    
    % 模拟人体运动导致的距离变化（基于步态周期）
    step_frequency = 1.2;  % 步频 1.2 Hz（正常步行）
    base_distance = 0.5;   % 基础距离 0.5m（胸部到手腕）
    distance_variation = 0.2; % 距离变化幅度 ±20cm
    
    % 人体运动模式：步行时手臂摆动
    distance = base_distance + distance_variation * sin(2*pi*step_frequency*t) + ...
               0.05 * sin(2*pi*2*step_frequency*t) + ... % 二次谐波
               0.02 * randn(size(t)); % 随机抖动
    
    % 基于路径损耗模型计算RSSI
    % 使用论文中的CM3A模型参数
    frequency = 2450; % 2.45 GHz
    tx_power = 0;     % 0 dBm发射功率
    
    % 路径损耗计算（基于距离）
    path_loss = zeros(size(distance));
    for i = 1:length(distance)
        % 调用真实的路径损耗函数
        path_loss(i) = pathloss(4, 1, distance(i)*1000, 1, 1); % 距离转换为mm
    end
    
    % RSSI = 发射功率 - 路径损耗 + 阴影衰落
    shadow_fading = 4 * randn(size(t)); % 4dB标准差的阴影衰落
    rssi = tx_power - path_loss + shadow_fading;
    
    % 添加多径衰落（快衰落）
    fast_fading = 2 * sin(2*pi*10*t + 2*pi*rand) .* exp(-0.1*t); % 衰减的快衰落
    rssi = rssi + fast_fading;
    
    % 绘制科学的RSSI图
    figure('Position', [100, 100, 1200, 800]);
    
    % 主RSSI图
    subplot(3,1,1);
    plot(t, rssi, 'b-', 'LineWidth', 1.2);
    xlabel('时间 (s)');
    ylabel('RSSI (dBm)');
    title('基于物理模型的RSSI时间序列（人体步行运动）');
    grid on;
    ylim([min(rssi)-5, max(rssi)+5]);
    
    % 添加统计信息
    mean_rssi = mean(rssi);
    std_rssi = std(rssi);
    text(0.02, 0.95, sprintf('均值: %.1f dBm\n标准差: %.1f dB\n采样率: 100 Hz\n运动: 步行 (%.1f Hz)', ...
         mean_rssi, std_rssi, step_frequency), ...
         'Units', 'normalized', 'BackgroundColor', 'white', 'EdgeColor', 'black');
    
    % 距离变化图（用于验证）
    subplot(3,1,2);
    plot(t, distance*100, 'r-', 'LineWidth', 1.2);
    xlabel('时间 (s)');
    ylabel('Tx-Rx距离 (cm)');
    title('发射器-接收器距离变化（模拟手臂摆动）');
    grid on;
    
    % 路径损耗图
    subplot(3,1,3);
    plot(t, path_loss, 'g-', 'LineWidth', 1.2);
    xlabel('时间 (s)');
    ylabel('路径损耗 (dB)');
    title('基于CM3A模型的路径损耗变化');
    grid on;
    
    fprintf('\n=== 科学RSSI数据特征 ===\n');
    fprintf('- 时间范围: %.1f秒\n', max(t));
    fprintf('- 采样点数: %d\n', length(t));
    fprintf('- RSSI范围: %.1f 到 %.1f dBm\n', min(rssi), max(rssi));
    fprintf('- 距离范围: %.1f 到 %.1f cm\n', min(distance)*100, max(distance)*100);
    fprintf('- 路径损耗范围: %.1f 到 %.1f dB\n', min(path_loss), max(path_loss));
    
    % 对比原来的不科学版本
    fprintf('\n=== 与原版本对比 ===\n');
    fprintf('原版本问题:\n');
    fprintf('  ❌ 单调递增趋势（不现实）\n');
    fprintf('  ❌ 简单正弦波（过于理想化）\n');
    fprintf('  ❌ 时间轴错误（显示4.5万秒）\n');
    fprintf('  ❌ 没有物理基础\n');
    fprintf('\n科学版本特点:\n');
    fprintf('  ✅ 基于真实人体运动模型\n');
    fprintf('  ✅ 使用论文中的CM3A路径损耗模型\n');
    fprintf('  ✅ 包含阴影衰落和多径衰落\n');
    fprintf('  ✅ 合理的时间尺度和采样率\n');
    fprintf('  ✅ 符合WBAN信道特征\n');
% 脚本结束
