根目录文件:

13_01_pl.txt - 存储 13_01 实验场景的原始路径损耗数值数据。
13_01_pl.mat - 与 13_01_pl.txt 同源的 MATLAB 二进制格式数据, 方便在 MATLAB 中直接加载。
13_01_pl_tx.txt - 记录 13_01 实验场景中各传感器节点发射功率相关的路径损耗数据。
13_04_pl.txt - 存储 13_04 实验场景的原始路径损耗数值数据。
13_04_pl.mat - 与 13_04_pl.txt 同源的 MATLAB 二进制格式数据。
13_04_pl_tx.txt - 记录 13_04 实验场景发射功率相关的路径损耗数据。
35_01_pl.txt - 存储 35_01 实验场景的原始路径损耗数值数据。
35_01_pl.mat - 与 35_01_pl.txt 同源的 MATLAB 二进制格式数据。
35_01_pl_tx.txt - 记录 35_01 实验场景发射功率相关的路径损耗数据。
35_01.avi - 35_01 实验场景的视频记录，用于可视化传感器部署与人体动作。
LICENSE - 开源许可文件，声明本仓库使用的许可证类型。
PAY ATTENTION.txt - 项目注意事项及重要提示。
README.md - 项目总体说明、使用方法与基本介绍。
论文复现说明.md - 论文复现的具体步骤与说明文档。

Castalia-3.2-Augment 目录:

CastaliaBin.exe / bin/                   - Castalia 网络模拟器可执行文件，用于在 Windows 平台运行仿真。
src/                                      - Castalia 增强版的 C++ 源代码，包含无线体域网节点、协议栈、物理过程等模块实
                                           现。该部分主要用于离线仿真以验证强化学习算法的节能效果。
Simulations/                              - 一组 OMNeT++ 仿真配置 (omnetpp.ini) 与实验场景定义，用于驱动 Castalia 进行
                                           不同场景下的网络仿真。
    BANtest/omnetpp.ini                  - 典型 BAN 场景配置文件。
    BridgeTest/omnetpp.ini               - 数据转发场景配置文件。
    connectivityMap/omnetpp.ini          - 节点连通性绘制实验配置。
    HIBAN/omnetpp.ini                    - HI-BAN 相关场景配置。
    HIchan/                              - HI 通道相关实验场景及结果文件。
        omnetpp_fixed.ini                - 修订版场景配置文件。
        results/                         - 仿真输出结果与绘图脚本 (MATLAB)。
    [...其他子场景省略...]
Parameters/                               - Castalia 各种硬件/协议参数模板。
    Castalia.ini                          - 全局默认参数。
    MAC/                                  - 不同 MAC 协议参数模板 (CSMA, SMAC 等)。
    PhysicalProcess/                      - 物理过程参数模板。
    Radio/                                - 不同无线芯片射频参数 (BANRadio, CC2420 等)。
    SensorDevice/                         - 传感器设备模板。
    WirelessChannel/                      - 无线信道模型与实验数据 (含路径损耗 txt)。
src/                                      - 详尽的 C++ 代码，按照层次分为 application、communication、routing、radio、
                                           mobilityManager、physicalProcess、resourceManager、sensorManager 等子目录。
                                           每个 *.cc / *.h / *.ned 文件实现了一种协议、算法或网络模块。
                                           (出于篇幅, 后续附录按文件前缀概述)。

hi_pathloss 目录:

该目录包含论文核心的 MATLAB 实验与算法实现。

数据与视频文件:
    13_01_pl.mat / txt / 13_01.avi  - 13_01 近端佩戴场景原始路径损耗及视频。
    13_04_pl.mat / txt / 13_04.bvh  - 13_04 动作场景数据与骨骼动画。
    35_01_pl.mat / txt / 35_01.bvh  - 35_01 远端佩戴场景数据。
    13_04_pl_tx_reproduced.txt      - 13_04 发射功率路径损耗再现数据。

核心算法脚本:
    adaptation_experiment_main.m            - 启动分层强化学习功率控制主实验脚本。
    adaptation_training_module.m            - 负责训练分层 RL Agent (高层调度器 + 低层功率控制器)。
    adaptation_environment_interface.m      - 定义 RL 环境 (状态、动作、奖励) 与 Castalia/hi_pathloss 数据的接口。
    adaptation_analysis_module.m            - 训练后分析 RL 性能指标 (能耗、吞吐等)。
    adaptation_evaluation_module.m          - 在不同场景下评估已训练模型的泛化能力。
    adaptation_visualization_module.m       - 绘制实验关键图表 (收敛曲线、能耗对比等)。
    adaptation_experiment_results/          - 存放主实验生成的结果文件与报告。
        adaptation_experiment_report.txt    - 文字报告。
        demo_experiment_report.txt          - Demo 实验报告。
        [...png, mat]                       - 对应图表和中间结果。

算法比较与验证脚本:
    algorithm_comparison_analysis.m         - 将分层 RL 与 baseline 算法进行对比分析。
    algorithm_energy_comparison.m           - 能耗维度对比脚本。
    clean_rl_comparison.fig / png           - 相关绘图文件。
    debug_rl_algorithm.m                    - 调试 RL 算法的脚本。
    complete_verification_analysis.m        - 完整实验验证分析脚本。
    estimate_values.m                       - 估计场景参数辅助函数。
    analyze_simulation_results.m            - 解析 Castalia 仿真输出。

函数库:
    functions/
        loadbvh.m                           - 载入 BVH 骨骼动画文件并解析位姿信息。
        localToGlobal3d.m                   - 坐标系转换函数。
        pathloss.m                          - 根据节点位姿计算路径损耗, 支持批量处理。

其他脚本与结果文件 (按文件名顾名思义):
    dqn_curve_comparison.m / png            - DQN 版本曲线对比脚本及图。
    energy_consumption_comparison.m / png   - 不同算法能耗曲线对比脚本及图。
    dynamic_energy_comparison.png           - 动态场景能耗对比图。
    [...]                                   - 其余 .m / .fig / .png / .txt / .md 文件以名称命名功能, 用于补充实验、绘图或汇报。

附录: Castalia-3.2-Augment/src 关键模块文件速览
    node/application/*/*.cc/.h              - 高层应用逻辑，例如 BridgeTest, ConnectivityMap, HiMesh, HiStar 等。
    communication/mac/*/*.cc/.h             - 不同 MAC 协议实现 (BaselineBANMac, BypassMAC, HiTdmaMac, Mac802154, TMAC 等)。
    communication/routing/*/*.cc/.h         - 路由协议实现 (BypassRouting, MultipathRingsRouting 等)。
    communication/radio/Radio.cc/.h         - 节点射频抽象。
    mobilityManager/*/*.cc/.h               - 节点移动模型。
    physicalProcess/*/*.cc/.h               - 物理过程 (CarsPhysicalProcess, CustomizablePhysicalProcess 等)。
    wirelessChannel/WirelessChannel.cc/.h   - 无线信道模型核心代码。

上述 C++ / NED 文件均遵循 Castalia/OMNeT++ 模块规范, 用于仿真验证强化学习功率控制算法在完整协议栈中的性能。

说明:
1. 为避免文件过长, 对于具有相同功能模式的大量结果文件 (.mat/.png/.fig) 与重复实验场景, 此处按类别说明而未逐一展开。
2. 若需查看任一具体文件, 可根据路径在仓库中定位。 