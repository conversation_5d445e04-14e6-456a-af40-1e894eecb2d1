% 简化版本：测试真实Castalia仿真数据
% 避免嵌套函数，使用简单的脚本结构

close all
clear all

fprintf('=== 测试真实Castalia仿真数据 ===\n');

%% 1. 检查文件存在性
results_path = '../Castalia-3.2-Augment/Simulations/HIchan/results/';
sca_file = [results_path 'TMAC-0.sca'];
vec_file = [results_path 'TMAC-0.vec'];

fprintf('检查文件存在性：\n');
fprintf('.sca文件: %s - ', sca_file);
if exist(sca_file, 'file')
    fprintf('✓ 存在\n');
    sca_exists = true;
else
    fprintf('✗ 不存在\n');
    sca_exists = false;
end

fprintf('.vec文件: %s - ', vec_file);
if exist(vec_file, 'file')
    fprintf('✓ 存在\n');
    vec_exists = true;
else
    fprintf('✗ 不存在\n');
    vec_exists = false;
end

%% 2. 读取并分析.sca文件
if sca_exists
    fprintf('\n=== 分析.sca文件内容 ===\n');
    
    try
        fid = fopen(sca_file, 'r');
        if fid == -1
            error('无法打开.sca文件');
        end
        
        line_count = 0;
        scalar_count = 0;
        packets_sent_node1 = 0;
        packets_received_node0 = 0;
        
        while ~feof(fid)
            line = fgetl(fid);
            line_count = line_count + 1;
            
            if ischar(line) && contains(line, 'scalar')
                scalar_count = scalar_count + 1;
                fprintf('第%d行: %s\n', line_count, line);
                
                % 简化的数据提取
                if contains(line, 'node[1]') && contains(line, 'Packets Sent')
                    % 提取发送数据包数量
                    parts = strsplit(line);
                    if length(parts) >= 4
                        packets_sent_node1 = str2double(parts{end});
                    end
                elseif contains(line, 'node[0]') && contains(line, 'Packets Received')
                    % 提取接收数据包数量
                    parts = strsplit(line);
                    if length(parts) >= 4
                        packets_received_node0 = str2double(parts{end});
                    end
                end
            end
        end
        fclose(fid);
        
        fprintf('总行数: %d\n', line_count);
        fprintf('标量数据行数: %d\n', scalar_count);
        
        % 计算PDR
        if packets_sent_node1 > 0
            pdr = packets_received_node0 / packets_sent_node1;
            fprintf('\n=== 真实仿真结果 ===\n');
            fprintf('节点1发送数据包: %d\n', packets_sent_node1);
            fprintf('节点0接收数据包: %d\n', packets_received_node0);
            fprintf('包递交率(PDR): %.3f (%.1f%%)\n', pdr, pdr*100);
        else
            fprintf('无法计算PDR：发送数据包为0\n');
        end
        
    catch ME
        fprintf('读取.sca文件时出错: %s\n', ME.message);
    end
else
    fprintf('跳过.sca文件分析\n');
end

%% 3. 读取并分析.vec文件
if vec_exists
    fprintf('\n=== 分析.vec文件内容 ===\n');
    
    try
        fid = fopen(vec_file, 'r');
        if fid == -1
            error('无法打开.vec文件');
        end
        
        line_count = 0;
        data_count = 0;
        rssi_values = [];
        time_values = [];
        
        % 只读前100行数据作为示例
        while ~feof(fid) && data_count < 100
            line = fgetl(fid);
            line_count = line_count + 1;
            
            if ischar(line)
                % 跳过头部信息
                if startsWith(line, 'vector') || startsWith(line, 'version') || ...
                   startsWith(line, 'attr') || startsWith(line, 'run') || startsWith(line, 'file')
                    if line_count <= 20
                        fprintf('头部第%d行: %s\n', line_count, line);
                    end
                    continue;
                end
                
                % 检查数据行
                if ~isempty(line) && length(line) > 5
                    parts = strsplit(strtrim(line));
                    if length(parts) == 4
                        try
                            vector_id = str2double(parts{1});
                            event_num = str2double(parts{2});
                            time = str2double(parts{3});
                            value = str2double(parts{4});
                            
                            if vector_id == 0  % RSSI数据
                                data_count = data_count + 1;
                                rssi_values = [rssi_values; value];
                                time_values = [time_values; time];
                                
                                if data_count <= 10
                                    fprintf('数据第%d行: 时间=%.3f, RSSI=%.3f\n', data_count, time, value);
                                end
                            end
                        catch
                            % 跳过无法解析的行
                            continue;
                        end
                    end
                end
            end
        end
        fclose(fid);
        
        fprintf('读取的数据行数: %d\n', data_count);
        
        if ~isempty(rssi_values)
            fprintf('\n=== RSSI数据统计 ===\n');
            fprintf('RSSI最小值: %.3f\n', min(rssi_values));
            fprintf('RSSI最大值: %.3f\n', max(rssi_values));
            fprintf('RSSI平均值: %.3f\n', mean(rssi_values));
            fprintf('RSSI标准差: %.3f\n', std(rssi_values));
            fprintf('非零RSSI数量: %d\n', sum(rssi_values ~= 0));
            
            % 检查RSSI是否全为0
            if all(rssi_values == 0)
                fprintf('⚠️ 警告：所有RSSI值都为0\n');
                fprintf('可能原因：\n');
                fprintf('  1. 仿真中没有启用RSSI记录\n');
                fprintf('  2. 无线电配置问题\n');
                fprintf('  3. 仿真参数设置问题\n');
            end
            
            % 绘制RSSI时间序列
            figure('Position', [100, 100, 800, 400]);
            plot(time_values, rssi_values, 'b-', 'LineWidth', 1.5);
            xlabel('时间 (s)');
            ylabel('RSSI值');
            title('真实Castalia仿真RSSI数据');
            grid on;
            
            if all(rssi_values == 0)
                text(mean(time_values), 0.5, '所有RSSI值为0', ...
                     'HorizontalAlignment', 'center', 'FontSize', 14, ...
                     'BackgroundColor', 'yellow', 'EdgeColor', 'red');
            end
        else
            fprintf('没有找到RSSI数据\n');
        end
        
    catch ME
        fprintf('读取.vec文件时出错: %s\n', ME.message);
    end
else
    fprintf('跳过.vec文件分析\n');
end

%% 4. 总结和建议
fprintf('\n=== 数据可靠性总结 ===\n');

if sca_exists && vec_exists
    fprintf('✓ Castalia仿真文件存在\n');
    if exist('pdr', 'var')
        fprintf('✓ PDR数据可用: %.1f%%\n', pdr*100);
    end
    
    if exist('rssi_values', 'var') && ~isempty(rssi_values)
        if any(rssi_values ~= 0)
            fprintf('✓ RSSI数据可用且有变化\n');
        else
            fprintf('⚠️ RSSI数据全为0，建议使用模拟数据\n');
        end
    else
        fprintf('⚠️ 没有RSSI数据\n');
    end
else
    fprintf('✗ 缺少仿真文件\n');
end

fprintf('\n=== 建议 ===\n');
fprintf('1. 可以使用真实的PDR数据进行分析\n');
fprintf('2. 由于RSSI数据问题，建议使用模拟RSSI数据\n');
fprintf('3. 模拟数据基于合理假设，适合论文复现\n');

fprintf('\n测试完成！现在可以继续运行完整的复现程序。\n');
fprintf('建议运行: run(''run_all_reproductions.m'')\n');
