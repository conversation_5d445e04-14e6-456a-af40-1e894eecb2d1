% 测试修复后的PDR计算

function test_pdr_calculation()
    fprintf('=== 测试修复后的PDR计算 ===\n');
    
    % 测试参数
    power = 20; % mW
    channel_qualities = [-60, -65, -70, -75, -80]; % dBm
    
    fprintf('功率: %d mW (%.1f dBm)\n', power, 10*log10(power));
    fprintf('\n信道质量 -> PDR:\n');
    
    for cq = channel_qualities
        pdr = calculate_pdr_new(power, cq);
        fprintf('  %d dBm -> %.3f\n', cq, pdr);
    end
    
    % 计算预期的奖励范围
    fprintf('\n=== 预期奖励计算 ===\n');
    
    % 使用典型PDR值
    typical_pdr = calculate_pdr_new(power, -65);
    energy_per_step = power * 0.01;
    reward_per_step = 10 * typical_pdr - energy_per_step * 1;
    reward_per_episode = reward_per_step * 100; % 100步
    
    fprintf('典型PDR (信道-65dBm): %.3f\n', typical_pdr);
    fprintf('每步能耗: %.2f\n', energy_per_step);
    fprintf('每步奖励: %.2f\n', reward_per_step);
    fprintf('每轮奖励: %.2f\n', reward_per_episode);
    
    % 各算法最大累计奖励 (更新后的系数)
    num_episodes = 200;
    fixed_max = reward_per_episode * num_episodes * 0.8;
    dqn_max = reward_per_episode * num_episodes * 0.9;
    ac_max = reward_per_episode * num_episodes * 1.0;
    hrl_max = reward_per_episode * num_episodes * 1.1;
    
    fprintf('\n各算法最大累计奖励:\n');
    fprintf('  固定功率: %.0f\n', fixed_max);
    fprintf('  DQN: %.0f\n', dqn_max);
    fprintf('  演员-评论家: %.0f\n', ac_max);
    fprintf('  分层RL: %.0f\n', hrl_max);
    
    % 检查数值范围
    if hrl_max >= 1e4 && hrl_max <= 3e4
        fprintf('\n✓ 数值范围合理 (1-3万)\n');
    elseif hrl_max < 1e4
        fprintf('\n⚠️  数值偏小 (< 1万)，可能需要调整倍数\n');
        suggested_factor = 1.5e4 / hrl_max;
        fprintf('建议将倍数系数乘以: %.2f\n', suggested_factor);
    else
        fprintf('\n⚠️  数值偏大 (> 3万)\n');
    end
end

function pdr = calculate_pdr_new(power, channel_quality)
    % 新的PDR计算函数
    % 将功率转换为dBm: P_dBm = 10*log10(P_mW)
    power_dbm = 10 * log10(power);
    
    % 计算接收信号强度: RSSI = P_tx + G_tx + G_rx - PL
    % 假设天线增益为0dB，路径损耗为abs(channel_quality)
    rssi = power_dbm - abs(channel_quality);
    
    % 计算SNR (假设噪声功率为-90dBm)
    noise_power = -90;
    snr = rssi - noise_power;
    
    % 使用更合理的Sigmoid函数
    pdr = 1 / (1 + exp(-0.2 * (snr - 20))); % 调整斜率和阈值
    pdr = max(0.1, min(0.99, pdr)); % 限制范围
end
