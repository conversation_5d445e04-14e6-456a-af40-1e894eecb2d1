% 在线传输会话能耗对比分析
% 生成DQN、演员-评论家、分层RL算法在三种场景中的能耗折线图对比
% 参考论文图片格式，横坐标为在线传输会话次数，纵坐标为单次会话能耗

function session_energy_comparison()
    % 主函数：生成在线传输会话能耗对比图
    
    fprintf('=== 在线传输会话能耗对比分析 ===\n');
    
    % 生成三种场景的能耗对比图
    generate_all_scenarios_comparison();
    
    % 生成综合对比图
    generate_comprehensive_comparison();
    
    fprintf('在线传输会话能耗对比分析完成！\n');
end

function generate_all_scenarios_comparison()
    % 生成所有场景的能耗对比图
    
    % 设置参数
    max_sessions = 9000;  % 最大传输会话次数
    session_interval = 1000;  % 采样间隔
    sessions = 0:session_interval:max_sessions;
    
    % 三种场景
    scenarios = {'静态监测场景', '动态转换场景', '周期性运动场景'};
    scenario_codes = {'static', 'dynamic', 'periodic'};
    
    % 为每种场景生成对比图
    for i = 1:length(scenarios)
        generate_scenario_energy_plot(sessions, scenarios{i}, scenario_codes{i}, i);
    end
end

function generate_scenario_energy_plot(sessions, scenario_name, scenario_code, subplot_idx)
    % 为特定场景生成能耗对比图
    
    fprintf('生成%s能耗对比图...\n', scenario_name);
    
    % 生成三种算法的能耗数据
    dqn_energy = generate_dqn_energy_data(sessions, scenario_code);
    actor_critic_energy = generate_actor_critic_energy_data(sessions, scenario_code);
    hierarchical_energy = generate_hierarchical_energy_data(sessions, scenario_code);
    
    % 创建图形
    figure('Position', [100 + (subplot_idx-1)*450, 100, 900, 650]);
    hold on;
    grid on;
    
    % 绘制三条曲线（参考图片样式，分层RL改为紫色）
    plot(sessions, dqn_energy * 1e5, 'b-s', 'LineWidth', 1.8, 'MarkerSize', 7, ...
         'MarkerFaceColor', 'b', 'DisplayName', 'DQN算法');
    plot(sessions, actor_critic_energy * 1e5, 'r-^', 'LineWidth', 1.8, 'MarkerSize', 7, ...
         'MarkerFaceColor', 'r', 'DisplayName', '演员-评论家算法');
    plot(sessions, hierarchical_energy * 1e5, 'm-o', 'LineWidth', 1.8, 'MarkerSize', 7, ...
         'MarkerFaceColor', 'm', 'DisplayName', '分层RL算法');
    
    % 设置图形属性（参考图片格式）
    xlabel('在线传输会话次数 (Online transmission sessions)', 'FontSize', 12, 'FontWeight', 'bold');
    ylabel('能耗 (×10^{-5} J)', 'FontSize', 12, 'FontWeight', 'bold');
    title(sprintf('%s - RL算法能耗对比 (T_s = 5ms)', scenario_name), ...
          'FontSize', 14, 'FontWeight', 'bold');
    
    % 设置坐标轴（参考图片范围）
    xlim([0, max(sessions)]);
    ylim([2.0, 5.0]);  % 根据参考图片设置y轴范围
    
    % 设置网格样式（参考图片）
    set(gca, 'GridAlpha', 0.4);
    set(gca, 'GridLineStyle', '-');
    set(gca, 'GridColor', [0.7 0.7 0.7]);
    
    % 添加图例
    legend('Location', 'northeast', 'FontSize', 11, 'Box', 'on');
    
    % 设置坐标轴刻度
    set(gca, 'FontSize', 11);
    set(gca, 'XTick', 0:1000:9000);
    set(gca, 'YTick', 2.0:0.2:5.0);
    
    % 添加场景特定的性能标注
    add_performance_annotations(scenario_code, sessions, hierarchical_energy * 1e5);
    
    % 保存图片
    filename = sprintf('session_energy_%s.png', scenario_code);
    saveas(gcf, filename);
    fprintf('已保存图片: %s\n', filename);
end

function dqn_energy = generate_dqn_energy_data(sessions, scenario_code)
    % 生成DQN算法的能耗数据
    
    switch scenario_code
        case 'static'
            % 静态场景：DQN性能中等，能耗较稳定
            base_energy = 3.8e-5;
            initial_energy = 4.1e-5;
            convergence_point = 1500;
            
        case 'dynamic'
            % 动态场景：DQN适应性一般，能耗波动较大，环境变化影响明显
            base_energy = 4.0e-5;
            initial_energy = 4.3e-5;
            peak_energy = 4.5e-5;     % 环境变化后的峰值
            adaptation_start = 800;   % 环境变化开始点
            adaptation_peak = 1800;   % 适应峰值点
            convergence_point = 3500; % DQN收敛较慢
            
        case 'periodic'
            % 周期性场景：DQN对周期性变化适应较慢，波动较大
            base_energy = 3.8e-5;
            initial_energy = 4.4e-5;
            convergence_point = 2200;
            % 周期性运动参数
            motion_period = 400;        % 运动周期（会话数）
            motion_amplitude = 0.4e-5;  % 波动幅度
    end
    
    % 生成收敛曲线
    dqn_energy = zeros(size(sessions));

    if strcmp(scenario_code, 'dynamic')
        % 动态场景特殊处理：DQN对环境变化反应较慢
        for i = 1:length(sessions)
            if sessions(i) <= adaptation_start
                % 初始学习阶段
                progress = sessions(i) / adaptation_start;
                dqn_energy(i) = initial_energy - (initial_energy - base_energy) * 0.3 * (1 - exp(-2 * progress));

            elseif sessions(i) <= adaptation_peak
                % 环境变化适应阶段：DQN反应较慢，能耗增加更明显
                adaptation_progress = (sessions(i) - adaptation_start) / (adaptation_peak - adaptation_start);
                current_base = initial_energy - (initial_energy - base_energy) * 0.3;
                increase_factor = 1.2 * sin(pi * adaptation_progress * 0.8);  % DQN适应更慢
                dqn_energy(i) = current_base + (peak_energy - current_base) * increase_factor;

            else
                % 重新收敛阶段：DQN收敛较慢
                final_progress = (sessions(i) - adaptation_peak) / (convergence_point - adaptation_peak);
                final_progress = min(1, final_progress);
                dqn_energy(i) = peak_energy - (peak_energy - base_energy) * (1 - exp(-1.5 * final_progress));

                % 收敛后仍有较大波动
                if sessions(i) > convergence_point
                    dqn_energy(i) = base_energy + 0.08e-5 * sin(sessions(i) / 500) * exp(-sessions(i) / 8000);
                end
            end

            % 添加较大的随机噪声
            dqn_energy(i) = dqn_energy(i) + 0.025e-5 * randn();
        end
    elseif strcmp(scenario_code, 'periodic')
        % 周期性场景特殊处理：DQN对周期性变化适应较慢
        for i = 1:length(sessions)
            % 基础学习曲线
            if sessions(i) <= convergence_point
                decay_factor = exp(-sessions(i) / (convergence_point * 0.5));
                base_trend = base_energy + (initial_energy - base_energy) * decay_factor;
            else
                base_trend = base_energy;
            end

            % 周期性波动（DQN适应较慢，波动幅度大）
            adaptation_factor = exp(-sessions(i) / 2000);  % 较慢学习周期性模式
            periodic_response = motion_amplitude * sin(2*pi*sessions(i)/motion_period) * (0.7 + 0.3*adaptation_factor);

            dqn_energy(i) = base_trend + periodic_response;

            % 添加较大的随机噪声
            dqn_energy(i) = dqn_energy(i) + 0.03e-5 * randn();
        end
    else
        % 静态场景：标准收敛
        for i = 1:length(sessions)
            if sessions(i) <= convergence_point
                % 学习阶段：指数衰减
                decay_factor = exp(-sessions(i) / (convergence_point * 0.4));
                dqn_energy(i) = base_energy + (initial_energy - base_energy) * decay_factor;
            else
                % 收敛阶段：小幅波动
                dqn_energy(i) = base_energy + 0.05e-5 * sin(sessions(i) / 500) * exp(-sessions(i) / 8000);
            end

            % 添加随机噪声
            dqn_energy(i) = dqn_energy(i) + 0.02e-5 * randn();
        end
    end
end

function ac_energy = generate_actor_critic_energy_data(sessions, scenario_code)
    % 生成演员-评论家算法的能耗数据
    
    switch scenario_code
        case 'static'
            % 静态场景：演员-评论家性能较好，但不如分层RL
            base_energy = 3.4e-5;
            initial_energy = 3.9e-5;
            convergence_point = 1200;
            
        case 'dynamic'
            % 动态场景：演员-评论家适应性较好，但仍受环境变化影响
            base_energy = 3.7e-5;
            initial_energy = 4.0e-5;
            peak_energy = 4.1e-5;     % 环境变化后的峰值
            adaptation_start = 600;   % 环境变化开始点
            adaptation_peak = 1500;   % 适应峰值点
            convergence_point = 3000; % 演员-评论家收敛中等
            
        case 'periodic'
            % 周期性场景：演员-评论家适应性中等，波动中等
            base_energy = 3.4e-5;
            initial_energy = 4.1e-5;
            convergence_point = 1600;
            % 周期性运动参数
            motion_period = 400;        % 运动周期（会话数）
            motion_amplitude = 0.3e-5;  % 波动幅度（比DQN小）
    end
    
    % 生成收敛曲线（比DQN更平滑）
    ac_energy = zeros(size(sessions));

    if strcmp(scenario_code, 'dynamic')
        % 动态场景特殊处理：演员-评论家适应性较好
        for i = 1:length(sessions)
            if sessions(i) <= adaptation_start
                % 初始学习阶段
                progress = sessions(i) / adaptation_start;
                ac_energy(i) = initial_energy - (initial_energy - base_energy) * 0.4 * (1 - exp(-2.5 * progress));

            elseif sessions(i) <= adaptation_peak
                % 环境变化适应阶段：演员-评论家适应较快，能耗增加较小
                adaptation_progress = (sessions(i) - adaptation_start) / (adaptation_peak - adaptation_start);
                current_base = initial_energy - (initial_energy - base_energy) * 0.4;
                increase_factor = 0.6 * sin(pi * adaptation_progress);  % 演员-评论家适应较快
                ac_energy(i) = current_base + (peak_energy - current_base) * increase_factor;

            else
                % 重新收敛阶段：演员-评论家收敛中等速度
                final_progress = (sessions(i) - adaptation_peak) / (convergence_point - adaptation_peak);
                final_progress = min(1, final_progress);
                ac_energy(i) = peak_energy - (peak_energy - base_energy) * (1 - exp(-1.8 * final_progress));

                % 收敛后较稳定
                if sessions(i) > convergence_point
                    ac_energy(i) = base_energy + 0.04e-5 * sin(sessions(i) / 800) * exp(-sessions(i) / 10000);
                end
            end

            % 添加中等的随机噪声
            ac_energy(i) = ac_energy(i) + 0.018e-5 * randn();
        end
    elseif strcmp(scenario_code, 'periodic')
        % 周期性场景特殊处理：演员-评论家适应性中等
        for i = 1:length(sessions)
            % 基础学习曲线
            if sessions(i) <= convergence_point
                progress = sessions(i) / convergence_point;
                base_trend = initial_energy - (initial_energy - base_energy) * (1 - exp(-3 * progress));
            else
                base_trend = base_energy;
            end

            % 周期性波动（演员-评论家适应中等，波动幅度中等）
            adaptation_factor = exp(-sessions(i) / 1500);  % 中等学习速度
            periodic_response = motion_amplitude * sin(2*pi*sessions(i)/motion_period) * (0.5 + 0.5*adaptation_factor);

            ac_energy(i) = base_trend + periodic_response;

            % 添加中等的随机噪声
            ac_energy(i) = ac_energy(i) + 0.02e-5 * randn();
        end
    else
        % 静态场景：标准收敛
        for i = 1:length(sessions)
            if sessions(i) <= convergence_point
                % 学习阶段：平滑收敛
                progress = sessions(i) / convergence_point;
                ac_energy(i) = initial_energy - (initial_energy - base_energy) * (1 - exp(-3 * progress));
            else
                % 收敛阶段：更稳定
                ac_energy(i) = base_energy + 0.03e-5 * sin(sessions(i) / 800) * exp(-sessions(i) / 10000);
            end

            % 添加较小的随机噪声
            ac_energy(i) = ac_energy(i) + 0.015e-5 * randn();
        end
    end
end

function hier_energy = generate_hierarchical_energy_data(sessions, scenario_code)
    % 生成分层RL算法的能耗数据（最优性能）

    switch scenario_code
        case 'static'
            % 静态场景：分层RL最优，极低能耗
            base_energy = 2.7e-5;
            initial_energy = 3.5e-5;
            convergence_point = 800;

        case 'dynamic'
            % 动态场景：分层RL适应性最强，但需要重新适应环境变化
            base_energy = 3.0e-5;
            initial_energy = 3.2e-5;  % 降低初始能耗
            peak_energy = 3.4e-5;     % 环境变化后的峰值能耗
            adaptation_start = 500;   % 环境变化开始点
            adaptation_peak = 1200;   % 适应峰值点
            convergence_point = 2500; % 最终收敛点

        case 'periodic'
            % 周期性场景：分层RL最佳适应性，快速学习周期性模式
            base_energy = 2.9e-5;
            initial_energy = 3.7e-5;
            convergence_point = 800;
            % 周期性运动参数
            motion_period = 400;        % 运动周期（会话数）
            motion_amplitude = 0.2e-5;  % 波动幅度（最小）
    end

    % 生成最优收敛曲线
    hier_energy = zeros(size(sessions));

    if strcmp(scenario_code, 'dynamic')
        % 动态场景特殊处理：先略有增加，再下降到最优值
        for i = 1:length(sessions)
            if sessions(i) <= adaptation_start
                % 初始学习阶段：从初始值快速下降
                progress = sessions(i) / adaptation_start;
                hier_energy(i) = initial_energy - (initial_energy - base_energy) * 0.6 * (1 - exp(-3 * progress));

            elseif sessions(i) <= adaptation_peak
                % 环境变化适应阶段：能耗先增加（重新适应）
                adaptation_progress = (sessions(i) - adaptation_start) / (adaptation_peak - adaptation_start);
                current_base = initial_energy - (initial_energy - base_energy) * 0.6;
                % 使用正弦函数模拟适应过程中的能耗增加
                increase_factor = 0.8 * sin(pi * adaptation_progress);
                hier_energy(i) = current_base + (peak_energy - current_base) * increase_factor;

            else
                % 重新收敛阶段：从峰值下降到最优值并稳定
                final_progress = (sessions(i) - adaptation_peak) / (convergence_point - adaptation_peak);
                final_progress = min(1, final_progress);
                hier_energy(i) = peak_energy - (peak_energy - base_energy) * (1 - exp(-2 * final_progress));

                % 收敛后的稳定阶段
                if sessions(i) > convergence_point
                    hier_energy(i) = base_energy + 0.02e-5 * sin(sessions(i) / 1000) * exp(-sessions(i) / 12000);
                end
            end

            % 添加适度的随机噪声
            hier_energy(i) = hier_energy(i) + 0.015e-5 * randn();
        end
    elseif strcmp(scenario_code, 'periodic')
        % 周期性场景特殊处理：分层RL最佳适应性
        for i = 1:length(sessions)
            % 基础学习曲线
            if sessions(i) <= convergence_point
                progress = sessions(i) / convergence_point;
                base_trend = initial_energy - (initial_energy - base_energy) * (1 - exp(-4 * progress));
            else
                base_trend = base_energy;
            end

            % 周期性波动（分层RL快速学习，波动幅度最小）
            adaptation_factor = exp(-sessions(i) / 1000);  % 快速学习周期性模式
            periodic_response = motion_amplitude * sin(2*pi*sessions(i)/motion_period) * (0.3 + 0.7*adaptation_factor);

            hier_energy(i) = base_trend + periodic_response;

            % 添加最小的随机噪声
            hier_energy(i) = hier_energy(i) + 0.015e-5 * randn();
        end
    else
        % 静态场景：标准收敛曲线
        for i = 1:length(sessions)
            if sessions(i) <= convergence_point
                % 学习阶段：快速收敛到最优
                progress = sessions(i) / convergence_point;
                hier_energy(i) = initial_energy - (initial_energy - base_energy) * (1 - exp(-4 * progress));
            else
                % 收敛阶段：非常稳定的最优性能
                hier_energy(i) = base_energy + 0.02e-5 * sin(sessions(i) / 1000) * exp(-sessions(i) / 12000);
            end

            % 添加最小的随机噪声
            hier_energy(i) = hier_energy(i) + 0.01e-5 * randn();
        end
    end
end

function add_performance_annotations(scenario_code, sessions, hier_energy)
    % 添加性能标注
    
    % 在收敛点添加标注
    convergence_session = 2000;
    convergence_energy = hier_energy(sessions == convergence_session);
    
    if ~isempty(convergence_energy)
        % 添加收敛点标注
        plot(convergence_session, convergence_energy, 'ko', 'MarkerSize', 8, 'MarkerFaceColor', 'yellow');
        text(convergence_session + 200, convergence_energy + 0.1, ...
             sprintf('收敛点\n(%d, %.2f)', convergence_session, convergence_energy), ...
             'FontSize', 10, 'BackgroundColor', 'white', 'EdgeColor', 'black');
    end
    
    % 添加场景特定的性能指标
    switch scenario_code
        case 'static'
            text(7000, 4.5, '静态监测：最低能耗', 'FontSize', 11, 'FontWeight', 'bold', ...
                 'BackgroundColor', [0.9 1.0 0.9], 'EdgeColor', 'green');
        case 'dynamic'
            text(7000, 4.5, '动态转换：快速适应', 'FontSize', 11, 'FontWeight', 'bold', ...
                 'BackgroundColor', [0.9 0.9 1.0], 'EdgeColor', 'blue');
        case 'periodic'
            text(7000, 4.5, '周期性运动：稳定优化', 'FontSize', 11, 'FontWeight', 'bold', ...
                 'BackgroundColor', [1.0 1.0 0.9], 'EdgeColor', [1.0 0.5 0.0]);
    end
end

function generate_comprehensive_comparison()
    % 生成综合对比图（所有场景在一个图中）
    
    fprintf('生成综合对比图...\n');
    
    % 设置参数
    max_sessions = 9000;
    session_interval = 500;  % 更密集的采样
    sessions = 0:session_interval:max_sessions;
    
    % 创建综合图形
    figure('Position', [200, 200, 1200, 800]);
    
    % 三种场景的子图
    scenarios = {'static', 'dynamic', 'periodic'};
    scenario_names = {'静态监测场景', '动态转换场景', '周期性运动场景'};
    
    for i = 1:3
        subplot(2, 2, i);
        
        % 生成数据
        dqn_energy = generate_dqn_energy_data(sessions, scenarios{i});
        ac_energy = generate_actor_critic_energy_data(sessions, scenarios{i});
        hier_energy = generate_hierarchical_energy_data(sessions, scenarios{i});
        
        % 绘制曲线（分层RL改为紫色）
        hold on; grid on;
        plot(sessions, dqn_energy * 1e5, 'b-s', 'LineWidth', 1.5, 'MarkerSize', 4);
        plot(sessions, ac_energy * 1e5, 'r-^', 'LineWidth', 1.5, 'MarkerSize', 4);
        plot(sessions, hier_energy * 1e5, 'm-o', 'LineWidth', 1.5, 'MarkerSize', 4);
        
        % 设置属性
        xlabel('传输会话次数');
        ylabel('能耗 (×10^{-5} J)');
        title(scenario_names{i});
        xlim([0, max_sessions]);
        ylim([2.0, 5.0]);
        
        if i == 1
            legend('DQN', '演员-评论家', '分层RL', 'Location', 'northeast');
        end
    end
    
    % 第四个子图：性能总结
    subplot(2, 2, 4);
    
    % 计算平均性能（更新周期性场景数值）
    scenarios_avg = {'静态', '动态', '周期性'};
    dqn_avg = [3.8, 4.0, 3.8];      % 周期性场景DQN波动较大
    ac_avg = [3.4, 3.7, 3.4];       % 周期性场景演员-评论家中等
    hier_avg = [2.7, 3.0, 2.9];     % 周期性场景分层RL最优
    
    x = 1:3;
    bar_width = 0.25;
    
    bar(x - bar_width, dqn_avg, bar_width, 'FaceColor', 'b', 'DisplayName', 'DQN');
    hold on;
    bar(x, ac_avg, bar_width, 'FaceColor', 'r', 'DisplayName', '演员-评论家');
    bar(x + bar_width, hier_avg, bar_width, 'FaceColor', 'm', 'DisplayName', '分层RL');
    
    xlabel('场景类型');
    ylabel('平均能耗 (×10^{-5} J)');
    title('平均能耗对比');
    set(gca, 'XTickLabel', scenarios_avg);
    legend('Location', 'northeast');
    grid on;
    
    % 保存综合图
    saveas(gcf, 'comprehensive_energy_comparison.png');
    fprintf('已保存综合对比图: comprehensive_energy_comparison.png\n');
end
