# ****************************************************************************
# *  Copyright: National ICT Australia,  2009 - 2010                         *
# *  Developed at the ATP lab, Networked Systems research theme              *
# *  Author(s): <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>                        *
# *  This file is distributed under the terms in the attached LICENSE file.  *
# *  If you do not find this file, copies can be found by writing to:        *
# *                                                                          *
# *      NICTA, Locked Bag 9013, Alexandria, NSW 1435, Australia             *
# *      Attention:  License Inquiry.                                        *
# *                                                                          *
# ***************************************************************************/


RX MODES
# Name, dataRate(kbps), modulationType, bitsPerSymbol, bandwidth(MHz), noiseBandwidth(MHz), noiseFloor(dBm), sensitivity(dBm), powerConsumed(mW)
normal, 19.2, FSK, 1, 10, 30, -105, -98, 22.2
IDEAL, 19.2, IDEAL, 1, 10, 30, -105, -98, 22.2

TX LEVELS
Tx_dBm 10 5 0 -5 -10 -15 -20
Tx_mW 80.1 44.4 31.2 26.7 23.7 22.2 15.9

DELAY TRANSITION MATRIX
# State switching times (time to switch from column state to row state, in msec)
#       RX      TX      SLEEP
RX      -       0.01    0.2
TX      0.01    -       0.2
SLEEP   0.05    0.05    -

POWER TRANSITION MATRIX
#       RX      TX      SLEEP
RX      -       22.2    22.2
TX      22.2     -      22.2
SLEEP   0.5     0.5     -

SLEEP LEVELS
idle 0.0006, -, -, -, -
