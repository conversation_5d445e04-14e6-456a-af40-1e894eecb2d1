% 调试经验回放内存问题
function debug_memory_issue()
    fprintf('=== 调试经验回放内存问题 ===\n');
    
    % 创建环境
    env = rl_environment();
    fprintf('环境创建完成，状态维度: %d, 动作维度: %d\n', env.state_dim, env.action_dim);
    
    % 创建智能体
    agent = create_simplified_hierarchical_agent(env);
    fprintf('智能体创建完成，内存大小: %d\n', agent.memory_size);
    
    % 测试基本功能
    test_basic_functions(agent, env);
    
    % 测试经验存储
    test_experience_storage(agent, env);
    
    % 测试训练循环
    test_training_loop(agent, env);
    
    fprintf('调试完成!\n');
end

function test_basic_functions(agent, env)
    fprintf('\n--- 测试基本功能 ---\n');
    
    % 测试环境重置
    try
        state = env.reset();
        fprintf('✓ 环境重置成功，状态大小: %dx%d\n', size(state, 1), size(state, 2));
    catch ME
        fprintf('✗ 环境重置失败: %s\n', ME.message);
        return;
    end
    
    % 测试动作选择
    try
        action = select_action(agent, state);
        fprintf('✓ 动作选择成功，动作: %d\n', action);
    catch ME
        fprintf('✗ 动作选择失败: %s\n', ME.message);
        return;
    end
    
    % 测试环境步进
    try
        [next_state, reward, done, info] = env.step(action);
        fprintf('✓ 环境步进成功，奖励: %.2f, 完成: %d\n', reward, done);
    catch ME
        fprintf('✗ 环境步进失败: %s\n', ME.message);
        return;
    end
end

function test_experience_storage(agent, env)
    fprintf('\n--- 测试经验存储 ---\n');
    
    % 重置环境
    state = env.reset();
    
    % 存储几个经验
    for i = 1:10
        action = randi(agent.action_dim);
        [next_state, reward, done, ~] = env.step(action);
        
        fprintf('存储经验 %d: 动作=%d, 奖励=%.2f\n', i, action, reward);
        
        % 手动调用存储函数
        try
            agent = store_experience(agent, state, action, reward, next_state, done);
            fprintf('  ✓ 经验存储成功，当前内存大小: %d\n', numel(agent.memory));
        catch ME
            fprintf('  ✗ 经验存储失败: %s\n', ME.message);
        end
        
        state = next_state;
        if done
            state = env.reset();
        end
    end
    
    fprintf('最终内存大小: %d\n', numel(agent.memory));
    
    % 检查存储的经验
    if numel(agent.memory) > 0
        exp = agent.memory{1};
        fprintf('第一个经验: 动作=%d, 奖励=%.2f\n', exp.action, exp.reward);
    end
end

function test_training_loop(agent, env)
    fprintf('\n--- 测试训练循环 ---\n');
    
    % 设置训练参数
    params = struct();
    params.batch_size = 4;  % 小批次
    params.update_frequency = 5;
    params.max_steps_per_episode = 20;
    
    state = env.reset();
    loss_values = [];
    
    for step = 1:params.max_steps_per_episode
        % 选择动作
        action = select_action(agent, state);
        
        % 环境步进
        [next_state, reward, done, ~] = env.step(action);
        
        % 存储经验
        agent = store_experience(agent, state, action, reward, next_state, done);
        
        fprintf('步骤 %d: 动作=%d, 奖励=%.2f, 内存=%d\n', ...
                step, action, reward, numel(agent.memory));
        
        % 尝试更新
        if mod(step, params.update_frequency) == 0
            try
                loss_val = update_agent(agent, params);
                loss_values(end+1) = loss_val;
                fprintf('  更新成功，损失=%.4f\n', loss_val);
            catch ME
                fprintf('  更新失败: %s\n', ME.message);
            end
        end
        
        state = next_state;
        if done
            break;
        end
    end
    
    fprintf('训练循环完成，损失值: ');
    fprintf('%.2f ', loss_values);
    fprintf('\n');
end

% 复制必要的函数
function agent = create_simplified_hierarchical_agent(env)
    agent = struct();
    agent.state_dim   = env.state_dim;
    agent.action_dim  = env.action_dim;
    agent.hidden_size = 32;  % 减小网络大小
    agent.learning_rate   = 0.001;
    agent.epsilon         = 1.0;
    agent.epsilon_decay   = 0.995;
    agent.epsilon_min     = 0.01;
    agent.gamma           = 0.95;
    agent.memory_size     = 1000;  % 减小内存大小
    
    agent.upper_weights = initialize_network_weights(env.state_dim, agent.hidden_size, 4);
    agent.lower_weights = initialize_network_weights(env.state_dim+4, agent.hidden_size, env.action_dim);
    
    agent.memory      = cell(0);
    agent.memory_idx  = 1;
    agent.update_counter = 0;
end

function weights = initialize_network_weights(in_dim, hid_dim, out_dim)
    weights = struct();
    weights.W1 = randn(hid_dim, in_dim) * sqrt(2/in_dim);
    weights.b1 = zeros(hid_dim, 1);
    weights.W2 = randn(hid_dim, hid_dim) * sqrt(2/hid_dim);
    weights.b2 = zeros(hid_dim, 1);
    weights.W3 = randn(out_dim, hid_dim) * sqrt(2/hid_dim);
    weights.b3 = zeros(out_dim, 1);
end

function action = select_action(agent, state)
    if rand() < agent.epsilon
        action = randi(agent.action_dim);
    else
        action = get_best_action(agent, state);
    end
end

function action = get_best_action(agent, state)
    upper_out = forward_pass_local(agent.upper_weights, state);
    policy_w  = softmax_local(upper_out);
    input_low = [state; policy_w];
    q_values  = forward_pass_local(agent.lower_weights, input_low);
    [~,action] = max(q_values);
end

function agent = store_experience(agent, state, action, reward, next_state, done)
    exp = struct();
    exp.state = state;
    exp.action = action;
    exp.reward = reward;
    exp.next_state = next_state;
    exp.done = done;
    
    if numel(agent.memory) < agent.memory_size
        agent.memory{end+1} = exp;
    else
        agent.memory{agent.memory_idx} = exp;
        agent.memory_idx = mod(agent.memory_idx, agent.memory_size) + 1;
    end
end

function loss_val = update_agent(agent, params)
    memory_size = numel(agent.memory);
    if memory_size < 2  % 至少需要2个样本
        loss_val = 1000;
        return; 
    end
    
    actual_batch_size = min(params.batch_size, memory_size);
    idx = randperm(memory_size, actual_batch_size);
    batch = agent.memory(idx);
    
    loss_acc = 0;
    
    for i = 1:actual_batch_size
        exp = batch{i};
        [up_out, ~] = forward_pass_local(agent.upper_weights, exp.state);
        pw = softmax_local(up_out);
        [low_out, ~] = forward_pass_local(agent.lower_weights, [exp.state; pw]);
        q_pred = low_out(exp.action);
        
        if exp.done
            target_q = exp.reward;
        else
            best_next = get_best_action(agent, exp.next_state);
            next_q = get_q_value(agent, exp.next_state, best_next);
            target_q = exp.reward + agent.gamma * next_q;
        end
        
        diff = q_pred - target_q;
        loss_acc = loss_acc + diff^2;
    end
    
    agent.update_counter = agent.update_counter + 1;
    loss_val = loss_acc / actual_batch_size;
end

function [out, cache] = forward_pass_local(W, x)
    z1 = W.W1*x + W.b1; a1 = max(0, z1);
    z2 = W.W2*a1 + W.b2; a2 = max(0, z2);
    out = W.W3*a2 + W.b3;
    if nargout>1, cache = struct('x',x,'z1',z1,'a1',a1,'z2',z2,'a2',a2); end
end

function s = softmax_local(x)
    ex = exp(x - max(x)); s = ex/sum(ex);
end

function q = get_q_value(agent, s, a)
    up = forward_pass_local(agent.upper_weights, s);
    pw = softmax_local(up);
    low = forward_pass_local(agent.lower_weights, [s;pw]);
    q = low(a);
end
