# ****************************************************************************
# *  Copyright: National ICT Australia,  2007 - 2010                         *
# *  Developed at the ATP lab, Networked Systems research theme              *
# *  Author(s): <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>                        *
# *  This file is distributed under the terms in the attached LICENSE file.  *
# *  If you do not find this file, copies can be found by writing to:        *
# *                                                                          *
# *      NICTA, Locked Bag 9013, Alexandria, NSW 1435, Australia             *
# *      Attention:  License Inquiry.                                        *
# *                                                                          *
# ***************************************************************************/

#!/bin/bash

ROOT=`pwd`

# The following options are used by OMNeT's opp_makemake tool
# For explanation and a complete list of options run: opp_makemake -h
OPTS=" -f -r --deep -o CastaliaBin -u Cmdenv -P $ROOT -M release"

# -X excludes a directory from being considered
EXCLUDEDIRS=" -X Simulations -X out -X bin"

# Use options -I -L -l to include external header files or libraries
EXTOPTS=""

# Run OMNeT's opp_makemake tool with the above options
opp_makemake $OPTS $EXCLUDEDIRS $EXTOPTS

