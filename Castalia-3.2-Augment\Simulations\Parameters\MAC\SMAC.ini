# ****************************************************************************
# *  Copyright: National ICT Australia,  2007 - 2010                         *
# *  Developed at the ATP lab, Networked Systems research theme              *
# *  Author(s): <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>                        *
# *  This file is distributed under the terms in the attached LICENSE file.  *
# *  If you do not find this file, copies can be found by writing to:        *
# *                                                                          *
# *      NICTA, Locked Bag 9013, Alexandria, NSW 1435, Australia             *
# *      Attention:  License Inquiry.                                        *
# *                                                                          *
# ***************************************************************************/

#===========================================================================
# Castalia does not have a dedicated SMAC module - instead we implement SMAC
# by modifying relevant parameters of TMAC module
#===========================================================================

SN.node[*].communication.MACProtocolName = "TMAC"

SN.node[*].communication.MAC.listenTimeout = 61	
SN.node[*].communication.MAC.disableTAextension = true  
SN.node[*].communication.MAC.conservativeTA = false
SN.node[*].communication.MAC.collisionResolution = 0
