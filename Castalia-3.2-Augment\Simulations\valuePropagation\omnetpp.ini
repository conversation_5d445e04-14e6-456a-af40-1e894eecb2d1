# ****************************************************************************
# *  Copyright: National ICT Australia,  2007 - 2010                         *
# *  Developed at the ATP lab, Networked Systems research theme              *
# *  Author(s): <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>                        *
# *  This file is distributed under the terms in the attached LICENSE file.  *
# *  If you do not find this file, copies can be found by writing to:        *
# *                                                                          *
# *      NICTA, Locked Bag 9013, Alexandria, NSW 1435, Australia             *
# *      Attention:  License Inquiry.                                        *
# *                                                                          *
# ***************************************************************************/

[General]

# ==============================================
# Always include the main Castalia.ini file
# ==============================================

include ../Parameters/Castalia.ini

sim-time-limit = 10s

SN.field_x = 60		# meters
SN.field_y = 60		# meters

SN.numNodes = 16
SN.deployment = "4x4"

include ../Parameters/PhysicalProcess/node0_asssignedValue40.ini

SN.node[*].Communication.Radio.RadioParametersFile = "../Parameters/Radio/CC2420.txt"
SN.node[*].Communication.MACProtocolName = "TunableMAC"
SN.node[*].ApplicationName = "ValuePropagation"

SN.node[*].Communication.MAC.listenInterval = 10
SN.node[*].Communication.MAC.dutyCycle = 0.1
SN.node[*].Communication.MAC.beaconIntervalFraction = 1.0

SN.node[*].Communication.Radio.TxOutputPower = "0dBm"


[Config varyDutyCycle]
SN.node[*].Communication.MAC.dutyCycle = ${dutyCycle= 0.02, 0.05, 0.1}

[Config varyBeacon]
SN.node[*].Communication.MAC.beaconIntervalFraction = ${beaconFraction= 0.2, 0.5, 0.8}

[Config varyTxPower]
SN.node[*].Communication.Radio.TxOutputPower = ${TXpower="-1dBm","-5dBm"}

[Config debug]
SN.node[*].Communication.MAC.collectTraceInfo = true
SN.node[*].Application.collectTraceInfo = true

[Config naiveChannel]
SN.wirelessChannel.sigma = 0
SN.wirelessChannel.bidirectionalSigma = 0

[Config beaconSize]
# have at least 2 beacons in a listening interval
# default is 125 bytes -> 4.2msec TX time-> 2.4 beacons in 10ms
SN.node[*].Communication.MAC.beaconFrameSize = 50  # in bytes
