% 测试改进的算法验证脚本
function test_improved_verification()
    close all;
    clear;
    clc;
    
    fprintf('=== 测试改进的算法验证脚本 ===\n');
    
    % 模拟环境和结果
    all_results = struct();
    all_results.fixed_power = struct('energy', 95, 'pdr', 0.75, 'delay', 25);
    all_results.emg_tpc = struct('energy', 85, 'pdr', 0.78, 'delay', 23);
    all_results.hr_tpc = struct('energy', 80, 'pdr', 0.80, 'delay', 22);
    all_results.simple_dqn = struct('energy', 75, 'pdr', 0.82, 'delay', 20);
    all_results.actor_critic = struct('energy', 70, 'pdr', 0.84, 'delay', 18);
    all_results.improved_hierarchical_rl = struct('energy', 65, 'pdr', 0.86, 'delay', 16);
    
    % 创建测试可视化
    create_test_visualization(all_results);
    
    fprintf('✓ 测试完成，图表已生成\n');
end

function create_test_visualization(all_results)
    % 创建测试可视化
    
    % 设置中文字体
    try
        set(0, 'DefaultAxesFontName', 'SimHei');
        set(0, 'DefaultTextFontName', 'SimHei');
    catch
        % 如果没有中文字体，使用默认字体
    end
    
    figure('Position', [100, 100, 1200, 800]);
    
    % 算法性能对比 - 删除EMG-TPC和HR-TPC，加入演员-评论家
    algorithms_display = {'固定功率', 'DQN', '演员-评论家', '改进分层RL'};
    energy_values_display = [all_results.fixed_power.energy, all_results.simple_dqn.energy, ...
                            all_results.actor_critic.energy, all_results.improved_hierarchical_rl.energy];
    pdr_values_display = [all_results.fixed_power.pdr, all_results.simple_dqn.pdr, ...
                         all_results.actor_critic.pdr, all_results.improved_hierarchical_rl.pdr];
    delay_values_display = [all_results.fixed_power.delay, all_results.simple_dqn.delay, ...
                           all_results.actor_critic.delay, all_results.improved_hierarchical_rl.delay];
    
    % 能耗对比
    subplot(2,2,1);
    colors_display = [0.7 0.7 0.7; 0.8 0.5 0.5; 0.5 0.8 0.5; 0.2 0.6 0.9];
    bar_handle = bar(energy_values_display, 'FaceColor', 'flat');
    bar_handle.CData = colors_display;
    set(gca, 'XTickLabel', algorithms_display, 'XTickLabelRotation', 45);
    title('(a) 能耗对比');
    ylabel('能耗 (mJ)');
    grid on;
    
    % 添加数值标签
    for i = 1:length(energy_values_display)
        text(i, energy_values_display(i) + 2, sprintf('%.0f', energy_values_display(i)), ...
             'HorizontalAlignment', 'center', 'FontWeight', 'bold');
    end
    
    % PDR对比
    subplot(2,2,2);
    bar_handle = bar(pdr_values_display, 'FaceColor', 'flat');
    bar_handle.CData = colors_display;
    set(gca, 'XTickLabel', algorithms_display, 'XTickLabelRotation', 45);
    title('(b) PDR对比');
    ylabel('包递交率');
    ylim([0, 1]);
    grid on;
    
    % 添加数值标签
    for i = 1:length(pdr_values_display)
        text(i, pdr_values_display(i) + 0.02, sprintf('%.2f', pdr_values_display(i)), ...
             'HorizontalAlignment', 'center', 'FontWeight', 'bold');
    end
    
    % 延迟对比
    subplot(2,2,3);
    bar_handle = bar(delay_values_display, 'FaceColor', 'flat');
    bar_handle.CData = colors_display;
    set(gca, 'XTickLabel', algorithms_display, 'XTickLabelRotation', 45);
    title('(c) 延迟对比');
    ylabel('延迟 (ms)');
    grid on;
    
    % 添加数值标签
    for i = 1:length(delay_values_display)
        text(i, delay_values_display(i) + 0.5, sprintf('%.0f', delay_values_display(i)), ...
             'HorizontalAlignment', 'center', 'FontWeight', 'bold');
    end
    
    % 综合性能得分
    subplot(2,2,4);
    energy_norm_display = (max(energy_values_display) - energy_values_display) / (max(energy_values_display) - min(energy_values_display));
    pdr_norm_display = pdr_values_display / max(pdr_values_display);
    delay_norm_display = (max(delay_values_display) - delay_values_display) / (max(delay_values_display) - min(delay_values_display));
    composite_score_display = 0.4 * energy_norm_display + 0.4 * pdr_norm_display + 0.2 * delay_norm_display;
    
    bar_handle = bar(composite_score_display, 'FaceColor', 'flat');
    bar_handle.CData = colors_display;
    set(gca, 'XTickLabel', algorithms_display, 'XTickLabelRotation', 45);
    title('(d) 综合性能得分');
    ylabel('综合得分');
    grid on;
    
    % 添加数值标签
    for i = 1:length(composite_score_display)
        text(i, composite_score_display(i) + 0.02, sprintf('%.2f', composite_score_display(i)), ...
             'HorizontalAlignment', 'center', 'FontWeight', 'bold');
    end
    
    sgtitle('改进算法验证结果 - 包含演员-评论家算法', 'FontSize', 16, 'FontWeight', 'bold');
    
    % 保存图片
    saveas(gcf, 'test_improved_verification_results.png');
    saveas(gcf, 'test_improved_verification_results.fig');
    
    % 打印结果
    fprintf('\n=== 算法性能对比 ===\n');
    fprintf('%-15s | %-10s | %-8s | %-10s | %-8s\n', '算法', '能耗(mJ)', 'PDR', '延迟(ms)', '综合得分');
    fprintf('----------------------------------------------------------------\n');
    
    for i = 1:length(algorithms_display)
        fprintf('%-15s | %10.0f | %8.2f | %10.0f | %8.2f\n', ...
               algorithms_display{i}, energy_values_display(i), pdr_values_display(i), ...
               delay_values_display(i), composite_score_display(i));
    end
    
    fprintf('\n✓ 可视化图表已保存到 test_improved_verification_results.png\n');
end
