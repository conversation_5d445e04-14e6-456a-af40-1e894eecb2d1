# ********************************************************************************
# *  Copyright: National ICT Australia,  2007 - 2010                             *
# *  Developed at the ATP lab, Networked Systems research theme                  *
# *  Author(s): <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>  *
# *  This file is distributed under the terms in the attached LICENSE file.      *
# *  If you do not find this file, copies can be found by writing to:            *
# *                                                                              *
# *      NICTA, Locked Bag 9013, Alexandria, NSW 1435, Australia                 *
# *      Attention:  License Inquiry.                                            *
# *                                                                              *
# *******************************************************************************/

[General]

# ==========================================================
# Always include the main Castalia.ini file
# ==========================================================

include ../Parameters/Castalia.ini

sim-time-limit = 100s


SN.field_x = 60					# meters
SN.field_y = 60					# meters
SN.numNodes = 16
SN.deployment = "4x4"

# Using ideal parameters for wireless channel and CC2420 radio
include ../Parameters/WirelessChannel/Ideal.ini
SN.node[*].Communication.Radio.RadioParametersFile = "../Parameters/Radio/CC2420.txt"
SN.node[*].Communication.Radio.mode = "IDEAL"

# Using CSMA (carrier sense) MAC and simple tree routing
include ../Parameters/MAC/CSMA.ini
# include ../Parameters/Routing/SimpleTree.ini

# Using simple aggregation application module with node 3 as a sink
SN.node[*].ApplicationName = "SimpleAggregation"
SN.node[3].Application.isSink = true


SN.wirelessChannel.printDebugInfo = true

