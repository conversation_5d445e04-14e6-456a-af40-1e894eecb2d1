% 快速测试修复后的分层RL算法
% 验证算法是否能够学习到节能策略

clear; clc; close all;

fprintf('=== 测试修复后的分层RL算法 ===\n');

% 1. 初始化环境和智能体
fprintf('1. 初始化系统...\n');
env = rl_environment();
agent = hierarchical_agent();

% 2. 快速训练 (20轮)
fprintf('2. 开始快速训练...\n');
num_episodes = 20;
episode_rewards = zeros(num_episodes, 1);
episode_energies = zeros(num_episodes, 1);
episode_pdrs = zeros(num_episodes, 1);

for episode = 1:num_episodes
    state = env.reset();
    episode_reward = 0;
    done = false;
    step_count = 0;
    max_steps = 100; % 限制步数
    
    while ~done && step_count < max_steps
        % 选择动作
        meta_action = agent.select_meta_action(state);
        action = agent.select_local_action(state, meta_action);
        
        % 执行动作
        [next_state, reward, done, info] = env.step(action);
        
        % 存储经验
        agent.store_meta_experience(state, meta_action, reward, next_state, done);
        agent.store_local_experience(state, meta_action, action, reward, next_state, done);
        
        % 训练
        if step_count > 10
            agent.train_meta_agent();
            agent.train_local_agent();
        end
        
        % 更新
        state = next_state;
        episode_reward = episode_reward + reward;
        step_count = step_count + 1;
        
        % 更新目标网络
        agent.update_target_networks();
    end
    
    % 衰减探索率
    agent.decay_epsilon();
    
    % 记录性能
    episode_rewards(episode) = episode_reward;
    episode_energies(episode) = env.energy_consumption;
    episode_pdrs(episode) = env.pdr;
    
    if mod(episode, 5) == 0
        fprintf('  Episode %d: 奖励=%.2f, 能耗=%.1f mJ, PDR=%.3f\n', ...
                episode, episode_reward, env.energy_consumption, env.pdr);
    end
end

% 3. 最终测试
fprintf('3. 最终性能测试...\n');
state = env.reset();
done = false;
step_count = 0;
power_choices = [];

% 设置为纯利用模式
agent.meta_epsilon = 0;
agent.local_epsilon = 0;

while ~done && step_count < 50
    meta_action = agent.select_meta_action(state);
    action = agent.select_local_action(state, meta_action);
    
    power_choices = [power_choices, action];
    [next_state, reward, done, info] = env.step(action);
    
    state = next_state;
    step_count = step_count + 1;
end

% 4. 分析结果
fprintf('4. 结果分析...\n');
final_energy = env.energy_consumption;
final_pdr = env.pdr;
final_delay = env.avg_delay;

fprintf('最终性能:\n');
fprintf('  能耗: %.1f mJ\n', final_energy);
fprintf('  PDR: %.3f\n', final_pdr);
fprintf('  延迟: %.1f ms\n', final_delay);

% 分析功率选择
power_levels = [-20, -15, -10, -5, 0, 4];
avg_power = mean(power_levels(power_choices));
low_power_ratio = sum(power_choices <= 3) / length(power_choices);

fprintf('功率选择分析:\n');
fprintf('  平均功率: %.1f dBm\n', avg_power);
fprintf('  低功率比例: %.1f%%\n', low_power_ratio * 100);

% 5. 与基准比较
fprintf('5. 与基准算法比较...\n');

% 固定功率基准 (0dBm)
env_baseline = rl_environment();
state = env_baseline.reset();
baseline_energy = 0;
baseline_pdr = 0;
baseline_delay = 0;

for i = 1:50
    [~, ~, done, info] = env_baseline.step(5); % 使用0dBm功率
    if done
        break;
    end
end

baseline_energy = env_baseline.energy_consumption;
baseline_pdr = env_baseline.pdr;
baseline_delay = env_baseline.avg_delay;

% 计算改进
energy_improvement = (baseline_energy - final_energy) / baseline_energy * 100;
pdr_improvement = (final_pdr - baseline_pdr) / baseline_pdr * 100;
delay_improvement = (baseline_delay - final_delay) / baseline_delay * 100;

fprintf('性能改进:\n');
fprintf('  能耗改进: %.1f%%\n', energy_improvement);
fprintf('  PDR改进: %.1f%%\n', pdr_improvement);
fprintf('  延迟改进: %.1f%%\n', delay_improvement);

% 6. 绘制结果
figure('Position', [100, 100, 1200, 400]);

subplot(1,3,1);
plot(1:num_episodes, episode_energies, 'b-o', 'LineWidth', 2);
title('训练过程中的能耗');
xlabel('训练轮次');
ylabel('能耗 (mJ)');
grid on;

subplot(1,3,2);
plot(1:num_episodes, episode_pdrs, 'r-s', 'LineWidth', 2);
title('训练过程中的PDR');
xlabel('训练轮次');
ylabel('PDR');
grid on;

subplot(1,3,3);
histogram(power_choices, 1:7, 'FaceColor', 'g', 'EdgeColor', 'black');
title('功率选择分布');
xlabel('功率等级');
ylabel('选择次数');
xticks(1:6);
xticklabels({'-20dBm', '-15dBm', '-10dBm', '-5dBm', '0dBm', '4dBm'});
grid on;

% 保存结果
saveas(gcf, 'quick_test_fixed_results.png');

fprintf('\n=== 测试完成 ===\n');
if energy_improvement > 0
    fprintf('✓ 算法修复成功！实现了节能目标\n');
else
    fprintf('✗ 算法仍需进一步优化\n');
end
