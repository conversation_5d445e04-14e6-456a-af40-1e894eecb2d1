//
// Generated file, do not edit! Created by nedtool 4.6 from src/node/communication/routing/bypassRouting/BypassRoutingPacket.msg.
//

#ifndef _BYPASSROUTINGPACKET_M_H_
#define _BYPASSROUTINGPACKET_M_H_

#include <omnetpp.h>

// nedtool version check
#define MSGC_VERSION 0x0406
#if (MSGC_VERSION!=OMNETPP_VERSION)
#    error Version mismatch! Probably this file was generated by an earlier version of nedtool: 'make clean' should help.
#endif



// cplusplus {{
#include "RoutingPacket_m.h"
// }}

/**
 * Class generated from <tt>src/node/communication/routing/bypassRouting/BypassRoutingPacket.msg:19</tt> by nedtool.
 * <pre>
 * packet BypassRoutingPacket extends RoutingPacket
 * {
 *     string source;
 *     string destination;
 * }
 * </pre>
 */
class BypassRoutingPacket : public ::RoutingPacket
{
  protected:
    opp_string source_var;
    opp_string destination_var;

  private:
    void copy(const BypassRoutingPacket& other);

  protected:
    // protected and unimplemented operator==(), to prevent accidental usage
    bool operator==(const BypassRoutingPacket&);

  public:
    BypassRoutingPacket(const char *name=NULL, int kind=0);
    BypassRoutingPacket(const BypassRoutingPacket& other);
    virtual ~BypassRoutingPacket();
    BypassRoutingPacket& operator=(const BypassRoutingPacket& other);
    virtual BypassRoutingPacket *dup() const {return new BypassRoutingPacket(*this);}
    virtual void parsimPack(cCommBuffer *b);
    virtual void parsimUnpack(cCommBuffer *b);

    // field getter/setter methods
    virtual const char * getSource() const;
    virtual void setSource(const char * source);
    virtual const char * getDestination() const;
    virtual void setDestination(const char * destination);
};

inline void doPacking(cCommBuffer *b, BypassRoutingPacket& obj) {obj.parsimPack(b);}
inline void doUnpacking(cCommBuffer *b, BypassRoutingPacket& obj) {obj.parsimUnpack(b);}


#endif // ifndef _BYPASSROUTINGPACKET_M_H_

