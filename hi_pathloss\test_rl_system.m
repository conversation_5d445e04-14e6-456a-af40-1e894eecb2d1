% 测试强化学习系统的基本功能
% 确保所有组件正常工作

% 主测试函数
function main_test()
    close all;
    clear;
    clc;
    
    fprintf('=== 测试强化学习WBAN功率控制系统 ===\n');
    
    try
        % 测试1: 环境初始化
        fprintf('测试1: 环境初始化...\n');
        env = rl_environment();
        fprintf('✓ 环境初始化成功\n');
        
        % 测试2: 智能体初始化
        fprintf('测试2: 智能体初始化...\n');
        agent = hierarchical_agent();
        fprintf('✓ 智能体初始化成功\n');
        
        % 测试3: 基本交互
        fprintf('测试3: 环境-智能体交互...\n');
        state = env.reset();
        fprintf('  初始状态维度: %d\n', length(state));
        
        meta_action = agent.select_meta_action(state);
        fprintf('  上层动作维度: %d\n', length(meta_action));
        
        action = agent.select_local_action(state, meta_action);
        fprintf('  下层动作: %d\n', action);
        
        [next_state, reward, done, info] = env.step(action);
        fprintf('  奖励: %.3f\n', reward);
        fprintf('  完成状态: %d\n', done);
        fprintf('✓ 基本交互测试成功\n');
        
        % 测试4: 短期训练
        fprintf('测试4: 短期训练 (5轮)...\n');
        test_short_training(env, agent);
        fprintf('✓ 短期训练测试成功\n');
        
        % 测试5: 性能评估
        fprintf('测试5: 性能评估...\n');
        performance = test_performance_evaluation(env, agent);
        fprintf('  测试性能 - 能耗: %.2f, PDR: %.3f, 延迟: %.1f\n', ...
               performance.energy, performance.pdr, performance.delay);
        fprintf('✓ 性能评估测试成功\n');
        
        % 测试6: 可视化
        fprintf('测试6: 可视化功能...\n');
        test_visualization(env);
        fprintf('✓ 可视化测试成功\n');
        
        fprintf('\n=== 所有测试通过！系统运行正常 ===\n');

        % 自动运行简单演示
        fprintf('\n自动运行简单演示...\n');
        run_simple_demo();
        
    catch ME
        fprintf('❌ 测试失败: %s\n', ME.message);
        fprintf('错误位置: %s (第%d行)\n', ME.stack(1).name, ME.stack(1).line);
        rethrow(ME);
    end
end

function test_short_training(env, agent)
    % 测试短期训练功能
    num_episodes = 5;
    
    for episode = 1:num_episodes
        state = env.reset();
        episode_reward = 0;
        
        for step = 1:20  % 每轮20步
            meta_action = agent.select_meta_action(state);
            action = agent.select_local_action(state, meta_action);
            
            [next_state, reward, done, info] = env.step(action);
            
            % 存储经验
            agent.store_meta_experience(state, meta_action, reward, next_state, done);
            agent.store_local_experience(state, meta_action, action, reward, next_state, done);
            
            state = next_state;
            episode_reward = episode_reward + reward;
            
            % 训练
            if step > 10
                agent.train_meta_agent();
                agent.train_local_agent();
            end
            
            if done
                break;
            end
        end
        
        agent.decay_epsilon();
        fprintf('  Episode %d: 奖励 = %.3f\n', episode, episode_reward);
    end
end

function performance = test_performance_evaluation(env, agent)
    % 测试性能评估功能
    
    % 关闭探索
    original_meta_epsilon = agent.meta_epsilon;
    original_local_epsilon = agent.local_epsilon;
    agent.meta_epsilon = 0;
    agent.local_epsilon = 0;
    
    state = env.reset();
    total_reward = 0;
    
    for step = 1:50
        meta_action = agent.select_meta_action(state);
        action = agent.select_local_action(state, meta_action);
        
        [next_state, reward, done, info] = env.step(action);
        total_reward = total_reward + reward;
        state = next_state;
        
        if done
            break;
        end
    end
    
    % 恢复探索率
    agent.meta_epsilon = original_meta_epsilon;
    agent.local_epsilon = original_local_epsilon;
    
    env_info = env.get_environment_info();
    performance = struct('reward', total_reward, 'energy', env_info.total_energy, ...
                        'pdr', env_info.average_pdr, 'delay', env_info.average_delay);
end

function test_visualization(env)
    % 测试可视化功能
    
    % 运行一小段仿真
    state = env.reset();
    for step = 1:30
        action = randi(6);  % 随机动作
        [next_state, reward, done, info] = env.step(action);
        state = next_state;
        
        if done
            break;
        end
    end
    
    % 测试环境绘图功能
    env.plot_performance();
    
    % 创建简单的测试图
    figure('Position', [400, 400, 600, 400]);
    subplot(1,2,1);
    plot(1:length(env.ecg_data), env.ecg_data);
    title('ECG信号测试');
    xlabel('样本');
    ylabel('心率 (BPM)');
    grid on;
    
    subplot(1,2,2);
    plot(1:length(env.rssi_data), env.rssi_data);
    title('RSSI信号测试');
    xlabel('样本');
    ylabel('RSSI (dBm)');
    grid on;
end

function run_simple_demo()
    % 运行简单演示
    fprintf('\n=== 简单演示 ===\n');
    
    % 初始化
    env = rl_environment();
    agent = hierarchical_agent();
    
    % 快速训练
    fprintf('快速训练 (10轮)...\n');
    episode_rewards = zeros(10, 1);
    
    for episode = 1:10
        state = env.reset();
        episode_reward = 0;
        
        for step = 1:30
            meta_action = agent.select_meta_action(state);
            action = agent.select_local_action(state, meta_action);
            
            [next_state, reward, done, info] = env.step(action);
            
            agent.store_meta_experience(state, meta_action, reward, next_state, done);
            agent.store_local_experience(state, meta_action, action, reward, next_state, done);
            
            state = next_state;
            episode_reward = episode_reward + reward;
            
            if mod(step, 5) == 0
                agent.train_meta_agent();
                agent.train_local_agent();
            end
            
            if done
                break;
            end
        end
        
        episode_rewards(episode) = episode_reward;
        agent.decay_epsilon();
        
        fprintf('  Episode %d: 奖励 = %.3f\n', episode, episode_reward);
    end
    
    % 测试最终性能
    fprintf('测试最终性能...\n');
    final_performance = test_performance_evaluation(env, agent);
    
    % 对比固定功率算法
    fprintf('对比固定功率算法...\n');
    fixed_performance = test_fixed_power_demo(env);
    
    % 显示结果
    fprintf('\n=== 演示结果 ===\n');
    fprintf('强化学习算法:\n');
    fprintf('  能耗: %.2f mJ\n', final_performance.energy);
    fprintf('  PDR: %.3f\n', final_performance.pdr);
    fprintf('  延迟: %.1f ms\n', final_performance.delay);
    
    fprintf('固定功率算法:\n');
    fprintf('  能耗: %.2f mJ\n', fixed_performance.energy);
    fprintf('  PDR: %.3f\n', fixed_performance.pdr);
    fprintf('  延迟: %.1f ms\n', fixed_performance.delay);
    
    % 计算改进
    energy_improvement = (fixed_performance.energy - final_performance.energy) / fixed_performance.energy * 100;
    pdr_improvement = (final_performance.pdr - fixed_performance.pdr) / fixed_performance.pdr * 100;
    delay_improvement = (fixed_performance.delay - final_performance.delay) / fixed_performance.delay * 100;
    
    fprintf('\n改进效果:\n');
    fprintf('  能耗降低: %.1f%%\n', energy_improvement);
    fprintf('  PDR提升: %.1f%%\n', pdr_improvement);
    fprintf('  延迟降低: %.1f%%\n', delay_improvement);
    
    % 绘制对比图
    figure('Position', [500, 500, 800, 600]);
    
    subplot(2,2,1);
    plot(1:10, episode_rewards, 'b-o', 'LineWidth', 2);
    title('训练奖励曲线');
    xlabel('Episode');
    ylabel('累积奖励');
    grid on;
    
    subplot(2,2,2);
    algorithms = {'固定功率', '强化学习'};
    energy_values = [fixed_performance.energy, final_performance.energy];
    bar(energy_values, 'FaceColor', [0.2, 0.6, 0.8]);
    set(gca, 'XTickLabel', algorithms);
    title('能耗对比');
    ylabel('能耗 (mJ)');
    grid on;
    
    subplot(2,2,3);
    pdr_values = [fixed_performance.pdr, final_performance.pdr];
    bar(pdr_values, 'FaceColor', [0.8, 0.4, 0.2]);
    set(gca, 'XTickLabel', algorithms);
    title('PDR对比');
    ylabel('PDR');
    ylim([0, 1]);
    grid on;
    
    subplot(2,2,4);
    delay_values = [fixed_performance.delay, final_performance.delay];
    bar(delay_values, 'FaceColor', [0.4, 0.8, 0.4]);
    set(gca, 'XTickLabel', algorithms);
    title('延迟对比');
    ylabel('延迟 (ms)');
    grid on;
    
    sgtitle('简单演示结果', 'FontSize', 16);
    
    fprintf('\n演示完成！\n');
end

function performance = test_fixed_power_demo(env)
    % 测试固定功率算法 (演示用)
    state = env.reset();
    total_reward = 0;
    
    for step = 1:50
        action = 4; % 固定中等功率
        [next_state, reward, done, info] = env.step(action);
        total_reward = total_reward + reward;
        state = next_state;
        
        if done
            break;
        end
    end
    
    env_info = env.get_environment_info();
    performance = struct('reward', total_reward, 'energy', env_info.total_energy, ...
                        'pdr', env_info.average_pdr, 'delay', env_info.average_delay);
end

% 运行测试
if ~exist('OCTAVE_VERSION', 'builtin')  % 检查是否在MATLAB中运行
    fprintf('开始系统测试...\n');
    main_test();
else
    fprintf('检测到Octave环境，请在MATLAB中运行以获得最佳体验\n');
end
