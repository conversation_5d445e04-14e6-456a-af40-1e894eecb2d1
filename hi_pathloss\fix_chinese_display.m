% 修复中文显示问题的可视化函数
function fix_chinese_display()
    close all;
    clear;
    clc;
    
    fprintf('=== 修复中文显示问题 ===\n');
    
    % 检查并加载实验结果
    if exist('algorithm_energy_comparison_results.mat', 'file')
        load('algorithm_energy_comparison_results.mat');
        fprintf('✓ 成功加载实验结果\n');
    else
        fprintf('❌ 未找到实验结果文件，请先运行 algorithm_energy_comparison\n');
        return;
    end
    
    % 提取数据
    energy_results = results.energy_results;
    energy_std = results.energy_std;
    scenario_names = results.scenario_names;
    algorithm_names = results.algorithm_names;
    
    % 设置中文字体支持
    setup_chinese_fonts();
    
    % 生成修复后的图表
    generate_fixed_comparison_plots(energy_results, energy_std, scenario_names, algorithm_names);
    
    fprintf('✓ 中文显示问题已修复，新图表已生成\n');
end

function setup_chinese_fonts()
    % 设置支持中文的字体
    fprintf('设置中文字体支持...\n');
    
    % 尝试不同的中文字体
    chinese_fonts = {'SimHei', 'Microsoft YaHei', 'SimSun', 'KaiTi', 'FangSong'};
    
    % 测试哪个字体可用
    available_font = 'Arial'; % 默认字体
    
    for i = 1:length(chinese_fonts)
        try
            % 创建测试图形
            test_fig = figure('Visible', 'off');
            test_text = text(0.5, 0.5, '测试中文', 'FontName', chinese_fonts{i});
            
            % 如果没有错误，说明字体可用
            available_font = chinese_fonts{i};
            close(test_fig);
            fprintf('✓ 找到可用中文字体: %s\n', available_font);
            break;
        catch
            % 字体不可用，继续尝试下一个
            if ishandle(test_fig)
                close(test_fig);
            end
            continue;
        end
    end
    
    % 设置全局字体
    set(0, 'DefaultAxesFontName', available_font);
    set(0, 'DefaultTextFontName', available_font);
    set(0, 'DefaultAxesFontSize', 12);
    set(0, 'DefaultTextFontSize', 12);
    
    % 如果没有找到中文字体，使用英文标签
    if strcmp(available_font, 'Arial')
        fprintf('⚠️  未找到中文字体，将使用英文标签\n');
    end
end

function generate_fixed_comparison_plots(energy_results, energy_std, scenario_names, algorithm_names)
    % 生成修复中文显示的对比图表
    
    % 检查是否有中文字体支持
    has_chinese_support = check_chinese_support();
    
    if has_chinese_support
        % 使用中文标签
        scenario_labels = {'静态监测场景', '动态转换场景', '周期性运动场景'};
        algorithm_labels = {'固定功率', 'DQN', '分层RL'};
        title_main = '算法能耗对比分析';
        xlabel_text = '运动场景';
        ylabel_text = '平均能耗 (mJ)';
        legend_title = '算法类型';
    else
        % 使用英文标签
        scenario_labels = {'Static Monitoring', 'Dynamic Transition', 'Periodic Motion'};
        algorithm_labels = {'Fixed Power', 'DQN', 'Hierarchical RL'};
        title_main = 'Algorithm Energy Consumption Comparison';
        xlabel_text = 'Motion Scenarios';
        ylabel_text = 'Average Energy Consumption (mJ)';
        legend_title = 'Algorithms';
    end
    
    % 图1: 修复的分组柱状图
    figure('Position', [100, 100, 1200, 800]);
    
    % 创建分组柱状图
    bar_data = energy_results';
    h = bar(bar_data, 'grouped');
    
    % 设置颜色
    colors = [0.2, 0.6, 0.8;    % 固定功率 - 蓝色
              0.8, 0.4, 0.2;    % DQN - 橙色
              0.2, 0.8, 0.4];   % 分层RL - 绿色
    
    for i = 1:length(h)
        h(i).FaceColor = colors(i, :);
        h(i).EdgeColor = 'black';
        h(i).LineWidth = 1.2;
    end
    
    % 添加误差棒和数值标签
    hold on;
    for i = 1:size(bar_data, 1)
        for j = 1:size(bar_data, 2)
            x_pos = i + (j - 2) * 0.27;
            
            % 误差棒
            errorbar(x_pos, bar_data(i, j), energy_std(j, i), 'k', 'LineWidth', 1.5, 'CapSize', 8);
            
            % 数值标签
            text(x_pos, bar_data(i, j) + energy_std(j, i) + 0.1, ...
                sprintf('%.2f', bar_data(i, j)), ...
                'HorizontalAlignment', 'center', 'FontSize', 11, 'FontWeight', 'bold');
        end
    end
    
    % 设置图表属性
    xlabel(xlabel_text, 'FontSize', 14, 'FontWeight', 'bold');
    ylabel(ylabel_text, 'FontSize', 14, 'FontWeight', 'bold');
    title(title_main, 'FontSize', 16, 'FontWeight', 'bold');
    set(gca, 'XTickLabel', scenario_labels);
    legend(algorithm_labels, 'Location', 'best', 'FontSize', 12);
    grid on;
    grid minor;
    
    % 保存修复后的图表
    saveas(gcf, 'fixed_energy_comparison_grouped.png');
    saveas(gcf, 'fixed_energy_comparison_grouped.fig');
    
    % 图2: 修复的热力图
    figure('Position', [200, 200, 900, 600]);
    
    % 创建热力图
    imagesc(energy_results);
    colorbar;
    colormap(flipud(hot));
    
    % 设置标签
    set(gca, 'XTick', 1:length(scenario_labels));
    set(gca, 'XTickLabel', scenario_labels);
    set(gca, 'YTick', 1:length(algorithm_labels));
    set(gca, 'YTickLabel', algorithm_labels);
    
    % 添加数值标签
    for i = 1:size(energy_results, 1)
        for j = 1:size(energy_results, 2)
            text(j, i, sprintf('%.2f', energy_results(i, j)), ...
                'HorizontalAlignment', 'center', 'Color', 'white', ...
                'FontSize', 14, 'FontWeight', 'bold');
        end
    end
    
    if has_chinese_support
        title('算法能耗热力图 (mJ)', 'FontSize', 16, 'FontWeight', 'bold');
        xlabel('运动场景', 'FontSize', 14);
        ylabel('算法类型', 'FontSize', 14);
    else
        title('Algorithm Energy Consumption Heatmap (mJ)', 'FontSize', 16, 'FontWeight', 'bold');
        xlabel('Motion Scenarios', 'FontSize', 14);
        ylabel('Algorithm Types', 'FontSize', 14);
    end
    
    % 保存热力图
    saveas(gcf, 'fixed_energy_comparison_heatmap.png');
    saveas(gcf, 'fixed_energy_comparison_heatmap.fig');
    
    % 图3: 详细对比分析图
    figure('Position', [300, 300, 1400, 900]);
    
    % 子图1: 能耗对比
    subplot(2, 3, 1);
    bar(bar_data, 'grouped');
    xlabel(xlabel_text);
    ylabel(ylabel_text);
    if has_chinese_support
        title('(a) 能耗对比');
    else
        title('(a) Energy Comparison');
    end
    set(gca, 'XTickLabel', scenario_labels);
    legend(algorithm_labels, 'Location', 'best');
    grid on;
    
    % 子图2: 相对性能
    subplot(2, 3, 2);
    relative_perf = (bar_data - bar_data(:, 1)) ./ bar_data(:, 1) * 100;
    bar(relative_perf, 'grouped');
    xlabel(xlabel_text);
    if has_chinese_support
        ylabel('相对变化 (%)');
        title('(b) 相对性能变化');
    else
        ylabel('Relative Change (%)');
        title('(b) Relative Performance');
    end
    set(gca, 'XTickLabel', scenario_labels);
    legend(algorithm_labels, 'Location', 'best');
    grid on;
    yline(0, 'r--', 'LineWidth', 2);
    
    % 子图3: 算法稳定性
    subplot(2, 3, 3);
    avg_std = mean(energy_std, 2);
    bar(avg_std, 'FaceColor', [0.6, 0.4, 0.8]);
    xlabel(legend_title);
    if has_chinese_support
        ylabel('平均标准差 (mJ)');
        title('(c) 算法稳定性');
    else
        ylabel('Average Std Dev (mJ)');
        title('(c) Algorithm Stability');
    end
    set(gca, 'XTickLabel', algorithm_labels);
    grid on;
    
    % 子图4: 场景复杂度
    subplot(2, 3, 4);
    scenario_range = max(energy_results) - min(energy_results);
    bar(scenario_range, 'FaceColor', [0.8, 0.6, 0.2]);
    xlabel(xlabel_text);
    if has_chinese_support
        ylabel('算法间差异 (mJ)');
        title('(d) 场景复杂度');
    else
        ylabel('Algorithm Difference (mJ)');
        title('(d) Scenario Complexity');
    end
    set(gca, 'XTickLabel', scenario_labels);
    grid on;
    
    % 子图5: 算法排名
    subplot(2, 3, 5);
    avg_energy = mean(energy_results, 2);
    [sorted_energy, sort_idx] = sort(avg_energy);
    bar(sorted_energy, 'FaceColor', [0.4, 0.7, 0.6]);
    xlabel(legend_title);
    if has_chinese_support
        ylabel('平均能耗 (mJ)');
        title('(e) 算法排名');
    else
        ylabel('Average Energy (mJ)');
        title('(e) Algorithm Ranking');
    end
    set(gca, 'XTickLabel', algorithm_labels(sort_idx));
    grid on;
    
    % 子图6: 改进潜力
    subplot(2, 3, 6);
    improvement_potential = (max(energy_results) - min(energy_results)) ./ max(energy_results) * 100;
    bar(improvement_potential, 'FaceColor', [0.9, 0.5, 0.3]);
    xlabel(xlabel_text);
    if has_chinese_support
        ylabel('改进潜力 (%)');
        title('(f) 改进潜力分析');
    else
        ylabel('Improvement Potential (%)');
        title('(f) Improvement Potential');
    end
    set(gca, 'XTickLabel', scenario_labels);
    grid on;
    
    % 总标题
    sgtitle(title_main, 'FontSize', 18, 'FontWeight', 'bold');
    
    % 保存详细分析图
    saveas(gcf, 'fixed_detailed_energy_analysis.png');
    saveas(gcf, 'fixed_detailed_energy_analysis.fig');
    
    fprintf('✓ 修复后的图表已生成:\n');
    fprintf('  - fixed_energy_comparison_grouped.png\n');
    fprintf('  - fixed_energy_comparison_heatmap.png\n');
    fprintf('  - fixed_detailed_energy_analysis.png\n');
end

function has_support = check_chinese_support()
    % 检查是否支持中文显示
    try
        test_fig = figure('Visible', 'off');
        test_text = text(0.5, 0.5, '测试', 'FontName', 'SimHei');
        has_support = true;
        close(test_fig);
    catch
        has_support = false;
        if exist('test_fig', 'var') && ishandle(test_fig)
            close(test_fig);
        end
    end
end
