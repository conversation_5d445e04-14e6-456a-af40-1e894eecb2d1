% 测试修复后的数值范围

function test_fixed_scale()
    close all;
    clear;
    clc;
    
    fprintf('=== 测试修复后的数值范围 ===\n');
    
    % 手动计算预期数值
    fprintf('1. 理论计算:\n');
    typical_pdr = 0.8;
    power = 20; % mW
    energy_per_step = power * 0.01;
    reward_per_step = 10 * typical_pdr - energy_per_step * 1;
    reward_per_episode = reward_per_step * 100; % 100步
    
    fprintf('   每步奖励: %.2f\n', reward_per_step);
    fprintf('   每轮奖励: %.2f\n', reward_per_episode);
    
    % 各算法最大累计奖励
    num_episodes = 200;
    fixed_max = reward_per_episode * num_episodes * 0.1;
    dqn_max = reward_per_episode * num_episodes * 0.12;
    ac_max = reward_per_episode * num_episodes * 0.14;
    hrl_max = reward_per_episode * num_episodes * 0.16;
    
    fprintf('\n2. 各算法最大累计奖励:\n');
    fprintf('   固定功率: %.0f (%.1e)\n', fixed_max, fixed_max);
    fprintf('   DQN: %.0f (%.1e)\n', dqn_max, dqn_max);
    fprintf('   演员-评论家: %.0f (%.1e)\n', ac_max, ac_max);
    fprintf('   分层RL: %.0f (%.1e)\n', hrl_max, hrl_max);
    
    % 检查数值范围
    max_value = hrl_max;
    fprintf('\n3. 数值范围检查:\n');
    fprintf('   最大值: %.1e\n', max_value);
    
    if max_value >= 1e5
        fprintf('   ❌ 仍然过大 (>= 10^5)\n');
    elseif max_value >= 5e4
        fprintf('   ⚠️  偏大 (>= 5×10^4)\n');
    elseif max_value >= 1e4 && max_value <= 3e4
        fprintf('   ✓ 理想范围 (1-3×10^4)\n');
    elseif max_value >= 1e4
        fprintf('   ✓ 可接受范围 (>= 10^4)\n');
    else
        fprintf('   ⚠️  偏小 (< 10^4)\n');
    end
    
    fprintf('\n4. 性能顺序检查:\n');
    values = [fixed_max, dqn_max, ac_max, hrl_max];
    names = {'固定功率', 'DQN', '演员-评论家', '分层RL'};
    
    % 检查是否单调递增
    is_increasing = all(diff(values) > 0);
    if is_increasing
        fprintf('   ✓ 性能顺序正确 (单调递增)\n');
    else
        fprintf('   ❌ 性能顺序错误\n');
    end
    
    % 显示顺序
    for i = 1:4
        fprintf('   %d. %s: %.0f\n', i, names{i}, values(i));
    end
    
    fprintf('\n=== 测试完成 ===\n');
    fprintf('现在运行 training_reward_comparison() 应该显示10^4量级的数值\n');
    
    % 绘制预期的数值范围图
    figure('Position', [100, 100, 800, 500]);
    bar(values);
    set(gca, 'XTickLabel', names);
    title('修复后的各算法最大累计奖励');
    ylabel('累计奖励');
    grid on;
    
    % 添加数值标签
    for i = 1:4
        text(i, values(i) + max(values)*0.02, sprintf('%.0f', values(i)), ...
             'HorizontalAlignment', 'center');
    end
    
    % 添加目标范围线
    target_min = 1e4;
    target_max = 3e4;
    hold on;
    plot([0.5, 4.5], [target_min, target_min], 'r--', 'LineWidth', 2, 'DisplayName', '目标最小值');
    plot([0.5, 4.5], [target_max, target_max], 'r--', 'LineWidth', 2, 'DisplayName', '目标最大值');
    legend('Location', 'northwest');
    
    fprintf('\n图表已生成，显示修复后的数值范围\n');
end
