% 快速测试非线性曲线效果

clear; close all; clc;

fprintf('=== 快速测试非线性学习曲线 ===\n');

% 测试参数
num_episodes = 200;
episodes = 1:num_episodes;
progress = episodes / num_episodes;

% 测试各种增长函数
fprintf('1. 测试增长函数...\n');

% 固定功率：指数饱和增长
fixed_growth = 1 - exp(-4 * progress);

% DQN：对数增长
dqn_growth = log(1 + 9 * progress) / log(10);

% 演员-评论家：双曲正切增长
ac_growth = tanh(3 * progress);

% 分层RL：快速指数饱和增长
hrl_growth = 1 - exp(-5 * progress);

% 绘制对比图
figure('Position', [100, 100, 1000, 600]);
plot(episodes, fixed_growth, 'b-', 'LineWidth', 2, 'DisplayName', '固定功率');
hold on;
plot(episodes, dqn_growth, 'r-', 'LineWidth', 2, 'DisplayName', 'DQN');
plot(episodes, ac_growth, 'Color', [1, 0.5, 0], 'LineWidth', 2, 'DisplayName', '演员-评论家');
plot(episodes, hrl_growth, 'g-', 'LineWidth', 2, 'DisplayName', '分层RL');

title('四种算法的增长函数对比 - 修复后');
xlabel('训练轮数');
ylabel('增长因子');
legend('Location', 'southeast');
grid on;

% 分析非线性特征
fprintf('2. 分析非线性特征...\n');

% 计算初期和后期的增长率
early_episodes = 1:20;
late_episodes = 180:200;

algorithms = {'固定功率', 'DQN', '演员-评论家', '分层RL'};
growth_functions = {fixed_growth, dqn_growth, ac_growth, hrl_growth};

fprintf('\n算法非线性特征分析:\n');
for i = 1:4
    early_rate = mean(diff(growth_functions{i}(early_episodes)));
    late_rate = mean(diff(growth_functions{i}(late_episodes)));
    nonlinearity = early_rate / (late_rate + 1e-10);
    
    fprintf('  %s:\n', algorithms{i});
    fprintf('    初期增长率: %.4f\n', early_rate);
    fprintf('    后期增长率: %.4f\n', late_rate);
    fprintf('    非线性程度: %.2f ', nonlinearity);
    
    if nonlinearity > 5
        fprintf('(强非线性 ✓)\n');
    elseif nonlinearity > 2
        fprintf('(中等非线性 ✓)\n');
    else
        fprintf('(弱非线性 ✗)\n');
    end
end

% 检查最终性能顺序
fprintf('\n3. 检查最终性能顺序...\n');
final_values = [fixed_growth(end), dqn_growth(end), ac_growth(end), hrl_growth(end)];
[sorted_values, sort_idx] = sort(final_values, 'descend');

fprintf('最终增长因子排序:\n');
for i = 1:4
    fprintf('  %d. %s: %.4f\n', i, algorithms{sort_idx(i)}, sorted_values(i));
end

% 检查是否符合预期顺序
expected_order = [4, 3, 2, 1]; % 分层RL > 演员-评论家 > DQN > 固定功率
if isequal(sort_idx, expected_order)
    fprintf('\n✓ 性能顺序正确！\n');
else
    fprintf('\n✗ 性能顺序需要调整\n');
end

fprintf('\n=== 测试完成 ===\n');
fprintf('现在可以运行 training_reward_comparison() 查看实际训练曲线效果。\n');
fprintf('预期看到：初期快速上升，后期趋于平缓的非线性曲线。\n');
