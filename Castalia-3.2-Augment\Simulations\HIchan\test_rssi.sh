#!/bin/bash
# RSSI测试脚本
echo "=== 测试修复后的RSSI配置 ==="

cd ../Castalia-3.2-Augment/Simulations/HIchan

echo "1. 清理旧结果..."
rm -f results/*.vec results/*.sca

echo "2. 运行修复后的仿真..."
../../bin/Castalia -c TMAC -f omnetpp_fixed.ini

echo "3. 检查RSSI结果..."
if [ -f "results/TMAC-0.vec" ]; then
    echo "✓ 生成了.vec文件"
    echo "RSSI数据统计:"
    grep "^0" results/TMAC-0.vec | head -10
else
    echo "❌ 未生成.vec文件"
fi

echo "=== 测试完成 ==="
