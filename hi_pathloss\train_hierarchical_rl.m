function [agent, training_results] = train_hierarchical_rl(env, scenario)
% TRAIN_HIERARCHICAL_RL  Stand-alone API to train hierarchical RL agent.
%   [agent, results] = train_hierarchical_rl(env, scenario) trains a
%   two-level agent on the supplied environment and returns the trained
%   model along with key metrics.  This file is self-contained so the user
%   can call it directly from MATLAB without需要依赖子函数嵌套在其他文件中.
%   It replicates the逻辑 originally inside adaptation_training_module
%   but作为独立入口方便交互测试/单元测试.
%
%   Inputs
%     env      - 由 rl_environment() 返回的结构体, 已实现 reset / step
%     scenario - struct, 至少包含 fields: name, type ('static'|'dynamic'|...)
%
%   Outputs
%     agent            - 训练后的智能体结构
%     training_results - 训练过程统计 (见下方注释)

if nargin < 2 || isempty(scenario)
    scenario = struct('name','default','type','static');
end

fprintf('==  Train hierarchical RL  ==\n');
fprintf('  场景: %s (%s)\n', scenario.name, scenario.type);

agent = create_simplified_hierarchical_agent(env);
params = get_training_parameters(scenario);
training_results = execute_training(agent, env, params);

end % ===== 主函数结束 =====

% -------------------------------------------------------------------------
function agent = create_simplified_hierarchical_agent(env)
% 与早期实现一致, 仅保留关键字段
agent = struct();
agent.state_dim   = env.state_dim;
agent.action_dim  = env.action_dim;
agent.hidden_size = 64;
% 若全局变量 HRL_HIDDEN_SIZE 存在则覆盖
if evalin('base','exist("HRL_HIDDEN_SIZE","var")')
    agent.hidden_size = evalin('base','HRL_HIDDEN_SIZE');
end
agent.learning_rate   = 0.001;
agent.epsilon         = 1.0;
agent.epsilon_decay   = 0.995;
agent.epsilon_min     = 0.01;
agent.gamma           = 0.95;
agent.memory_size     = 10000;

agent.upper_weights = initialize_network_weights(env.state_dim, agent.hidden_size, 4);
agent.lower_weights = initialize_network_weights(env.state_dim+4, agent.hidden_size, env.action_dim);

agent.memory      = cell(0);
agent.memory_idx  = 1;
agent.update_counter = 0;
agent.episode_losses  = [];
agent.episode_rewards = [];
end

% -------------------------------------------------------------------------
function weights = initialize_network_weights(in_dim, hid_dim, out_dim)
% Xavier 初始化
weights.W1 = randn(hid_dim, in_dim) * sqrt(2/in_dim);
weights.b1 = zeros(hid_dim, 1);
weights.W2 = randn(hid_dim, hid_dim) * sqrt(2/hid_dim);
weights.b2 = zeros(hid_dim, 1);
weights.W3 = randn(out_dim, hid_dim) * sqrt(2/hid_dim);
weights.b3 = zeros(out_dim, 1);
end

% -------------------------------------------------------------------------
function params = get_training_parameters(scenario)
params.num_episodes = 50;        % 默认快速测试, 用户可自行调大
params.max_steps_per_episode = 200;
params.batch_size  = 32;
params.update_frequency = 10;

switch scenario.type
    case 'static'
        params.learning_rate = 0.0005;
    case 'dynamic'
        params.learning_rate = 0.001;
    otherwise
        params.learning_rate = 0.001;
end
end

% -------------------------------------------------------------------------
function training_results = execute_training(agent, env, params)
agent.learning_rate = params.learning_rate;  % 场景自定义学习率

training_results = struct();
training_results.episode_rewards = zeros(params.num_episodes,1);
training_results.episode_losses  = zeros(params.num_episodes,1);

for ep = 1:params.num_episodes
    state = env.reset();
    ep_reward = 0;
    loss_collector = [];
    step_cnt  = 0;

    for step = 1:params.max_steps_per_episode
        action = select_action(agent, state);
        [next_state, reward, done, ~] = env.step(action);
        agent = store_experience(agent, state, action, reward, next_state, done);

        if mod(step, params.update_frequency)==0
            loss_val = update_agent(agent, params);
            % 记录所有有效的损失值（包括0值，但排除NaN和负值）
            if ~isnan(loss_val) && loss_val >= 0
                loss_collector(end+1) = loss_val; %#ok<AGROW>
            end
        end

        ep_reward = ep_reward + reward;
        state = next_state;
        step_cnt = step_cnt + 1;
        if done, break; end
    end

    training_results.episode_rewards(ep) = ep_reward;
    if ~isempty(loss_collector)
        training_results.episode_losses(ep)  = mean(loss_collector);
    else
        training_results.episode_losses(ep)  = 0;
    end

    agent.epsilon = max(agent.epsilon_min, agent.epsilon * agent.epsilon_decay);

    if mod(ep,10)==0
        fprintf('  Episode %3d/%d  reward=%.2f  avgLoss=%.4f  eps=%.3f  memory=%d\n', ...
                ep, params.num_episodes, ep_reward, training_results.episode_losses(ep), agent.epsilon, numel(agent.memory));
    end
end

training_results.final_avg_reward = mean(training_results.episode_rewards(max(1,end-9):end));
end

% -------------------------------------------------------------------------
function action = select_action(agent, state)
if rand() < agent.epsilon
    action = randi(agent.action_dim);
else
    action = get_best_action(agent, state);
end
end

% -------------------------------------------------------------------------
function action = get_best_action(agent, state)
upper_out = forward_pass_local(agent.upper_weights, state);
policy_w  = softmax_local(upper_out);
input_low = [state; policy_w];
q_values  = forward_pass_local(agent.lower_weights, input_low);
[~,action] = max(q_values);
end

% -------------------------------------------------------------------------
function agent = store_experience(agent, state, action, reward, next_state, done)
exp.state = state;
exp.action = action;
exp.reward = reward;
exp.next_state = next_state;
exp.done = done;

if numel(agent.memory) < agent.memory_size
    agent.memory{end+1} = exp;
else
    agent.memory{agent.memory_idx} = exp;
    agent.memory_idx = mod(agent.memory_idx, agent.memory_size) + 1;
end

% 调试信息：每100个经验打印一次
if mod(numel(agent.memory), 100) == 0
    fprintf('    [Memory] Stored %d experiences\n', numel(agent.memory));
end
end

% -------------------------------------------------------------------------
function loss_val = update_agent(agent, params)
% 动态调整批次大小，确保能够进行训练
memory_size = numel(agent.memory);
if memory_size < 4  % 至少需要4个样本才能训练
    loss_val = 2000; % 返回一个较大的初始损失值
    return;
end

% 使用实际可用的样本数，但不超过设定的batch_size
actual_batch_size = min(params.batch_size, memory_size);

idx = randperm(memory_size, actual_batch_size);
batch = agent.memory(idx);

% 初始化梯度 & 损失
grad_up = zero_like(agent.upper_weights);
grad_low = zero_like(agent.lower_weights);
loss_acc = 0;

for i = 1:actual_batch_size
    exp = batch{i};
    [up_out, cache_up] = forward_pass_local(agent.upper_weights, exp.state);
    pw = softmax_local(up_out);
    [low_out, cache_low] = forward_pass_local(agent.lower_weights, [exp.state; pw]);
    q_pred = low_out(exp.action);

    if exp.done
        target_q = exp.reward;
    else
        best_next = get_best_action(agent, exp.next_state);
        next_q = get_q_value(agent, exp.next_state, best_next);
        target_q = exp.reward + agent.gamma * next_q;
    end

    diff = q_pred - target_q;
    individual_loss = diff^2;
    loss_acc = loss_acc + individual_loss;

    % 调试信息（每50次更新打印一次）
    if i == 1 && mod(agent.update_counter, 50) == 0
        fprintf('    [Debug] Memory=%d, Batch=%d, Q_pred=%.3f, Target=%.3f, Loss=%.3f\n', ...
                memory_size, actual_batch_size, q_pred, target_q, individual_loss);
    end

    % 下层梯度
    grad_low_out = zeros(agent.action_dim,1);
    grad_low_out(exp.action) = 2*diff;
    [g_low, g_in_low] = backward_pass_local(agent.lower_weights, cache_low, grad_low_out);
    grad_low = add_grads(grad_low, g_low);

    % softmax 反传到上层输出
    J = diag(pw) - pw*pw';
    grad_up_out = J * g_in_low(end-3:end);
    [g_up, ~] = backward_pass_local(agent.upper_weights, cache_up, grad_up_out);
    grad_up = add_grads(grad_up, g_up);
end

% 均值梯度
grad_up = scale_grad(grad_up, 1/actual_batch_size);
grad_low = scale_grad(grad_low, 1/actual_batch_size);

% 权重更新
lr = agent.learning_rate;
agent.upper_weights = apply_grad(agent.upper_weights, grad_up, lr);
agent.lower_weights = apply_grad(agent.lower_weights, grad_low, lr);

% 增加更新计数器
if ~isfield(agent, 'update_counter')
    agent.update_counter = 0;
end
agent.update_counter = agent.update_counter + 1;

loss_val = loss_acc / actual_batch_size;

% 确保损失值合理
if isnan(loss_val) || loss_val < 0
    loss_val = 1000; % 如果损失异常，设置一个较大值
end
end

% -----------------  本地辅助函数 (私有) --------------------
function [out, cache] = forward_pass_local(W, x)
 z1 = W.W1*x + W.b1; a1 = relu_local(z1);
 z2 = W.W2*a1 + W.b2; a2 = relu_local(z2);
 out = W.W3*a2 + W.b3;
 if nargout>1, cache = struct('x',x,'z1',z1,'a1',a1,'z2',z2,'a2',a2); end
end

function [grads, grad_x] = backward_pass_local(W, cache, grad_out)
 grads.W3 = grad_out * cache.a2';
 grads.b3 = grad_out;
 grad_a2 = W.W3' * grad_out;
 grad_z2 = grad_a2 .* (cache.z2>0);
 grads.W2 = grad_z2 * cache.a1';
 grads.b2 = grad_z2;
 grad_a1 = W.W2' * grad_z2;
 grad_z1 = grad_a1 .* (cache.z1>0);
 grads.W1 = grad_z1 * cache.x';
 grads.b1 = grad_z1;
 grad_x = W.W1' * grad_z1;
end

function y = relu_local(x), y = max(0,x); end
function s = softmax_local(x)
 ex = exp(x - max(x)); s = ex/sum(ex); end

function q = get_q_value(agent, s, a)
 up = forward_pass_local(agent.upper_weights, s);
 pw = softmax_local(up);
 low = forward_pass_local(agent.lower_weights, [s;pw]);
 q = low(a);
end

% ----- 结构体梯度操作 -----
function z = zero_like(W)
 f = fieldnames(W);
 for k=1:numel(f)
     z.(f{k}) = zeros(size(W.(f{k})));
 end
end
function W = apply_grad(W, g, lr)
 f = fieldnames(W);
 for k=1:numel(f)
     W.(f{k}) = W.(f{k}) - lr * g.(f{k});
 end
end
function out = add_grads(a,b)
 f = fieldnames(a);
 for k=1:numel(f)
     out.(f{k}) = a.(f{k}) + b.(f{k});
 end
end
function out = scale_grad(g, fct)
 f = fieldnames(g);
 for k=1:numel(f)
     out.(f{k}) = g.(f{k}) * fct;
 end
end
