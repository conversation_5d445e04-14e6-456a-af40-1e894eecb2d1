% 测试性能分析功能 - 修复Alpha参数问题后的验证
close all;
clear;
clc;

fprintf('=== 测试性能分析功能 ===\n');

try
    % 检查是否有训练结果
    if exist('rl_training_results.mat', 'file')
        fprintf('发现现有训练结果，直接进行分析...\n');
        rl_performance_analysis;
    else
        fprintf('未找到训练结果，先运行快速训练...\n');
        
        % 运行快速训练生成结果
        env = rl_environment();
        agent = hierarchical_agent();
        
        % 快速训练
        num_episodes = 20;
        episode_rewards = zeros(num_episodes, 1);
        episode_energy = zeros(num_episodes, 1);
        episode_pdr = zeros(num_episodes, 1);
        episode_delay = zeros(num_episodes, 1);
        
        fprintf('开始快速训练 (%d轮)...\n', num_episodes);
        
        for episode = 1:num_episodes
            state = env.reset();
            episode_reward = 0;
            
            for step = 1:30
                meta_action = agent.select_meta_action(state);
                action = agent.select_local_action(state, meta_action);
                [next_state, reward, done, info] = env.step(action);
                
                agent.store_meta_experience(state, meta_action, reward, next_state, done);
                agent.store_local_experience(state, meta_action, action, reward, next_state, done);
                
                state = next_state;
                episode_reward = episode_reward + reward;
                
                if mod(step, 10) == 0
                    agent.train_meta_agent();
                    agent.train_local_agent();
                end
                
                if done
                    break;
                end
            end
            
            env_info = env.get_environment_info();
            episode_rewards(episode) = episode_reward;
            episode_energy(episode) = env_info.total_energy;
            episode_pdr(episode) = env_info.average_pdr;
            episode_delay(episode) = env_info.average_delay;
            
            agent.decay_epsilon();
            
            if mod(episode, 5) == 0
                fprintf('  Episode %d: 奖励=%.2f, 能耗=%.1f, PDR=%.3f\n', ...
                       episode, episode_reward, env_info.total_energy, env_info.average_pdr);
            end
        end
        
        % 测试基准算法
        fprintf('测试基准算法...\n');
        
        % 固定功率
        state = env.reset();
        for step = 1:50
            [~, ~, ~, ~] = env.step(4);
        end
        fixed_performance = env.get_environment_info();
        
        % EMG-TPC
        state = env.reset();
        for step = 1:50
            emg_signal = env.emg_data(min(step, length(env.emg_data)));
            if emg_signal > 40
                action = 6;
            elseif emg_signal > 20
                action = 4;
            else
                action = 2;
            end
            [~, ~, ~, ~] = env.step(action);
        end
        emg_performance = env.get_environment_info();
        
        % HR-TPC
        state = env.reset();
        for step = 1:50
            hr_signal = env.ecg_data(min(step, length(env.ecg_data)));
            if hr_signal > 100
                action = 6;
            elseif hr_signal > 80
                action = 4;
            else
                action = 3;
            end
            [~, ~, ~, ~] = env.step(action);
        end
        hr_performance = env.get_environment_info();
        
        % 测试训练后的RL
        agent.meta_epsilon = 0;
        agent.local_epsilon = 0;
        state = env.reset();
        for step = 1:50
            meta_action = agent.select_meta_action(state);
            action = agent.select_local_action(state, meta_action);
            [next_state, ~, ~, ~] = env.step(action);
            state = next_state;
        end
        rl_performance = env.get_environment_info();
        
        % 创建结果结构
        results = struct();
        results.episode_rewards = episode_rewards;
        results.episode_energy = episode_energy;
        results.episode_pdr = episode_pdr;
        results.episode_delay = episode_delay;
        
        results.baseline_performance = struct();
        results.baseline_performance.fixed_power = struct('energy', fixed_performance.total_energy, ...
                                                         'pdr', fixed_performance.average_pdr, ...
                                                         'delay', fixed_performance.average_delay);
        results.baseline_performance.emg_tpc = struct('energy', emg_performance.total_energy, ...
                                                     'pdr', emg_performance.average_pdr, ...
                                                     'delay', emg_performance.average_delay);
        results.baseline_performance.hr_tpc = struct('energy', hr_performance.total_energy, ...
                                                    'pdr', hr_performance.average_pdr, ...
                                                    'delay', hr_performance.average_delay);
        
        results.final_performance = struct('energy', rl_performance.total_energy, ...
                                          'pdr', rl_performance.average_pdr, ...
                                          'delay', rl_performance.average_delay);
        
        % 保存结果
        save('rl_training_results.mat', 'results');
        fprintf('训练结果已保存\n');
        
        % 现在运行性能分析
        fprintf('运行性能分析...\n');
        rl_performance_analysis;
    end
    
    fprintf('✅ 性能分析测试成功完成！\n');
    
catch ME
    fprintf('❌ 性能分析测试失败: %s\n', ME.message);
    fprintf('错误位置: %s (第%d行)\n', ME.stack(1).name, ME.stack(1).line);
    
    % 显示详细错误信息
    if length(ME.stack) > 1
        fprintf('调用堆栈:\n');
        for i = 1:min(3, length(ME.stack))
            fprintf('  %d. %s (第%d行)\n', i, ME.stack(i).name, ME.stack(i).line);
        end
    end
end

fprintf('\n=== 测试完成 ===\n');
