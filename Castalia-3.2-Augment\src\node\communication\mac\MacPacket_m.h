//
// Generated file, do not edit! Created by nedtool 4.6 from src/node/communication/mac/MacPacket.msg.
//

#ifndef _MACPACKET_M_H_
#define _MACPACKET_M_H_

#include <omnetpp.h>

// nedtool version check
#define MSGC_VERSION 0x0406
#if (MSGC_VERSION!=OMNETPP_VERSION)
#    error Version mismatch! Probably this file was generated by an earlier version of nedtool: 'make clean' should help.
#endif



/**
 * Enum generated from <tt>src/node/communication/mac/MacPacket.msg:13</tt> by nedtool.
 * <pre>
 * enum MacControlMessage_type
 * {
 * 
 *     MAC_BUFFER_FULL = 1;
 * }
 * 
 * // We need to pass information between MAC and the Radio which is external
 * // to the packet i.e. not carried by a real packet (e.g., what was the 
 * // RSSI for the packet received) but this information is related to the
 * // specific packet. Since information is passed between modules with
 * // messages/packets, we decided to encode this kind of external info as a 
 * // separate structure in the packet. The fields there are handled by the
 * // virtualMAC code, setting a framework of interaction.
 * </pre>
 */
enum MacControlMessage_type {
    MAC_BUFFER_FULL = 1
};

/**
 * Struct generated from src/node/communication/mac/MacPacket.msg:25 by nedtool.
 */
struct MacRadioInfoExchange_type
{
    MacRadioInfoExchange_type();
    double RSSI;
    double LQI;
};

void doPacking(cCommBuffer *b, MacRadioInfoExchange_type& a);
void doUnpacking(cCommBuffer *b, MacRadioInfoExchange_type& a);

/**
 * Class generated from <tt>src/node/communication/mac/MacPacket.msg:30</tt> by nedtool.
 * <pre>
 * packet MacPacket
 * {
 *     MacRadioInfoExchange_type macRadioInfoExchange;
 * 
 *     int source;
 *     int destination;
 *     unsigned int sequenceNumber;
 * }
 * </pre>
 */
class MacPacket : public ::cPacket
{
  protected:
    MacRadioInfoExchange_type macRadioInfoExchange_var;
    int source_var;
    int destination_var;
    unsigned int sequenceNumber_var;

  private:
    void copy(const MacPacket& other);

  protected:
    // protected and unimplemented operator==(), to prevent accidental usage
    bool operator==(const MacPacket&);

  public:
    MacPacket(const char *name=NULL, int kind=0);
    MacPacket(const MacPacket& other);
    virtual ~MacPacket();
    MacPacket& operator=(const MacPacket& other);
    virtual MacPacket *dup() const {return new MacPacket(*this);}
    virtual void parsimPack(cCommBuffer *b);
    virtual void parsimUnpack(cCommBuffer *b);

    // field getter/setter methods
    virtual MacRadioInfoExchange_type& getMacRadioInfoExchange();
    virtual const MacRadioInfoExchange_type& getMacRadioInfoExchange() const {return const_cast<MacPacket*>(this)->getMacRadioInfoExchange();}
    virtual void setMacRadioInfoExchange(const MacRadioInfoExchange_type& macRadioInfoExchange);
    virtual int getSource() const;
    virtual void setSource(int source);
    virtual int getDestination() const;
    virtual void setDestination(int destination);
    virtual unsigned int getSequenceNumber() const;
    virtual void setSequenceNumber(unsigned int sequenceNumber);
};

inline void doPacking(cCommBuffer *b, MacPacket& obj) {obj.parsimPack(b);}
inline void doUnpacking(cCommBuffer *b, MacPacket& obj) {obj.parsimUnpack(b);}

/**
 * Class generated from <tt>src/node/communication/mac/MacPacket.msg:38</tt> by nedtool.
 * <pre>
 * message MacControlMessage
 * {
 *     int macControlMessageKind @enum(MacControlMessage_type);
 * }
 * </pre>
 */
class MacControlMessage : public ::cMessage
{
  protected:
    int macControlMessageKind_var;

  private:
    void copy(const MacControlMessage& other);

  protected:
    // protected and unimplemented operator==(), to prevent accidental usage
    bool operator==(const MacControlMessage&);

  public:
    MacControlMessage(const char *name=NULL, int kind=0);
    MacControlMessage(const MacControlMessage& other);
    virtual ~MacControlMessage();
    MacControlMessage& operator=(const MacControlMessage& other);
    virtual MacControlMessage *dup() const {return new MacControlMessage(*this);}
    virtual void parsimPack(cCommBuffer *b);
    virtual void parsimUnpack(cCommBuffer *b);

    // field getter/setter methods
    virtual int getMacControlMessageKind() const;
    virtual void setMacControlMessageKind(int macControlMessageKind);
};

inline void doPacking(cCommBuffer *b, MacControlMessage& obj) {obj.parsimPack(b);}
inline void doUnpacking(cCommBuffer *b, MacControlMessage& obj) {obj.parsimUnpack(b);}


#endif // ifndef _MACPACKET_M_H_

