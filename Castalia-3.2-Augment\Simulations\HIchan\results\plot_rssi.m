% 读取 Castalia .vec 文件并画出 RSSI 折线图
filename = 'TMAC-0.vec'; % 如果你的文件名不同，请修改这里

fid = fopen(filename, 'r');
data = [];
while ~feof(fid)
    line = fgetl(fid);
    if isempty(line) || startsWith(line, 'vector') || startsWith(line, 'version') ...
            || startsWith(line, 'attr') || startsWith(line, 'run') || startsWith(line, 'file')
        continue;
    end
    parts = strsplit(strtrim(line));
    if length(parts) == 4
        % 例：0  662  2.503702615464  0
        t = str2double(parts{2});
        rssi = str2double(parts{3});
        data = [data; t, rssi];
    end
end
fclose(fid);

% 画图
figure;
plot(data(:,1), data(:,2), '-o');
xlabel('Time');
ylabel('RSSI Value');
title('RSSI Values at Receiver');
grid on;