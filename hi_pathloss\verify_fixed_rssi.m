%% 验证修复后的RSSI数据
function verify_fixed_rssi()
    fprintf('=== 验证修复后的RSSI数据 ===\\n');
    
    vec_file = '../Castalia-3.2-Augment/Simulations/HIchan/results/TMAC-0.vec';
    
    if exist(vec_file, 'file')
        fprintf('✓ 找到.vec文件\\n');
        
        % 读取数据
        fid = fopen(vec_file, 'r');
        rssi_data = [];
        time_data = [];
        
        while ~feof(fid)
            line = fgetl(fid);
            if ischar(line) && ~isempty(line)
                if ~startsWith(line, 'vector') && ~startsWith(line, 'version') && ...
                   ~startsWith(line, 'attr') && ~startsWith(line, 'run') && ~startsWith(line, 'file')
                    parts = strsplit(strtrim(line));
                    if length(parts) == 4
                        time_val = str2double(parts{3});
                        rssi_val = str2double(parts{4});
                        time_data = [time_data; time_val];
                        rssi_data = [rssi_data; rssi_val];
                    end
                end
            end
        end
        fclose(fid);
        
        % 分析结果
        fprintf('数据点数量: %d\\n', length(rssi_data));
        fprintf('RSSI范围: %.3f 到 %.3f\\n', min(rssi_data), max(rssi_data));
        fprintf('非零RSSI数量: %d\\n', sum(rssi_data ~= 0));
        
        if all(rssi_data == 0)
            fprintf('❌ RSSI仍然全为0，需要进一步调试\\n');
        else
            fprintf('✓ RSSI修复成功！\\n');
            
            % 绘制结果
            figure;
            plot(time_data, rssi_data, 'b-', 'LineWidth', 1.5);
            xlabel('时间 (s)');
            ylabel('RSSI (dBm)');
            title('修复后的RSSI时间序列');
            grid on;
        end
    else
        fprintf('❌ 未找到.vec文件，请先运行仿真\\n');
    end
end
