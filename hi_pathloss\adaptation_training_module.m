function [output, cache] = forward_pass(weights, input)
    % 前向传播 (如需要可返回中间激活以供反向传播)
    
    % 第一层
    z1 = weights.W1 * input + weights.b1;
    a1 = relu(z1);
    
    % 第二层
    z2 = weights.W2 * a1 + weights.b2;
    a2 = relu(z2);
    
    % 输出层
    output = weights.W3 * a2 + weights.b3;
    
    % 如调用方需要, 返回缓存供反向传播
    if nargout > 1
        cache = struct('input', input, 'z1', z1, 'a1', a1, 'z2', z2, 'a2', a2);
    end
end

function update_agent(agent, params)
    % 使用梯度下降更新智能体网络 (取代之前的随机噪声)
    
    if length(agent.memory) < params.batch_size
        return;
    end
    
    % 随机采样经验
    batch_indices = randperm(length(agent.memory), params.batch_size);
    
    % 初始化梯度累积器
    grad_upper = zero_like_weights(agent.upper_weights);
    grad_lower = zero_like_weights(agent.lower_weights);
    total_loss = 0;
    
    for i = 1:length(batch_indices)
        expi = agent.memory{batch_indices(i)};
        
        % -------- 前向传播 --------
        [upper_out, cache_up] = forward_pass(agent.upper_weights, expi.state);
        policy_weights = softmax(upper_out);
        lower_input = [expi.state; policy_weights];
        [lower_out, cache_low] = forward_pass(agent.lower_weights, lower_input);
        
        q_pred = lower_out(expi.action);
        
        % 目标Q值 (DQN 目标)
        if expi.done
            target_q = expi.reward;
        else
            next_action = get_best_action(agent, expi.next_state);
            next_q = get_q_value(agent, expi.next_state, next_action);
            target_q = expi.reward + agent.gamma * next_q;
        end
        
        % ---- 计算损失与梯度 ----
        diff = q_pred - target_q;              % dL/dq_pred (无0.5系数, 与下式匹配)
        total_loss = total_loss + diff^2;      % 平方损失累加
        
        % 下层网络输出梯度 (对所有动作为0, 只对采样动作有梯度)
        grad_low_output = zeros(agent.action_dim, 1);
        grad_low_output(expi.action) = 2 * diff; % dL/dy = 2*(pred-target)
        
        % 反向传播: 下层
        [grad_low, grad_lower_input] = backward_pass(agent.lower_weights, cache_low, grad_low_output);
        grad_lower = accumulate_grads(grad_lower, grad_low);
        
        % 从下层输入梯度中分离出对 policy_weights 的梯度
        grad_policy = grad_lower_input(end-3:end); % size 4x1
        
        % 通过softmax链式法则传播到上层输出 z
        J_soft = diag(policy_weights) - (policy_weights * policy_weights');
        grad_up_output = J_soft * grad_policy;
        
        % 反向传播: 上层
        [grad_up, ~] = backward_pass(agent.upper_weights, cache_up, grad_up_output);
        grad_upper = accumulate_grads(grad_upper, grad_up);
    end
    
    % 平均梯度
    grad_upper = scale_grads(grad_upper, 1/params.batch_size);
    grad_lower = scale_grads(grad_lower, 1/params.batch_size);
    
    % ---- 权重更新 ----
    lr = agent.learning_rate;
    agent.upper_weights = apply_gradients(agent.upper_weights, grad_upper, lr);
    agent.lower_weights = apply_gradients(agent.lower_weights, grad_lower, lr);
    
    % 记录损失
    agent.episode_losses(end+1) = total_loss / params.batch_size;
end

function [grads, grad_input] = backward_pass(weights, cache, grad_output)
    % 对三层全连接 + ReLU 网络执行反向传播
    % weights : 网络权重结构体
    % cache   : forward_pass 存储的中间量
    % grad_output : dL/d(output) 列向量
    
    % 提取缓存
    input = cache.input;
    z1 = cache.z1; a1 = cache.a1;
    z2 = cache.z2; a2 = cache.a2;
    
    % 输出层梯度
    grads = struct();
    grads.W3 = grad_output * a2';
    grads.b3 = grad_output;
    
    % 传播到第二层
    grad_a2 = weights.W3' * grad_output;
    grad_z2 = grad_a2 .* (z2 > 0);
    grads.W2 = grad_z2 * a1';
    grads.b2 = grad_z2;
    
    % 传播到第一层
    grad_a1 = weights.W2' * grad_z2;
    grad_z1 = grad_a1 .* (z1 > 0);
    grads.W1 = grad_z1 * input';
    grads.b1 = grad_z1;
    
    % 梯度传回输入
    grad_input = weights.W1' * grad_z1;
end

function tmpl = zero_like_weights(weights)
    % 生成零梯度模板
    tmpl = struct('W1', zeros(size(weights.W1)), 'b1', zeros(size(weights.b1)), ...
                  'W2', zeros(size(weights.W2)), 'b2', zeros(size(weights.b2)), ...
                  'W3', zeros(size(weights.W3)), 'b3', zeros(size(weights.b3)));
end

function dst = accumulate_grads(dst, src)
    % 累加梯度结构体
    fields = fieldnames(dst);
    for k = 1:numel(fields)
        dst.(fields{k}) = dst.(fields{k}) + src.(fields{k});
    end
end

function dst = scale_grads(dst, factor)
    % 缩放梯度结构体
    fields = fieldnames(dst);
    for k = 1:numel(fields)
        dst.(fields{k}) = dst.(fields{k}) * factor;
    end
end

function weights = apply_gradients(weights, grads, lr)
    % 使用学习率更新权重
    fields = fieldnames(weights);
    for k = 1:numel(fields)
        weights.(fields{k}) = weights.(fields{k}) - lr * grads.(fields{k});
    end
end

function training_results = execute_training(agent, env, params, scenario)
    % 执行训练过程
    
    % 同步学习率，允许不同场景自定义
    agent.learning_rate = params.learning_rate;
    
    fprintf('开始训练过程...\n');
end
