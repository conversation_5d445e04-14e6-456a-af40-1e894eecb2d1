% 基于梯度反向传播的自适应训练模块
% 实现真正的梯度下降算法替代噪声更新方法
%
% 主要功能:
% 1. 实现完整的前向传播和反向传播
% 2. 使用真实梯度更新网络权重
% 3. 支持分层强化学习架构
%
% 作者: [您的姓名]
% 日期: [当前日期]

function adaptation_training_module()
    % 主函数入口 - 演示梯度反向传播训练
    fprintf('=== 基于梯度反向传播的自适应训练模块 ===\n');

    % 创建测试环境和智能体
    [agent, env, params] = create_test_environment();

    % 执行训练演示
    training_results = execute_training_demo(agent, env, params);

    % 显示结果
    display_training_results(training_results);

    fprintf('训练演示完成!\n');
end

function [output, cache] = forward_pass(weights, input)
    % 前向传播 (如需要可返回中间激活以供反向传播)

    % 确保输入是列向量
    if size(input, 2) > size(input, 1)
        input = input';
    end

    % 第一层
    z1 = weights.W1 * input + weights.b1;
    a1 = relu(z1);

    % 第二层
    z2 = weights.W2 * a1 + weights.b2;
    a2 = relu(z2);

    % 输出层
    output = weights.W3 * a2 + weights.b3;

    % 如调用方需要, 返回缓存供反向传播
    if nargout > 1
        cache = struct('input', input, 'z1', z1, 'a1', a1, 'z2', z2, 'a2', a2);
    end
end

function update_agent(agent, params)
    % 使用梯度下降更新智能体网络 (取代之前的随机噪声)
    
    if length(agent.memory) < params.batch_size
        return;
    end
    
    % 随机采样经验
    batch_indices = randperm(length(agent.memory), params.batch_size);
    
    % 初始化梯度累积器
    grad_upper = zero_like_weights(agent.upper_weights);
    grad_lower = zero_like_weights(agent.lower_weights);
    total_loss = 0;
    
    for i = 1:length(batch_indices)
        expi = agent.memory{batch_indices(i)};
        
        % -------- 前向传播 --------
        [upper_out, cache_up] = forward_pass(agent.upper_weights, expi.state);
        policy_weights = softmax(upper_out);
        lower_input = [expi.state; policy_weights];
        [lower_out, cache_low] = forward_pass(agent.lower_weights, lower_input);
        
        q_pred = lower_out(expi.action);
        
        % 目标Q值 (DQN 目标)
        if expi.done
            target_q = expi.reward;
        else
            next_action = get_best_action(agent, expi.next_state);
            next_q = get_q_value(agent, expi.next_state, next_action);
            target_q = expi.reward + agent.gamma * next_q;
        end
        
        % ---- 计算损失与梯度 ----
        diff = q_pred - target_q;              % dL/dq_pred (无0.5系数, 与下式匹配)
        total_loss = total_loss + diff^2;      % 平方损失累加
        
        % 下层网络输出梯度 (对所有动作为0, 只对采样动作有梯度)
        grad_low_output = zeros(agent.action_dim, 1);
        grad_low_output(expi.action) = 2 * diff; % dL/dy = 2*(pred-target)
        
        % 反向传播: 下层
        [grad_low, grad_lower_input] = backward_pass(agent.lower_weights, cache_low, grad_low_output);
        grad_lower = accumulate_grads(grad_lower, grad_low);
        
        % 从下层输入梯度中分离出对 policy_weights 的梯度
        grad_policy = grad_lower_input(end-3:end); % size 4x1
        
        % 通过softmax链式法则传播到上层输出 z
        J_soft = diag(policy_weights) - (policy_weights * policy_weights');
        grad_up_output = J_soft * grad_policy;
        
        % 反向传播: 上层
        [grad_up, ~] = backward_pass(agent.upper_weights, cache_up, grad_up_output);
        grad_upper = accumulate_grads(grad_upper, grad_up);
    end
    
    % 平均梯度
    grad_upper = scale_grads(grad_upper, 1/params.batch_size);
    grad_lower = scale_grads(grad_lower, 1/params.batch_size);
    
    % ---- 权重更新 ----
    lr = agent.learning_rate;
    agent.upper_weights = apply_gradients(agent.upper_weights, grad_upper, lr);
    agent.lower_weights = apply_gradients(agent.lower_weights, grad_lower, lr);
    
    % 记录损失
    agent.episode_losses(end+1) = total_loss / params.batch_size;
end

function [grads, grad_input] = backward_pass(weights, cache, grad_output)
    % 对三层全连接 + ReLU 网络执行反向传播
    % weights : 网络权重结构体
    % cache   : forward_pass 存储的中间量
    % grad_output : dL/d(output) 列向量
    
    % 提取缓存
    input = cache.input;
    z1 = cache.z1; a1 = cache.a1;
    z2 = cache.z2; a2 = cache.a2;
    
    % 输出层梯度
    grads = struct();
    grads.W3 = grad_output * a2';
    grads.b3 = grad_output;
    
    % 传播到第二层
    grad_a2 = weights.W3' * grad_output;
    grad_z2 = grad_a2 .* (z2 > 0);
    grads.W2 = grad_z2 * a1';
    grads.b2 = grad_z2;
    
    % 传播到第一层
    grad_a1 = weights.W2' * grad_z2;
    grad_z1 = grad_a1 .* (z1 > 0);
    grads.W1 = grad_z1 * input';
    grads.b1 = grad_z1;
    
    % 梯度传回输入
    grad_input = weights.W1' * grad_z1;
end

function tmpl = zero_like_weights(weights)
    % 生成零梯度模板
    tmpl = struct('W1', zeros(size(weights.W1)), 'b1', zeros(size(weights.b1)), ...
                  'W2', zeros(size(weights.W2)), 'b2', zeros(size(weights.b2)), ...
                  'W3', zeros(size(weights.W3)), 'b3', zeros(size(weights.b3)));
end

function dst = accumulate_grads(dst, src)
    % 累加梯度结构体
    fields = fieldnames(dst);
    for k = 1:numel(fields)
        dst.(fields{k}) = dst.(fields{k}) + src.(fields{k});
    end
end

function dst = scale_grads(dst, factor)
    % 缩放梯度结构体
    fields = fieldnames(dst);
    for k = 1:numel(fields)
        dst.(fields{k}) = dst.(fields{k}) * factor;
    end
end

function weights = apply_gradients(weights, grads, lr)
    % 使用学习率更新权重
    fields = fieldnames(weights);
    for k = 1:numel(fields)
        weights.(fields{k}) = weights.(fields{k}) - lr * grads.(fields{k});
    end
end

% ========== 辅助函数 ==========

function y = relu(x)
    % ReLU激活函数
    y = max(0, x);
end

function y = softmax(x)
    % Softmax激活函数
    exp_x = exp(x - max(x)); % 数值稳定性
    y = exp_x / sum(exp_x);
end

function action = get_best_action(agent, state)
    % 获取最佳动作 (贪婪策略)
    [upper_out, ~] = forward_pass(agent.upper_weights, state);
    policy_weights = softmax(upper_out);
    lower_input = [state; policy_weights];
    [q_values, ~] = forward_pass(agent.lower_weights, lower_input);
    [~, action] = max(q_values);
end

function q_value = get_q_value(agent, state, action)
    % 获取特定状态-动作的Q值
    [upper_out, ~] = forward_pass(agent.upper_weights, state);
    policy_weights = softmax(upper_out);
    lower_input = [state; policy_weights];
    [q_values, ~] = forward_pass(agent.lower_weights, lower_input);
    q_value = q_values(action);
end

% ========== 测试和演示函数 ==========

function [agent, env, params] = create_test_environment()
    % 创建测试环境
    fprintf('创建测试环境...\n');

    % 环境参数
    env = struct();
    env.state_dim = 8;
    env.action_dim = 6;

    % 训练参数
    params = struct();
    params.batch_size = 32;
    params.learning_rate = 0.001;
    params.gamma = 0.9;
    params.num_episodes = 50;

    % 创建智能体
    agent = create_test_agent(env);

    fprintf('测试环境创建完成\n');
end

function agent = create_test_agent(env)
    % 创建测试智能体
    agent = struct();
    agent.state_dim = env.state_dim;
    agent.action_dim = env.action_dim;
    agent.gamma = 0.9;
    agent.learning_rate = 0.001;
    agent.episode_losses = [];
    agent.memory = {};

    % 初始化网络权重
    agent.upper_weights = initialize_weights(env.state_dim, 4); % 输出4维策略权重
    agent.lower_weights = initialize_weights(env.state_dim + 4, env.action_dim);
end

function weights = initialize_weights(input_dim, output_dim)
    % 初始化网络权重 (He初始化)
    hidden1 = 32;
    hidden2 = 16;

    weights = struct();
    weights.W1 = randn(hidden1, input_dim) * sqrt(2/input_dim);
    weights.b1 = zeros(hidden1, 1);
    weights.W2 = randn(hidden2, hidden1) * sqrt(2/hidden1);
    weights.b2 = zeros(hidden2, 1);
    weights.W3 = randn(output_dim, hidden2) * sqrt(2/hidden2);
    weights.b3 = zeros(output_dim, 1);
end

function training_results = execute_training_demo(agent, env, params)
    % 执行训练演示
    fprintf('开始训练演示...\n');

    training_results = struct();
    training_results.losses = [];
    training_results.rewards = [];

    % 生成一些模拟经验数据
    for i = 1:100
        experience = generate_mock_experience(env);
        agent.memory{end+1} = experience;
    end

    % 执行几轮训练更新
    for episode = 1:10
        fprintf('训练轮次 %d/10\n', episode);

        % 更新智能体
        update_agent(agent, params);

        % 记录损失
        if ~isempty(agent.episode_losses)
            training_results.losses(end+1) = agent.episode_losses(end);
        end

        % 测试性能
        test_reward = test_agent_performance(agent, env);
        training_results.rewards(end+1) = test_reward;
    end

    fprintf('训练演示完成\n');
end

function experience = generate_mock_experience(env)
    % 生成模拟经验数据
    experience = struct();
    experience.state = randn(env.state_dim, 1);
    experience.action = randi(env.action_dim);
    experience.reward = randn() * 0.1; % 小的随机奖励
    experience.next_state = randn(env.state_dim, 1);
    experience.done = rand() < 0.1; % 10%概率结束
end

function reward = test_agent_performance(agent, env)
    % 测试智能体性能
    total_reward = 0;
    for step = 1:20
        state = randn(env.state_dim, 1);
        action = get_best_action(agent, state);
        reward_step = randn() * 0.1; % 模拟奖励
        total_reward = total_reward + reward_step;
    end
    reward = total_reward;
end

function display_training_results(results)
    % 显示训练结果
    fprintf('\n=== 训练结果 ===\n');

    if ~isempty(results.losses)
        fprintf('平均损失: %.4f\n', mean(results.losses));
        fprintf('最终损失: %.4f\n', results.losses(end));
    end

    if ~isempty(results.rewards)
        fprintf('平均奖励: %.4f\n', mean(results.rewards));
        fprintf('最终奖励: %.4f\n', results.rewards(end));
    end

    % 绘制训练曲线
    if ~isempty(results.losses) && ~isempty(results.rewards)
        figure('Name', '训练结果', 'Position', [100, 100, 800, 400]);

        subplot(1, 2, 1);
        plot(results.losses, 'b-', 'LineWidth', 2);
        title('训练损失');
        xlabel('训练轮次');
        ylabel('损失值');
        grid on;

        subplot(1, 2, 2);
        plot(results.rewards, 'r-', 'LineWidth', 2);
        title('测试奖励');
        xlabel('训练轮次');
        ylabel('奖励值');
        grid on;

        fprintf('训练曲线已绘制\n');
    end
end
