# ****************************************************************************
# *  Copyright: National ICT Australia,  2009 - 2010                         *
# *  Developed at the ATP lab, Networked Systems research theme              *
# *  Author(s): <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>                        *
# *  This file is distributed under the terms in the attached LICENSE file.  *
# *  If you do not find this file, copies can be found by writing to:        *
# *                                                                          *
# *      NICTA, Locked Bag 9013, Alexandria, NSW 1435, Australia             *
# *      Attention:  License Inquiry.                                        *
# *                                                                          *
# ***************************************************************************/


RX MODES
# Name, dataRate(kbps), modulationType, bitsPerSymbol, bandwidth(MHz), noiseBandwidth(MHz), noiseFloor(dBm), sensitivity(dBm), powerConsumed(mW)
normal, 250, PSK, 4, 20, 194, -100, -95, 62
IDEAL, 1024, <PERSON><PERSON><PERSON>, 2, 20, 1000, -100, -97, 0

TX LEVELS
Tx_dBm 5 2 0 -5 -10 -15 -20
Tx_mW 133.5 126.3 124.5 120.91 117.76 116.6 115.75 

# real values: 27.3 20.1 18.3 14.71 11.56 10.40 9.55

DELAY TRANSITION MATRIX
# State switching times (time to switch from column state to row state, in msec)
#	RX	TX	SLEEP
RX	-	0.02	0.194
TX	0.02	-	0.194
SLEEP	0.02	0.05	-

POWER TRANSITION MATRIX
#       RX      TX      SLEEP
RX	-	3.0	3.0
TX	3.0	-	3.0
SLEEP	1.4	1.4	-

SLEEP LEVELS
idle 1.4, -, -, -, -
