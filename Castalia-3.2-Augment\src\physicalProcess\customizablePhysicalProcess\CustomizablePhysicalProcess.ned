//********************************************************************************
//*  Copyright: National ICT Australia,  2007 - 2010                             *
//*  Developed at the ATP lab, Networked Systems research theme                  *
//*  Author(s): <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>  *
//*  This file is distributed under the terms in the attached LICENSE file.      *
//*  If you do not find this file, copies can be found by writing to:            *
//*                                                                              *
//*      NICTA, Locked Bag 9013, Alexandria, NSW 1435, Australia                 *
//*      Attention:  License Inquiry.                                            *
//*                                                                              *
//*******************************************************************************/

package physicalProcess.customizablePhysicalProcess;

//=======================================================================================
// The physical process module simulates a physical process that could be measured/sampled
// by a sensing device on the nodes. Different sensing devices (e.g temperature, pressure,
// light, acceleration) would be represented by distinct PhysicalProcess modules. A node
// simply ask the process for a sample which is returned as soon as it is calculated.
// The default values are set to simulate 'Fire' that is compatible with default
// sensor device 'Temperature'
//=======================================================================================

simple CustomizablePhysicalProcess like physicalProcess.iPhysicalProcess {
 parameters:
 	bool collectTraceInfo = default (false);
	double inputType = default (1);	// 0 --> values are dictated by "directNodeValueAssignment" parameter
									// 1 --> values are generated by the additive diffused sources 
									// 2 --> values read by the tracefile  (not yet ipmlemented)

	string directNodeValueAssignment = default ("(0)");	
									// Statically assign values to specific nodes.
									// Syntax explanantion : "(default_value) nodeID_A:value_A nodeID_B:val_B ... etc"
									// If some nodes are not mentioned/defined then they get the default_value.

	double multiplicative_k = default (0.25);	// multiplicative parameter (k)
	double attenuation_exp_a = default (1.0);	// attenuation exponent (a)
	double sigma = default (0.2);				// standart deviation to the zero-mean additive gaussian noise (sigma)
	double max_num_snapshots = default (10);	// the maximum number of descrete states/snapshots that can 
												// be specified in source_X params below                               
	double numSources = default (1);			// how many points in space will return this value, 
												// up to 5 sources supported

	// strings showing how sources evolve over time (change in location and value)
	string source_0 = default ("0 10 10 30.5; 5 10 10 45; 12 10 10 7.3");
	string source_1 = default ("");
	string source_2 = default ("");
	string source_3 = default ("");
	string source_4 = default ("");

	string tracefileName = default ("");
	string description = default ("Fire");

 gates:
 	output toNode[];
	input fromNode[];

}
