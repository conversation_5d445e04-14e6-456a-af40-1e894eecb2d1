<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<plugin>
	<extension point="org.eclipse.cdt.core.LanguageSettingsProvider">
		<provider class="org.eclipse.cdt.managedbuilder.language.settings.providers.GCCBuiltinSpecsDetector" console="false" env-hash="-1181394571734253711" id="org.eclipse.cdt.managedbuilder.core.GCCBuiltinSpecsDetector" keep-relative-paths="false" name="CDT GCC Built-in Compiler Settings" parameter="${COMMAND} ${FLAGS} -E -P -v -dD &quot;${INPUTS}&quot;">
			<language-scope id="org.eclipse.cdt.core.gcc"/>
			<language-scope id="org.eclipse.cdt.core.g++"/>
			<language id="org.eclipse.cdt.core.gcc">
				<entry kind="includePath" name="C:/omnetpp-4.6/tools/win32/mingw32/lib/gcc/i686-w64-mingw32/4.9.2/include">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="includePath" name="C:/omnetpp-4.6/tools/win32/mingw32/include">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="includePath" name="C:/omnetpp-4.6/tools/win32/mingw32/lib/gcc/i686-w64-mingw32/4.9.2/include-fixed">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="includePath" name="C:/omnetpp-4.6/tools/win32/mingw32/i686-w64-mingw32/include">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="WIN32" value="1">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="WINNT" value="1">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="_INTEGRAL_MAX_BITS" value="64">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="_REENTRANT" value="1">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="_WIN32" value="1">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="_X86_" value="1">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__ATOMIC_ACQUIRE" value="2">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__ATOMIC_ACQ_REL" value="4">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__ATOMIC_CONSUME" value="1">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__ATOMIC_HLE_ACQUIRE" value="65536">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__ATOMIC_HLE_RELEASE" value="131072">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__ATOMIC_RELAXED" value="0">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__ATOMIC_RELEASE" value="3">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__ATOMIC_SEQ_CST" value="5">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__BIGGEST_ALIGNMENT__" value="16">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__BYTE_ORDER__" value="__ORDER_LITTLE_ENDIAN__">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__CHAR16_TYPE__" value="short unsigned int">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__CHAR32_TYPE__" value="unsigned int">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__CHAR_BIT__" value="8">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__DBL_DECIMAL_DIG__" value="17">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__DBL_DENORM_MIN__" value="((double)4.94065645841246544177e-324L)">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__DBL_DIG__" value="15">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__DBL_EPSILON__" value="((double)2.22044604925031308085e-16L)">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__DBL_HAS_DENORM__" value="1">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__DBL_HAS_INFINITY__" value="1">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__DBL_HAS_QUIET_NAN__" value="1">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__DBL_MANT_DIG__" value="53">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__DBL_MAX_10_EXP__" value="308">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__DBL_MAX_EXP__" value="1024">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__DBL_MAX__" value="((double)1.79769313486231570815e+308L)">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__DBL_MIN_10_EXP__" value="(-307)">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__DBL_MIN_EXP__" value="(-1021)">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__DBL_MIN__" value="((double)2.22507385850720138309e-308L)">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__DEC128_EPSILON__" value="1E-33DL">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__DEC128_MANT_DIG__" value="34">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__DEC128_MAX_EXP__" value="6145">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__DEC128_MAX__" value="9.999999999999999999999999999999999E6144DL">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__DEC128_MIN_EXP__" value="(-6142)">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__DEC128_MIN__" value="1E-6143DL">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__DEC128_SUBNORMAL_MIN__" value="0.000000000000000000000000000000001E-6143DL">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__DEC32_EPSILON__" value="1E-6DF">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__DEC32_MANT_DIG__" value="7">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__DEC32_MAX_EXP__" value="97">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__DEC32_MAX__" value="9.999999E96DF">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__DEC32_MIN_EXP__" value="(-94)">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__DEC32_MIN__" value="1E-95DF">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__DEC32_SUBNORMAL_MIN__" value="0.000001E-95DF">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__DEC64_EPSILON__" value="1E-15DD">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__DEC64_MANT_DIG__" value="16">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__DEC64_MAX_EXP__" value="385">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__DEC64_MAX__" value="9.999999999999999E384DD">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__DEC64_MIN_EXP__" value="(-382)">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__DEC64_MIN__" value="1E-383DD">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__DEC64_SUBNORMAL_MIN__" value="0.000000000000001E-383DD">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__DECIMAL_BID_FORMAT__" value="1">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__DECIMAL_DIG__" value="21">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__DEC_EVAL_METHOD__" value="2">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__FINITE_MATH_ONLY__" value="0">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__FLOAT_WORD_ORDER__" value="__ORDER_LITTLE_ENDIAN__">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__FLT_DECIMAL_DIG__" value="9">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__FLT_DENORM_MIN__" value="1.40129846432481707092e-45F">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__FLT_DIG__" value="6">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__FLT_EPSILON__" value="1.19209289550781250000e-7F">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__FLT_EVAL_METHOD__" value="2">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__FLT_HAS_DENORM__" value="1">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__FLT_HAS_INFINITY__" value="1">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__FLT_HAS_QUIET_NAN__" value="1">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__FLT_MANT_DIG__" value="24">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__FLT_MAX_10_EXP__" value="38">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__FLT_MAX_EXP__" value="128">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__FLT_MAX__" value="3.40282346638528859812e+38F">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__FLT_MIN_10_EXP__" value="(-37)">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__FLT_MIN_EXP__" value="(-125)">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__FLT_MIN__" value="1.17549435082228750797e-38F">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__FLT_RADIX__" value="2">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__GCC_ATOMIC_BOOL_LOCK_FREE" value="2">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__GCC_ATOMIC_CHAR16_T_LOCK_FREE" value="2">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__GCC_ATOMIC_CHAR32_T_LOCK_FREE" value="2">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__GCC_ATOMIC_CHAR_LOCK_FREE" value="2">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__GCC_ATOMIC_INT_LOCK_FREE" value="2">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__GCC_ATOMIC_LLONG_LOCK_FREE" value="2">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__GCC_ATOMIC_LONG_LOCK_FREE" value="2">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__GCC_ATOMIC_POINTER_LOCK_FREE" value="2">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__GCC_ATOMIC_SHORT_LOCK_FREE" value="2">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__GCC_ATOMIC_TEST_AND_SET_TRUEVAL" value="1">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__GCC_ATOMIC_WCHAR_T_LOCK_FREE" value="2">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__GCC_HAVE_DWARF2_CFI_ASM" value="1">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__GCC_HAVE_SYNC_COMPARE_AND_SWAP_1" value="1">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__GCC_HAVE_SYNC_COMPARE_AND_SWAP_2" value="1">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__GCC_HAVE_SYNC_COMPARE_AND_SWAP_4" value="1">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__GCC_HAVE_SYNC_COMPARE_AND_SWAP_8" value="1">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__GCC_IEC_559" value="2">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__GCC_IEC_559_COMPLEX" value="2">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__GNUC_GNU_INLINE__" value="1">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__GNUC_MINOR__" value="9">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__GNUC_PATCHLEVEL__" value="2">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__GNUC__" value="4">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__GXX_ABI_VERSION" value="1002">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__GXX_MERGED_TYPEINFO_NAMES" value="0">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__GXX_TYPEINFO_EQUALITY_INLINE" value="0">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__INT16_C(c)" value="c">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__INT16_MAX__" value="32767">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__INT16_TYPE__" value="short int">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__INT32_C(c)" value="c">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__INT32_MAX__" value="2147483647">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__INT32_TYPE__" value="int">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__INT64_C(c)" value="c ## LL">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__INT64_MAX__" value="9223372036854775807LL">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__INT64_TYPE__" value="long long int">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__INT8_C(c)" value="c">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__INT8_MAX__" value="127">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__INT8_TYPE__" value="signed char">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__INTMAX_C(c)" value="c ## LL">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__INTMAX_MAX__" value="9223372036854775807LL">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__INTMAX_TYPE__" value="long long int">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__INTPTR_MAX__" value="2147483647">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__INTPTR_TYPE__" value="int">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__INT_FAST16_MAX__" value="32767">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__INT_FAST16_TYPE__" value="short int">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__INT_FAST32_MAX__" value="2147483647">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__INT_FAST32_TYPE__" value="int">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__INT_FAST64_MAX__" value="9223372036854775807LL">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__INT_FAST64_TYPE__" value="long long int">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__INT_FAST8_MAX__" value="127">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__INT_FAST8_TYPE__" value="signed char">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__INT_LEAST16_MAX__" value="32767">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__INT_LEAST16_TYPE__" value="short int">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__INT_LEAST32_MAX__" value="2147483647">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__INT_LEAST32_TYPE__" value="int">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__INT_LEAST64_MAX__" value="9223372036854775807LL">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__INT_LEAST64_TYPE__" value="long long int">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__INT_LEAST8_MAX__" value="127">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__INT_LEAST8_TYPE__" value="signed char">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__INT_MAX__" value="2147483647">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__LDBL_DENORM_MIN__" value="3.64519953188247460253e-4951L">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__LDBL_DIG__" value="18">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__LDBL_EPSILON__" value="1.08420217248550443401e-19L">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__LDBL_HAS_DENORM__" value="1">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__LDBL_HAS_INFINITY__" value="1">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__LDBL_HAS_QUIET_NAN__" value="1">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__LDBL_MANT_DIG__" value="64">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__LDBL_MAX_10_EXP__" value="4932">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__LDBL_MAX_EXP__" value="16384">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__LDBL_MAX__" value="1.18973149535723176502e+4932L">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__LDBL_MIN_10_EXP__" value="(-4931)">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__LDBL_MIN_EXP__" value="(-16381)">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__LDBL_MIN__" value="3.36210314311209350626e-4932L">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__LONG_LONG_MAX__" value="9223372036854775807LL">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__LONG_MAX__" value="2147483647L">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__MINGW32__" value="1">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__MSVCRT__" value="1">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__NO_INLINE__" value="1">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__ORDER_BIG_ENDIAN__" value="4321">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__ORDER_LITTLE_ENDIAN__" value="1234">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__ORDER_PDP_ENDIAN__" value="3412">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__PRAGMA_REDEFINE_EXTNAME" value="1">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__PTRDIFF_MAX__" value="2147483647">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__PTRDIFF_TYPE__" value="int">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__REGISTER_PREFIX__" value="">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__SCHAR_MAX__" value="127">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__SHRT_MAX__" value="32767">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__SIG_ATOMIC_MAX__" value="2147483647">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__SIG_ATOMIC_MIN__" value="(-__SIG_ATOMIC_MAX__ - 1)">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__SIG_ATOMIC_TYPE__" value="int">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__SIZEOF_DOUBLE__" value="8">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__SIZEOF_FLOAT__" value="4">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__SIZEOF_INT__" value="4">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__SIZEOF_LONG_DOUBLE__" value="12">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__SIZEOF_LONG_LONG__" value="8">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__SIZEOF_LONG__" value="4">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__SIZEOF_POINTER__" value="4">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__SIZEOF_PTRDIFF_T__" value="4">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__SIZEOF_SHORT__" value="2">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__SIZEOF_SIZE_T__" value="4">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__SIZEOF_WCHAR_T__" value="2">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__SIZEOF_WINT_T__" value="2">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__SIZE_MAX__" value="4294967295U">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__SIZE_TYPE__" value="unsigned int">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__STDC_HOSTED__" value="1">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__STDC__" value="1">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__UINT16_C(c)" value="c">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__UINT16_MAX__" value="65535">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__UINT16_TYPE__" value="short unsigned int">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__UINT32_C(c)" value="c ## U">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__UINT32_MAX__" value="4294967295U">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__UINT32_TYPE__" value="unsigned int">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__UINT64_C(c)" value="c ## ULL">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__UINT64_MAX__" value="18446744073709551615ULL">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__UINT64_TYPE__" value="long long unsigned int">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__UINT8_C(c)" value="c">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__UINT8_MAX__" value="255">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__UINT8_TYPE__" value="unsigned char">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__UINTMAX_C(c)" value="c ## ULL">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__UINTMAX_MAX__" value="18446744073709551615ULL">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__UINTMAX_TYPE__" value="long long unsigned int">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__UINTPTR_MAX__" value="4294967295U">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__UINTPTR_TYPE__" value="unsigned int">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__UINT_FAST16_MAX__" value="65535">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__UINT_FAST16_TYPE__" value="short unsigned int">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__UINT_FAST32_MAX__" value="4294967295U">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__UINT_FAST32_TYPE__" value="unsigned int">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__UINT_FAST64_MAX__" value="18446744073709551615ULL">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__UINT_FAST64_TYPE__" value="long long unsigned int">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__UINT_FAST8_MAX__" value="255">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__UINT_FAST8_TYPE__" value="unsigned char">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__UINT_LEAST16_MAX__" value="65535">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__UINT_LEAST16_TYPE__" value="short unsigned int">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__UINT_LEAST32_MAX__" value="4294967295U">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__UINT_LEAST32_TYPE__" value="unsigned int">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__UINT_LEAST64_MAX__" value="18446744073709551615ULL">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__UINT_LEAST64_TYPE__" value="long long unsigned int">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__UINT_LEAST8_MAX__" value="255">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__UINT_LEAST8_TYPE__" value="unsigned char">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__USER_LABEL_PREFIX__" value="_">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__VERSION__" value="&quot;4.9.2&quot;">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__WCHAR_MAX__" value="65535">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__WCHAR_MIN__" value="0">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__WCHAR_TYPE__" value="short unsigned int">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__WIN32" value="1">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__WIN32__" value="1">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__WINNT" value="1">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__WINNT__" value="1">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__WINT_MAX__" value="65535">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__WINT_MIN__" value="0">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__WINT_TYPE__" value="short unsigned int">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__cdecl" value="__attribute__((__cdecl__))">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__code_model_32__" value="1">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__declspec(x)" value="__attribute__((x))">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__fastcall" value="__attribute__((__fastcall__))">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__has_include(STR)" value="__has_include__(STR)">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__has_include_next(STR)" value="__has_include_next__(STR)">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__i386" value="1">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__i386__" value="1">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__i686" value="1">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__i686__" value="1">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__pentiumpro" value="1">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__pentiumpro__" value="1">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__stdcall" value="__attribute__((__stdcall__))">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__thiscall" value="__attribute__((__thiscall__))">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="_cdecl" value="__attribute__((__cdecl__))">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="_fastcall" value="__attribute__((__fastcall__))">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="_stdcall" value="__attribute__((__stdcall__))">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="_thiscall" value="__attribute__((__thiscall__))">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="i386" value="1">
					<flag value="BUILTIN|READONLY"/>
				</entry>
			</language>
			<language id="org.eclipse.cdt.core.g++">
				<entry kind="includePath" name="C:/omnetpp-4.6/tools/win32/mingw32/lib/gcc/i686-w64-mingw32/4.9.2/include">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="includePath" name="C:/omnetpp-4.6/tools/win32/mingw32/include">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="includePath" name="C:/omnetpp-4.6/tools/win32/mingw32/lib/gcc/i686-w64-mingw32/4.9.2/include-fixed">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="includePath" name="C:/omnetpp-4.6/tools/win32/mingw32/i686-w64-mingw32/include">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="includePath" name="C:/omnetpp-4.6/tools/win32/mingw32/include/c++/4.9.2">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="includePath" name="C:/omnetpp-4.6/tools/win32/mingw32/include/c++/4.9.2/i686-w64-mingw32">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="includePath" name="C:/omnetpp-4.6/tools/win32/mingw32/include/c++/4.9.2/backward">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="WIN32" value="1">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="WINNT" value="1">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="_INTEGRAL_MAX_BITS" value="64">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="_REENTRANT" value="1">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="_WIN32" value="1">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="_X86_" value="1">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__ATOMIC_ACQUIRE" value="2">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__ATOMIC_ACQ_REL" value="4">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__ATOMIC_CONSUME" value="1">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__ATOMIC_HLE_ACQUIRE" value="65536">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__ATOMIC_HLE_RELEASE" value="131072">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__ATOMIC_RELAXED" value="0">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__ATOMIC_RELEASE" value="3">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__ATOMIC_SEQ_CST" value="5">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__BIGGEST_ALIGNMENT__" value="16">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__BYTE_ORDER__" value="__ORDER_LITTLE_ENDIAN__">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__CHAR16_TYPE__" value="short unsigned int">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__CHAR32_TYPE__" value="unsigned int">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__CHAR_BIT__" value="8">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__DBL_DECIMAL_DIG__" value="17">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__DBL_DENORM_MIN__" value="double(4.94065645841246544177e-324L)">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__DBL_DIG__" value="15">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__DBL_EPSILON__" value="double(2.22044604925031308085e-16L)">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__DBL_HAS_DENORM__" value="1">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__DBL_HAS_INFINITY__" value="1">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__DBL_HAS_QUIET_NAN__" value="1">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__DBL_MANT_DIG__" value="53">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__DBL_MAX_10_EXP__" value="308">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__DBL_MAX_EXP__" value="1024">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__DBL_MAX__" value="double(1.79769313486231570815e+308L)">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__DBL_MIN_10_EXP__" value="(-307)">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__DBL_MIN_EXP__" value="(-1021)">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__DBL_MIN__" value="double(2.22507385850720138309e-308L)">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__DEC128_EPSILON__" value="1E-33DL">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__DEC128_MANT_DIG__" value="34">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__DEC128_MAX_EXP__" value="6145">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__DEC128_MAX__" value="9.999999999999999999999999999999999E6144DL">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__DEC128_MIN_EXP__" value="(-6142)">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__DEC128_MIN__" value="1E-6143DL">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__DEC128_SUBNORMAL_MIN__" value="0.000000000000000000000000000000001E-6143DL">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__DEC32_EPSILON__" value="1E-6DF">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__DEC32_MANT_DIG__" value="7">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__DEC32_MAX_EXP__" value="97">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__DEC32_MAX__" value="9.999999E96DF">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__DEC32_MIN_EXP__" value="(-94)">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__DEC32_MIN__" value="1E-95DF">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__DEC32_SUBNORMAL_MIN__" value="0.000001E-95DF">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__DEC64_EPSILON__" value="1E-15DD">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__DEC64_MANT_DIG__" value="16">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__DEC64_MAX_EXP__" value="385">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__DEC64_MAX__" value="9.999999999999999E384DD">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__DEC64_MIN_EXP__" value="(-382)">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__DEC64_MIN__" value="1E-383DD">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__DEC64_SUBNORMAL_MIN__" value="0.000000000000001E-383DD">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__DECIMAL_BID_FORMAT__" value="1">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__DECIMAL_DIG__" value="21">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__DEC_EVAL_METHOD__" value="2">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__DEPRECATED" value="1">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__EXCEPTIONS" value="1">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__FINITE_MATH_ONLY__" value="0">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__FLOAT_WORD_ORDER__" value="__ORDER_LITTLE_ENDIAN__">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__FLT_DECIMAL_DIG__" value="9">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__FLT_DENORM_MIN__" value="1.40129846432481707092e-45F">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__FLT_DIG__" value="6">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__FLT_EPSILON__" value="1.19209289550781250000e-7F">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__FLT_EVAL_METHOD__" value="2">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__FLT_HAS_DENORM__" value="1">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__FLT_HAS_INFINITY__" value="1">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__FLT_HAS_QUIET_NAN__" value="1">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__FLT_MANT_DIG__" value="24">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__FLT_MAX_10_EXP__" value="38">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__FLT_MAX_EXP__" value="128">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__FLT_MAX__" value="3.40282346638528859812e+38F">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__FLT_MIN_10_EXP__" value="(-37)">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__FLT_MIN_EXP__" value="(-125)">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__FLT_MIN__" value="1.17549435082228750797e-38F">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__FLT_RADIX__" value="2">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__GCC_ATOMIC_BOOL_LOCK_FREE" value="2">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__GCC_ATOMIC_CHAR16_T_LOCK_FREE" value="2">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__GCC_ATOMIC_CHAR32_T_LOCK_FREE" value="2">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__GCC_ATOMIC_CHAR_LOCK_FREE" value="2">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__GCC_ATOMIC_INT_LOCK_FREE" value="2">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__GCC_ATOMIC_LLONG_LOCK_FREE" value="2">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__GCC_ATOMIC_LONG_LOCK_FREE" value="2">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__GCC_ATOMIC_POINTER_LOCK_FREE" value="2">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__GCC_ATOMIC_SHORT_LOCK_FREE" value="2">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__GCC_ATOMIC_TEST_AND_SET_TRUEVAL" value="1">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__GCC_ATOMIC_WCHAR_T_LOCK_FREE" value="2">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__GCC_HAVE_DWARF2_CFI_ASM" value="1">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__GCC_HAVE_SYNC_COMPARE_AND_SWAP_1" value="1">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__GCC_HAVE_SYNC_COMPARE_AND_SWAP_2" value="1">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__GCC_HAVE_SYNC_COMPARE_AND_SWAP_4" value="1">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__GCC_HAVE_SYNC_COMPARE_AND_SWAP_8" value="1">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__GCC_IEC_559" value="2">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__GCC_IEC_559_COMPLEX" value="2">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__GNUC_GNU_INLINE__" value="1">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__GNUC_MINOR__" value="9">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__GNUC_PATCHLEVEL__" value="2">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__GNUC__" value="4">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__GNUG__" value="4">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__GXX_ABI_VERSION" value="1002">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__GXX_MERGED_TYPEINFO_NAMES" value="0">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__GXX_RTTI" value="1">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__GXX_TYPEINFO_EQUALITY_INLINE" value="0">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__GXX_WEAK__" value="1">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__INT16_C(c)" value="c">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__INT16_MAX__" value="32767">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__INT16_TYPE__" value="short int">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__INT32_C(c)" value="c">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__INT32_MAX__" value="2147483647">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__INT32_TYPE__" value="int">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__INT64_C(c)" value="c ## LL">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__INT64_MAX__" value="9223372036854775807LL">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__INT64_TYPE__" value="long long int">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__INT8_C(c)" value="c">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__INT8_MAX__" value="127">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__INT8_TYPE__" value="signed char">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__INTMAX_C(c)" value="c ## LL">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__INTMAX_MAX__" value="9223372036854775807LL">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__INTMAX_TYPE__" value="long long int">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__INTPTR_MAX__" value="2147483647">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__INTPTR_TYPE__" value="int">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__INT_FAST16_MAX__" value="32767">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__INT_FAST16_TYPE__" value="short int">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__INT_FAST32_MAX__" value="2147483647">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__INT_FAST32_TYPE__" value="int">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__INT_FAST64_MAX__" value="9223372036854775807LL">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__INT_FAST64_TYPE__" value="long long int">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__INT_FAST8_MAX__" value="127">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__INT_FAST8_TYPE__" value="signed char">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__INT_LEAST16_MAX__" value="32767">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__INT_LEAST16_TYPE__" value="short int">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__INT_LEAST32_MAX__" value="2147483647">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__INT_LEAST32_TYPE__" value="int">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__INT_LEAST64_MAX__" value="9223372036854775807LL">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__INT_LEAST64_TYPE__" value="long long int">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__INT_LEAST8_MAX__" value="127">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__INT_LEAST8_TYPE__" value="signed char">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__INT_MAX__" value="2147483647">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__LDBL_DENORM_MIN__" value="3.64519953188247460253e-4951L">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__LDBL_DIG__" value="18">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__LDBL_EPSILON__" value="1.08420217248550443401e-19L">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__LDBL_HAS_DENORM__" value="1">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__LDBL_HAS_INFINITY__" value="1">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__LDBL_HAS_QUIET_NAN__" value="1">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__LDBL_MANT_DIG__" value="64">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__LDBL_MAX_10_EXP__" value="4932">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__LDBL_MAX_EXP__" value="16384">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__LDBL_MAX__" value="1.18973149535723176502e+4932L">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__LDBL_MIN_10_EXP__" value="(-4931)">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__LDBL_MIN_EXP__" value="(-16381)">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__LDBL_MIN__" value="3.36210314311209350626e-4932L">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__LONG_LONG_MAX__" value="9223372036854775807LL">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__LONG_MAX__" value="2147483647L">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__MINGW32__" value="1">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__MSVCRT__" value="1">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__NO_INLINE__" value="1">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__ORDER_BIG_ENDIAN__" value="4321">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__ORDER_LITTLE_ENDIAN__" value="1234">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__ORDER_PDP_ENDIAN__" value="3412">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__PRAGMA_REDEFINE_EXTNAME" value="1">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__PTRDIFF_MAX__" value="2147483647">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__PTRDIFF_TYPE__" value="int">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__REGISTER_PREFIX__" value="">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__SCHAR_MAX__" value="127">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__SHRT_MAX__" value="32767">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__SIG_ATOMIC_MAX__" value="2147483647">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__SIG_ATOMIC_MIN__" value="(-__SIG_ATOMIC_MAX__ - 1)">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__SIG_ATOMIC_TYPE__" value="int">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__SIZEOF_DOUBLE__" value="8">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__SIZEOF_FLOAT__" value="4">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__SIZEOF_INT__" value="4">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__SIZEOF_LONG_DOUBLE__" value="12">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__SIZEOF_LONG_LONG__" value="8">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__SIZEOF_LONG__" value="4">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__SIZEOF_POINTER__" value="4">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__SIZEOF_PTRDIFF_T__" value="4">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__SIZEOF_SHORT__" value="2">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__SIZEOF_SIZE_T__" value="4">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__SIZEOF_WCHAR_T__" value="2">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__SIZEOF_WINT_T__" value="2">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__SIZE_MAX__" value="4294967295U">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__SIZE_TYPE__" value="unsigned int">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__STDC_HOSTED__" value="1">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__STDC__" value="1">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__UINT16_C(c)" value="c">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__UINT16_MAX__" value="65535">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__UINT16_TYPE__" value="short unsigned int">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__UINT32_C(c)" value="c ## U">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__UINT32_MAX__" value="4294967295U">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__UINT32_TYPE__" value="unsigned int">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__UINT64_C(c)" value="c ## ULL">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__UINT64_MAX__" value="18446744073709551615ULL">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__UINT64_TYPE__" value="long long unsigned int">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__UINT8_C(c)" value="c">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__UINT8_MAX__" value="255">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__UINT8_TYPE__" value="unsigned char">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__UINTMAX_C(c)" value="c ## ULL">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__UINTMAX_MAX__" value="18446744073709551615ULL">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__UINTMAX_TYPE__" value="long long unsigned int">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__UINTPTR_MAX__" value="4294967295U">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__UINTPTR_TYPE__" value="unsigned int">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__UINT_FAST16_MAX__" value="65535">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__UINT_FAST16_TYPE__" value="short unsigned int">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__UINT_FAST32_MAX__" value="4294967295U">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__UINT_FAST32_TYPE__" value="unsigned int">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__UINT_FAST64_MAX__" value="18446744073709551615ULL">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__UINT_FAST64_TYPE__" value="long long unsigned int">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__UINT_FAST8_MAX__" value="255">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__UINT_FAST8_TYPE__" value="unsigned char">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__UINT_LEAST16_MAX__" value="65535">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__UINT_LEAST16_TYPE__" value="short unsigned int">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__UINT_LEAST32_MAX__" value="4294967295U">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__UINT_LEAST32_TYPE__" value="unsigned int">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__UINT_LEAST64_MAX__" value="18446744073709551615ULL">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__UINT_LEAST64_TYPE__" value="long long unsigned int">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__UINT_LEAST8_MAX__" value="255">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__UINT_LEAST8_TYPE__" value="unsigned char">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__USER_LABEL_PREFIX__" value="_">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__VERSION__" value="&quot;4.9.2&quot;">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__WCHAR_MAX__" value="65535">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__WCHAR_MIN__" value="0">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__WCHAR_TYPE__" value="short unsigned int">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__WCHAR_UNSIGNED__" value="1">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__WIN32" value="1">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__WIN32__" value="1">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__WINNT" value="1">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__WINNT__" value="1">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__WINT_MAX__" value="65535">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__WINT_MIN__" value="0">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__WINT_TYPE__" value="short unsigned int">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__cdecl" value="__attribute__((__cdecl__))">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__code_model_32__" value="1">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__cplusplus" value="199711L">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__cpp_binary_literals" value="201304">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__declspec(x)" value="__attribute__((x))">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__fastcall" value="__attribute__((__fastcall__))">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__has_include(STR)" value="__has_include__(STR)">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__has_include_next(STR)" value="__has_include_next__(STR)">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__i386" value="1">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__i386__" value="1">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__i686" value="1">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__i686__" value="1">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__pentiumpro" value="1">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__pentiumpro__" value="1">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__stdcall" value="__attribute__((__stdcall__))">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="__thiscall" value="__attribute__((__thiscall__))">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="_cdecl" value="__attribute__((__cdecl__))">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="_fastcall" value="__attribute__((__fastcall__))">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="_stdcall" value="__attribute__((__stdcall__))">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="_thiscall" value="__attribute__((__thiscall__))">
					<flag value="BUILTIN|READONLY"/>
				</entry>
				<entry kind="macro" name="i386" value="1">
					<flag value="BUILTIN|READONLY"/>
				</entry>
			</language>
		</provider>
	</extension>
</plugin>
