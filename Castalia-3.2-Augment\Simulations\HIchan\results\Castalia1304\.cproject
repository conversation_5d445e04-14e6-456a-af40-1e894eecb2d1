<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<?fileVersion 4.0.0?><cproject storage_type_id="org.eclipse.cdt.core.XmlProjectDescriptionStorage">
	<storageModule moduleId="org.eclipse.cdt.core.settings">
		<cconfiguration id="org.omnetpp.cdt.gnu.config.debug.718612962">
			<storageModule buildSystemId="org.eclipse.cdt.managedbuilder.core.configurationDataProvider" id="org.omnetpp.cdt.gnu.config.debug.718612962" moduleId="org.eclipse.cdt.core.settings" name="gcc-debug">
				<externalSettings/>
				<extensions>
					<extension id="org.eclipse.cdt.core.GmakeErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.CWDLocator" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GCCErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GASErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GLDErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.MachO64" point="org.eclipse.cdt.core.BinaryParser"/>
					<extension id="org.eclipse.cdt.core.ELF" point="org.eclipse.cdt.core.BinaryParser"/>
					<extension id="org.eclipse.cdt.core.PE" point="org.eclipse.cdt.core.BinaryParser"/>
				</extensions>
			</storageModule>
			<storageModule moduleId="cdtBuildSystem" version="4.0.0">
				<configuration artifactName="${ProjName}" buildProperties="" description="" id="org.omnetpp.cdt.gnu.config.debug.718612962" name="gcc-debug" parent="org.omnetpp.cdt.gnu.config.debug">
					<folderInfo id="org.omnetpp.cdt.gnu.config.debug.718612962." name="/" resourcePath="">
						<toolChain id="org.omnetpp.cdt.gnu.toolchain.debug.865753456" name="GCC for OMNeT++" superClass="org.omnetpp.cdt.gnu.toolchain.debug">
							<targetPlatform binaryParser="org.eclipse.cdt.core.ELF;org.eclipse.cdt.core.MachO64;org.eclipse.cdt.core.PE" id="org.omnetpp.cdt.targetPlatform.1179216299" isAbstract="false" name="Windows, Linux, Mac" osList="win32,linux,macosx" superClass="org.omnetpp.cdt.targetPlatform"/>
							<builder id="org.omnetpp.cdt.gnu.builder.debug.1635438849" managedBuildOn="true" name="OMNeT++ Make Builder (opp_makemake).gcc-debug" superClass="org.omnetpp.cdt.gnu.builder.debug"/>
							<tool id="cdt.managedbuild.tool.gnu.archiver.base.1938350639" name="GCC Archiver" superClass="cdt.managedbuild.tool.gnu.archiver.base"/>
							<tool id="cdt.managedbuild.tool.gnu.cpp.compiler.base.1560134740" name="GCC C++ Compiler" superClass="cdt.managedbuild.tool.gnu.cpp.compiler.base"/>
							<tool id="cdt.managedbuild.tool.gnu.c.compiler.base.2120302505" name="GCC C Compiler" superClass="cdt.managedbuild.tool.gnu.c.compiler.base"/>
							<tool id="cdt.managedbuild.tool.gnu.c.linker.base.1164085649" name="GCC C Linker" superClass="cdt.managedbuild.tool.gnu.c.linker.base"/>
							<tool id="cdt.managedbuild.tool.gnu.cpp.linker.base.315588649" name="GCC C++ Linker" superClass="cdt.managedbuild.tool.gnu.cpp.linker.base"/>
							<tool id="cdt.managedbuild.tool.gnu.assembler.base.**********" name="GCC Assembler" superClass="cdt.managedbuild.tool.gnu.assembler.base"/>
						</toolChain>
					</folderInfo>
				</configuration>
			</storageModule>
			<storageModule moduleId="org.eclipse.cdt.core.externalSettings"/>
		</cconfiguration>
		<cconfiguration id="org.omnetpp.cdt.gnu.config.release.161839981">
			<storageModule buildSystemId="org.eclipse.cdt.managedbuilder.core.configurationDataProvider" id="org.omnetpp.cdt.gnu.config.release.161839981" moduleId="org.eclipse.cdt.core.settings" name="gcc-release">
				<externalSettings/>
				<extensions>
					<extension id="org.eclipse.cdt.core.GmakeErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.CWDLocator" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GCCErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GASErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GLDErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.MachO64" point="org.eclipse.cdt.core.BinaryParser"/>
					<extension id="org.eclipse.cdt.core.ELF" point="org.eclipse.cdt.core.BinaryParser"/>
					<extension id="org.eclipse.cdt.core.PE" point="org.eclipse.cdt.core.BinaryParser"/>
				</extensions>
			</storageModule>
			<storageModule moduleId="cdtBuildSystem" version="4.0.0">
				<configuration artifactName="${ProjName}" buildProperties="" description="" id="org.omnetpp.cdt.gnu.config.release.161839981" name="gcc-release" parent="org.omnetpp.cdt.gnu.config.release">
					<folderInfo id="org.omnetpp.cdt.gnu.config.release.161839981." name="/" resourcePath="">
						<toolChain id="org.omnetpp.cdt.gnu.toolchain.release.310695423" name="GCC for OMNeT++" superClass="org.omnetpp.cdt.gnu.toolchain.release">
							<targetPlatform binaryParser="org.eclipse.cdt.core.ELF;org.eclipse.cdt.core.MachO64;org.eclipse.cdt.core.PE" id="org.omnetpp.cdt.targetPlatform.1041058034" isAbstract="false" name="Windows, Linux, Mac" osList="win32,linux,macosx" superClass="org.omnetpp.cdt.targetPlatform"/>
							<builder id="org.omnetpp.cdt.gnu.builder.release.295833644" managedBuildOn="true" name="OMNeT++ Make Builder (opp_makemake).gcc-release" superClass="org.omnetpp.cdt.gnu.builder.release"/>
							<tool id="cdt.managedbuild.tool.gnu.archiver.base.422797043" name="GCC Archiver" superClass="cdt.managedbuild.tool.gnu.archiver.base"/>
							<tool id="cdt.managedbuild.tool.gnu.cpp.compiler.base.992758452" name="GCC C++ Compiler" superClass="cdt.managedbuild.tool.gnu.cpp.compiler.base"/>
							<tool id="cdt.managedbuild.tool.gnu.c.compiler.base.1026163649" name="GCC C Compiler" superClass="cdt.managedbuild.tool.gnu.c.compiler.base"/>
							<tool id="cdt.managedbuild.tool.gnu.c.linker.base.1795059267" name="GCC C Linker" superClass="cdt.managedbuild.tool.gnu.c.linker.base"/>
							<tool id="cdt.managedbuild.tool.gnu.cpp.linker.base.2118279827" name="GCC C++ Linker" superClass="cdt.managedbuild.tool.gnu.cpp.linker.base"/>
							<tool id="cdt.managedbuild.tool.gnu.assembler.base.67345438" name="GCC Assembler" superClass="cdt.managedbuild.tool.gnu.assembler.base"/>
						</toolChain>
					</folderInfo>
				</configuration>
			</storageModule>
		</cconfiguration>
	</storageModule>
	<storageModule moduleId="cdtBuildSystem" version="4.0.0">
		<project id="Castalia1304.org.omnetpp.cdt.omnetppProjectType.2102729272" name="OMNeT++ Simulation" projectType="org.omnetpp.cdt.omnetppProjectType"/>
	</storageModule>
	<storageModule moduleId="scannerConfiguration">
		<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId=""/>
		<scannerConfigBuildInfo instanceId="org.omnetpp.cdt.gnu.config.debug.718612962">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="org.omnetpp.cdt.OmnetppGCCPerProjectProfile"/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="org.omnetpp.cdt.gnu.config.release.161839981">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="org.omnetpp.cdt.OmnetppGCCPerProjectProfile"/>
		</scannerConfigBuildInfo>
	</storageModule>
	<storageModule moduleId="org.eclipse.cdt.core.LanguageSettingsProviders"/>
</cproject>
