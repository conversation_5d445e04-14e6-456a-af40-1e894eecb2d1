% 运行修复后的训练奖励对比

function run_fixed_training()
    close all;
    clear;
    clc;
    
    fprintf('=== 运行修复后的训练奖励对比 ===\n');
    
    try
        % 运行训练奖励对比
        training_reward_comparison();
        
        fprintf('\n✓ 训练奖励对比已完成！\n');
        fprintf('请检查生成的图表是否显示非线性学习曲线：\n');
        fprintf('- 初期：快速上升（陡峭）\n');
        fprintf('- 后期：缓慢上升（平缓）\n');
        fprintf('- 性能顺序：分层RL > 演员-评论家 > DQN > 固定功率\n');
        
    catch ME
        fprintf('❌ 运行出错：%s\n', ME.message);
        fprintf('错误位置：%s (第%d行)\n', ME.stack(1).file, ME.stack(1).line);
    end
end
