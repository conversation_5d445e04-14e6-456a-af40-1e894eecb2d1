% 梯度验证测试脚本
% 验证反向传播算法的正确性
function test_gradient_verification()
    fprintf('=== 梯度验证测试 ===\n');
    
    % 测试1: 数值梯度验证
    test_numerical_gradients();
    
    % 测试2: 前向传播一致性
    test_forward_consistency();
    
    % 测试3: 反向传播维度检查
    test_backward_dimensions();
    
    fprintf('所有测试完成!\n');
end

function test_numerical_gradients()
    fprintf('\n测试1: 数值梯度验证\n');
    
    % 创建小型网络进行测试
    input_dim = 4;
    output_dim = 2;
    weights = initialize_test_weights(input_dim, output_dim);
    
    % 随机输入和目标
    input = randn(input_dim, 1);
    target = randn(output_dim, 1);
    
    % 计算解析梯度
    [output, cache] = forward_pass_test(weights, input);
    loss = 0.5 * sum((output - target).^2);
    grad_output = output - target;
    [analytical_grads, ~] = backward_pass_test(weights, cache, grad_output);
    
    % 计算数值梯度
    epsilon = 1e-5;
    numerical_grads = compute_numerical_gradients(weights, input, target, epsilon);
    
    % 比较梯度
    compare_gradients(analytical_grads, numerical_grads);
end

function weights = initialize_test_weights(input_dim, output_dim)
    % 初始化测试权重
    hidden = 8;
    weights = struct();
    weights.W1 = randn(hidden, input_dim) * 0.1;
    weights.b1 = randn(hidden, 1) * 0.1;
    weights.W2 = randn(hidden, hidden) * 0.1;
    weights.b2 = randn(hidden, 1) * 0.1;
    weights.W3 = randn(output_dim, hidden) * 0.1;
    weights.b3 = randn(output_dim, 1) * 0.1;
end

function [output, cache] = forward_pass_test(weights, input)
    % 测试用前向传播
    z1 = weights.W1 * input + weights.b1;
    a1 = max(0, z1); % ReLU
    
    z2 = weights.W2 * a1 + weights.b2;
    a2 = max(0, z2); % ReLU
    
    output = weights.W3 * a2 + weights.b3;
    
    cache = struct('input', input, 'z1', z1, 'a1', a1, 'z2', z2, 'a2', a2);
end

function [grads, grad_input] = backward_pass_test(weights, cache, grad_output)
    % 测试用反向传播
    input = cache.input;
    z1 = cache.z1; a1 = cache.a1;
    z2 = cache.z2; a2 = cache.a2;
    
    % 输出层
    grads = struct();
    grads.W3 = grad_output * a2';
    grads.b3 = grad_output;
    
    % 第二层
    grad_a2 = weights.W3' * grad_output;
    grad_z2 = grad_a2 .* (z2 > 0);
    grads.W2 = grad_z2 * a1';
    grads.b2 = grad_z2;
    
    % 第一层
    grad_a1 = weights.W2' * grad_z2;
    grad_z1 = grad_a1 .* (z1 > 0);
    grads.W1 = grad_z1 * input';
    grads.b1 = grad_z1;
    
    grad_input = weights.W1' * grad_z1;
end

function numerical_grads = compute_numerical_gradients(weights, input, target, epsilon)
    % 计算数值梯度
    numerical_grads = struct();
    
    fields = fieldnames(weights);
    for i = 1:length(fields)
        field = fields{i};
        param = weights.(field);
        grad = zeros(size(param));
        
        for j = 1:numel(param)
            % 正向扰动
            weights_plus = weights;
            weights_plus.(field)(j) = param(j) + epsilon;
            [output_plus, ~] = forward_pass_test(weights_plus, input);
            loss_plus = 0.5 * sum((output_plus - target).^2);
            
            % 负向扰动
            weights_minus = weights;
            weights_minus.(field)(j) = param(j) - epsilon;
            [output_minus, ~] = forward_pass_test(weights_minus, input);
            loss_minus = 0.5 * sum((output_minus - target).^2);
            
            % 数值梯度
            grad(j) = (loss_plus - loss_minus) / (2 * epsilon);
        end
        
        numerical_grads.(field) = grad;
    end
end

function compare_gradients(analytical, numerical)
    % 比较解析梯度和数值梯度
    fields = fieldnames(analytical);
    
    fprintf('梯度比较结果:\n');
    for i = 1:length(fields)
        field = fields{i};
        
        anal_grad = analytical.(field);
        num_grad = numerical.(field);
        
        % 计算相对误差
        diff = abs(anal_grad - num_grad);
        rel_error = diff ./ (abs(anal_grad) + abs(num_grad) + 1e-8);
        max_rel_error = max(rel_error(:));
        
        fprintf('  %s: 最大相对误差 = %.2e', field, max_rel_error);
        if max_rel_error < 1e-5
            fprintf(' ✓ 通过\n');
        else
            fprintf(' ✗ 失败\n');
        end
    end
end

function test_forward_consistency()
    fprintf('\n测试2: 前向传播一致性\n');
    
    % 测试多次前向传播的一致性
    weights = initialize_test_weights(5, 3);
    input = randn(5, 1);
    
    [output1, ~] = forward_pass_test(weights, input);
    [output2, ~] = forward_pass_test(weights, input);
    
    diff = max(abs(output1 - output2));
    fprintf('前向传播一致性误差: %.2e', diff);
    if diff < 1e-12
        fprintf(' ✓ 通过\n');
    else
        fprintf(' ✗ 失败\n');
    end
end

function test_backward_dimensions()
    fprintf('\n测试3: 反向传播维度检查\n');
    
    input_dim = 6;
    output_dim = 4;
    weights = initialize_test_weights(input_dim, output_dim);
    input = randn(input_dim, 1);
    
    [output, cache] = forward_pass_test(weights, input);
    grad_output = randn(output_dim, 1);
    [grads, grad_input] = backward_pass_test(weights, cache, grad_output);
    
    % 检查梯度维度
    fields = fieldnames(weights);
    all_correct = true;
    
    for i = 1:length(fields)
        field = fields{i};
        if ~isequal(size(grads.(field)), size(weights.(field)))
            fprintf('维度错误: %s\n', field);
            all_correct = false;
        end
    end
    
    if ~isequal(size(grad_input), size(input))
        fprintf('输入梯度维度错误\n');
        all_correct = false;
    end
    
    if all_correct
        fprintf('所有梯度维度正确 ✓ 通过\n');
    else
        fprintf('存在维度错误 ✗ 失败\n');
    end
end
