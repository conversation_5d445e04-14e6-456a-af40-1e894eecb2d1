1，电脑有两个Castalia-3.2文件夹，一个位于这个文件夹中。这是从github上解压得到的，但是由于路径中存在中文，所以我复制了一个到C盘中（C:\Castalia-3.2），因此在omnetpp命令行中执行模拟是在C:\Castalia-3.2中模拟，需要修改文件夹中的文件时也是在该文件夹中修改（不过最好两个都修改，保持一致）。
2，C:\Users\<USER>\Desktop\wban-pathloss-master\hi_pathloss中的pl_tx_times可能存在问题，拷贝一份作为原件，以备复原。
3，当前遇到的问题：一，Castalia-3.2无法对13_04_pl.txt和13_04_pl_tx.txt进行模拟（时间序列没有单调递增）。已解决
                                  二，用什么参数作为性能改进的指标？ 能耗衡量指标怎么生成？答：1，PDR作为指标之一，高的PDR表示少的重传次数，也节省了能量。2，节点每发送1kb的数据消耗多少能量。
                                  三，生成.vec文件是否必要，这个文件反映了什么？.vec文件已生成，但内容是错误的，并没有记录RSSI值。
                                  四，原论文其他图像如何生成，是否必要？
                                  五，如何在omnetpp的IDE中打开.sca文件？ 已解决，同打开.elog文件的方法
4，原文核心研究：在保持低功耗和干扰的情况下提高PDR，相对来讲也是一种节能角度。
      我的研究方向：在原文研究基础上进一步利用新方法进一步降低功耗或者在保持低功耗的同时继续提高PDR。
5，执行run_all_reproductions时先需要重启Matlab。
