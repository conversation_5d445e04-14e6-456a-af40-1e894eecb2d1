% 运行四种算法训练奖励对比分析的测试脚本

function run_training_reward_analysis()
    close all;
    clear;
    clc;
    
    fprintf('=== 开始运行四种算法训练奖励对比分析 ===\n');
    fprintf('这将生成四种算法在200轮训练过程中的平均累计奖励变化图表\n\n');
    
    % 记录开始时间
    start_time = tic;
    
    try
        % 运行主要分析函数
        training_reward_comparison();
        
        % 计算运行时间
        elapsed_time = toc(start_time);
        
        fprintf('\n=== 分析完成 ===\n');
        fprintf('总运行时间: %.2f 秒\n', elapsed_time);
        fprintf('\n生成的文件:\n');
        fprintf('1. training_reward_comparison.png - 训练奖励对比图表\n');
        fprintf('2. training_reward_comparison_results.mat - 训练数据\n');
        fprintf('3. training_reward_analysis_report.csv - 训练分析报告\n');
        
        % 显示图表
        fprintf('\n图表已显示，请查看训练奖励变化曲线。\n');
        
    catch ME
        fprintf('运行过程中出现错误:\n');
        fprintf('错误信息: %s\n', ME.message);
        fprintf('错误位置: %s (第 %d 行)\n', ME.stack(1).name, ME.stack(1).line);
    end
end
