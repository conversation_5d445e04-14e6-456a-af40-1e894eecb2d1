# 修复后的HIchan配置文件 - 解决RSSI为0问题
[General]

include ../Parameters/Castalia.ini

**.result-recording-modes = all
output-vector-file = ${resultdir}/${configname}-${runnumber}.vec
output-scalar-file = ${resultdir}/${configname}-${runnumber}.sca

sim-time-limit = 120s
record-eventlog = true

SN.numNodes = 2

# 无线信道配置
SN.wirelessChannel.pathLossFile = "../Parameters/WirelessChannel/BANmodels/13_04_pl.txt"
SN.wirelessChannel.collectTraceInfo = true  # 启用调试
SN.wirelessChannel.sigma = 4.0  # 阴影衰落标准差

# Radio配置 - 修复RSSI问题
SN.node[*].Communication.Radio.RadioParametersFile = "../Parameters/Radio/nRF51822.txt"
SN.node[*].Communication.Radio.symbolsForRSSI = 64  # 增加RSSI积分符号数
SN.node[*].Communication.Radio.TxOutputPower = "0dBm"  # 适中的发射功率
SN.node[*].Communication.Radio.collectTraceInfo = true  # 启用Radio调试
SN.node[*].Communication.Radio.carrierSenseInterruptEnabled = true  # 启用载波侦听
SN.node[*].Communication.Radio.CCAthreshold = -85  # 设置CCA阈值

# MAC和应用层配置
SN.node[*].Communication.MAC.collectTraceInfo = true
SN.node[*].Application.collectTraceInfo = true

# 应用程序配置
SN.node[*].ApplicationName = "ThroughputTestWalking"
SN.node[0].Application.txFile = ""
SN.node[*].Application.txFile = "../Parameters/WirelessChannel/BANmodels/13_04_pl_tx.txt"
SN.node[0].Application.nextRecipient = "0"
SN.node[1].Application.packet_rate = 5  # 降低发包率以便观察
SN.node[1].Application.nextRecipient = "0"

# 节点位置配置 - 确保合理距离
SN.node[0].xCoor = 0
SN.node[0].yCoor = 0
SN.node[0].zCoor = 0
SN.node[1].xCoor = 1  # 1米距离
SN.node[1].yCoor = 0
SN.node[1].zCoor = 0

[Config TMAC]
SN.node[*].Communication.MACProtocolName = "TMAC"
SN.node[*].Communication.MAC.phyDataRate = 1024
