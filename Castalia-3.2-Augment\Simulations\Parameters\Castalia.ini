# ****************************************************************************
# *  Copyright: National ICT Australia,  2007 - 2010                         *
# *  Developed at the ATP lab, Networked Systems research theme              *
# *  Author(s): <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>                        *
# *  This file is distributed under the terms in the attached LICENSE file.  *
# *  If you do not find this file, copies can be found by writing to:        *
# *                                                                          *
# *      NICTA, Locked Bag 9013, Alexandria, NSW 1435, Australia             *
# *      Attention:  License Inquiry.                                        *
# *                                                                          *
# ***************************************************************************/

# ===========================================================
# OMNet parameters related to cmd line execution 
# (reporting and progress monitoring)
# usually you will not need to change these parameters as
# Castalia uses its own reporting and debuging mechanisms
# ===========================================================

cmdenv-express-mode = true
cmdenv-module-messages = true
cmdenv-event-banners = false				
cmdenv-performance-display = false
cmdenv-interactive = false

ned-path = ../../src

network = SN  	# this line is for Cmdenv

#output-vector-file = Castalia-statistics.vec
#output-scalar-file = Castalia-statistics.sca

# 11 random number streams (or generators as OMNeT calls them)
num-rngs = 11 

# ===========================================================
# Map the 11 RNGs streams with the various module RNGs. 
# ==========================================================

SN.wirelessChannel.rng-0 		= 1    	# used to produce the random shadowing effects
SN.wirelessChannel.rng-2 		= 9	# used in temporal model
                                    
SN.node[*].Application.rng-0		= 3	# Randomizes the start time of the application
SN.node[*].Communication.Radio.rng-0	= 2	# used to decide if a receiver, with X probability.
						# to receive a packet, will indeed receive it

SN.node[*].Communication.MAC.rng-0	= 4	# Produces values compared against txProb
SN.node[*].Communication.MAC.rng-1	= 5	# Produces values between [0 ....  randomTxOffset]

SN.node[*].ResourceManager.rng-0	= 6	# Produces values of the clock drift of the CPU of each node
SN.node[*].SensorManager.rng-0	 	= 7	# Produces values of the sensor devices' bias
SN.node[*].SensorManager.rng-1	 	= 8	# Produces values of the sensor devices' noise

SN.physicalProcess[*].rng-0 		= 10	# currently used only in CarsPhysicalProcess

SN.node[*].MobilityManager.rng-0	= 0	# used to randomly place the nodes
