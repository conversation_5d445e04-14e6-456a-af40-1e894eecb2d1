% 快速版本：实现论文中的三个伪代码算法
% 优化版本，减少数据量，提高执行速度

close all
clear all

fprintf('=== 快速版本：论文算法实现演示 ===\n');

%% 算法1：周期性运动的调度算法 (简化快速版本)
function tx_times = algorithm1_scheduling_fast(imu_data, time_vector)
    fprintf('执行算法1：周期性运动的调度算法（快速版本）\n');
    
    % 简化的峰值检测
    [pks, locs] = findpeaks(imu_data, 'MinPeakDistance', 50, 'MinPeakProminence', 0.5);
    
    tx_times = [];
    alpha = 0.344;  % 经验参数
    
    % 生成传输时机
    for i = 2:length(locs)-1
        if i > 2
            t_current = time_vector(locs(i));
            t_prev = time_vector(locs(i-1));
            tx_time = t_current + alpha * (t_current - t_prev);
            
            if tx_time <= max(time_vector)
                tx_times = [tx_times; tx_time];
            end
        end
    end
    
    fprintf('算法1完成，生成%d个传输时机\n', length(tx_times));
end

%% 算法2：基于肌电控制的TPC算法 (简化快速版本)
function P_Tx = algorithm2_emg_tpc_fast(emg_data, emg_threshold)
    fprintf('执行算法2：基于肌电控制的TPC算法（快速版本）\n');
    
    P_Tx_l = -8;  % 低功率
    P_Tx_h = 4;   % 高功率
    P_Tx = zeros(size(emg_data));
    
    window_size = 100;  % 100个样本窗口
    
    for i = 1:window_size:length(emg_data)
        end_idx = min(i + window_size - 1, length(emg_data));
        window_data = emg_data(i:end_idx);
        
        % 计算窗口内的EMG范围
        emg_range = max(window_data) - min(window_data);
        
        % 根据阈值设置功率
        if emg_range > emg_threshold
            P_Tx(i:end_idx) = P_Tx_h;
        else
            P_Tx(i:end_idx) = P_Tx_l;
        end
    end
    
    fprintf('算法2完成\n');
end

%% 算法3：基于心率控制的TPC算法 (简化快速版本)
function P_Tx = algorithm3_hr_tpc_fast(hr_data, hr_threshold)
    fprintf('执行算法3：基于心率控制的TPC算法（快速版本）\n');
    
    P_Tx_l = -8;  % 低功率
    P_Tx_h = 4;   % 高功率
    P_Tx = zeros(size(hr_data));
    
    update_interval = 300;  % 300个样本更新间隔（对应3秒@100Hz）
    
    for i = 1:update_interval:length(hr_data)
        end_idx = min(i + update_interval - 1, length(hr_data));
        
        % 计算当前窗口的平均心率
        avg_hr = mean(hr_data(i:end_idx));
        
        % 根据心率阈值设置功率
        if avg_hr > hr_threshold
            P_Tx(i:end_idx) = P_Tx_h;
        else
            P_Tx(i:end_idx) = P_Tx_l;
        end
    end
    
    fprintf('算法3完成\n');
end

%% 主函数：快速演示
function main_demo_fast()
    fprintf('开始快速演示...\n');
    
    % 生成较小的模拟数据集（减少计算时间）
    time_duration = 5;  % 5秒（而不是10秒）
    fs = 100;  % 100Hz采样率（而不是1kHz）
    t = 0:1/fs:time_duration;
    
    fprintf('生成模拟数据，时长：%.1f秒，采样率：%dHz\n', time_duration, fs);
    
    % 模拟IMU数据
    imu_data = 2*sin(2*pi*0.5*t) + 0.5*randn(size(t));
    
    % 模拟心率数据（简化）
    base_hr = 70;
    hr_variation = 10*sin(2*pi*0.1*t);
    hr_data = base_hr + hr_variation + 2*randn(size(t));
    
    % 模拟EMG数据
    emg_data = abs(randn(size(t))) * 30 + 20;
    
    fprintf('数据生成完成，开始执行算法...\n');
    
    % 执行算法1
    tx_times = algorithm1_scheduling_fast(imu_data, t);
    
    % 执行算法2
    emg_threshold = 25;
    power_emg = algorithm2_emg_tpc_fast(emg_data, emg_threshold);
    
    % 执行算法3
    hr_threshold = 75;
    power_hr = algorithm3_hr_tpc_fast(hr_data, hr_threshold);
    
    fprintf('所有算法执行完成，开始绘图...\n');
    
    % 绘制结果
    figure('Position', [100, 100, 1000, 800]);
    
    % IMU数据和传输时机
    subplot(4,1,1);
    plot(t, imu_data, 'b-', 'LineWidth', 1.5);
    hold on;
    for i = 1:length(tx_times)
        if tx_times(i) <= max(t)
            xline(tx_times(i), 'r--', 'LineWidth', 2);
        end
    end
    ylabel('IMU加速度');
    title('算法1：基于IMU的传输调度');
    grid on;
    legend('IMU数据', '传输时机', 'Location', 'best');
    
    % 心率数据
    subplot(4,1,2);
    plot(t, hr_data, 'r-', 'LineWidth', 1.5);
    hold on;
    yline(hr_threshold, 'k--', 'LineWidth', 2);
    ylabel('心率 (BPM)');
    title('算法3：心率数据');
    legend('心率', '阈值', 'Location', 'best');
    grid on;
    
    % 心率功率控制
    subplot(4,1,3);
    plot(t, power_hr, 'b-', 'LineWidth', 2);
    ylabel('传输功率 (dBm)');
    title('基于心率的功率控制');
    ylim([-10, 6]);
    grid on;
    
    % EMG数据和功率控制
    subplot(4,1,4);
    yyaxis left;
    plot(t, emg_data, 'g-', 'LineWidth', 1.5);
    ylabel('EMG信号 (mV)');
    
    yyaxis right;
    plot(t, power_emg, 'm-', 'LineWidth', 2);
    ylabel('传输功率 (dBm)');
    xlabel('时间 (s)');
    title('算法2：基于肌电的功率控制');
    grid on;
    
    fprintf('绘图完成！\n');
    fprintf('算法演示成功完成！\n');
    fprintf('检测到的传输时机数量: %d\n', length(tx_times));
    
    % 显示算法性能统计
    fprintf('\n=== 算法性能统计 ===\n');
    fprintf('心率功率调整次数: %d\n', sum(diff(power_hr) ~= 0));
    fprintf('肌电功率调整次数: %d\n', sum(diff(power_emg) ~= 0));
    fprintf('平均心率: %.1f BPM\n', mean(hr_data));
    fprintf('平均EMG信号: %.1f mV\n', mean(emg_data));
end

% 运行快速演示
fprintf('开始运行快速版本算法实现...\n');
tic;
main_demo_fast();
execution_time = toc;
fprintf('总执行时间: %.2f 秒\n', execution_time);
