% WBAN场景下的梯度训练验证
% 验证梯度反向传播在实际WBAN功率控制中的效果
function test_wban_gradient_training()
    fprintf('=== WBAN梯度训练验证 ===\n');
    
    % 创建WBAN环境
    env = create_wban_environment();
    
    % 创建分层智能体
    agent = create_hierarchical_agent(env);
    
    % 训练参数
    params = struct();
    params.batch_size = 32;
    params.learning_rate = 0.001;
    params.num_episodes = 100;
    params.max_steps = 200;
    
    % 执行训练
    results = train_wban_agent(agent, env, params);
    
    % 分析结果
    analyze_training_results(results);
    
    % 测试训练后的性能
    test_trained_performance(agent, env);
    
    fprintf('WBAN梯度训练验证完成!\n');
end

function env = create_wban_environment()
    % 创建WBAN环境
    env = struct();
    env.state_dim = 12; % 生物信号 + 信道状态
    env.action_dim = 6;  % 6个功率等级
    env.power_levels = [-20, -15, -10, -5, 0, 4]; % dBm
    
    % 生成模拟的生物信号数据
    duration = 60; % 60秒
    fs = 10; % 10Hz采样率
    time = 0:1/fs:duration;
    
    % ECG信号 (心率)
    base_hr = 80;
    env.ecg_data = base_hr + 20*sin(2*pi*0.05*time) + 5*randn(size(time));
    
    % EMG信号 (肌电)
    env.emg_data = abs(sin(2*pi*0.1*time)) * 50 + 10*randn(size(time));
    
    % IMU信号 (加速度)
    env.imu_data = 2*sin(2*pi*1.2*time) + 0.5*randn(size(time));
    
    % RSSI信号 (信号强度)
    env.rssi_data = -60 + 10*sin(2*pi*0.2*time) + 5*randn(size(time));
    
    env.time_vector = time;
    env.current_step = 1;
    
    fprintf('WBAN环境创建完成 (状态维度: %d, 动作维度: %d)\n', ...
            env.state_dim, env.action_dim);
end

function agent = create_hierarchical_agent(env)
    % 创建分层智能体
    agent = struct();
    agent.state_dim = env.state_dim;
    agent.action_dim = env.action_dim;
    agent.meta_dim = 4; % 上层策略维度
    agent.gamma = 0.9;
    agent.learning_rate = 0.001;
    
    % 初始化网络
    agent.upper_weights = initialize_network_weights(env.state_dim, agent.meta_dim);
    agent.lower_weights = initialize_network_weights(env.state_dim + agent.meta_dim, env.action_dim);
    
    % 经验回放
    agent.memory = {};
    agent.max_memory = 10000;
    
    % 训练统计
    agent.episode_losses = [];
    agent.episode_rewards = [];
    
    fprintf('分层智能体创建完成\n');
end

function weights = initialize_network_weights(input_dim, output_dim)
    % 初始化网络权重
    hidden1 = 64;
    hidden2 = 32;
    
    weights = struct();
    weights.W1 = randn(hidden1, input_dim) * sqrt(2/input_dim);
    weights.b1 = zeros(hidden1, 1);
    weights.W2 = randn(hidden2, hidden1) * sqrt(2/hidden1);
    weights.b2 = zeros(hidden2, 1);
    weights.W3 = randn(output_dim, hidden2) * sqrt(2/hidden2);
    weights.b3 = zeros(output_dim, 1);
end

function results = train_wban_agent(agent, env, params)
    % 训练WBAN智能体
    fprintf('开始训练WBAN智能体...\n');
    
    results = struct();
    results.episode_rewards = [];
    results.episode_losses = [];
    results.energy_consumption = [];
    results.pdr_values = [];
    
    for episode = 1:params.num_episodes
        % 重置环境
        state = reset_environment(env);
        episode_reward = 0;
        episode_energy = 0;
        successful_transmissions = 0;
        total_transmissions = 0;
        
        for step = 1:params.max_steps
            % 选择动作
            action = select_action(agent, state);
            
            % 执行动作
            [next_state, reward, done, info] = step_environment(env, action);
            
            % 存储经验
            experience = struct('state', state, 'action', action, ...
                              'reward', reward, 'next_state', next_state, 'done', done);
            store_experience(agent, experience);
            
            % 更新统计
            episode_reward = episode_reward + reward;
            episode_energy = episode_energy + info.energy;
            if info.transmission_success
                successful_transmissions = successful_transmissions + 1;
            end
            total_transmissions = total_transmissions + 1;
            
            state = next_state;
            
            if done
                break;
            end
        end
        
        % 训练网络
        if length(agent.memory) >= params.batch_size
            loss = update_agent_gradients(agent, params);
            agent.episode_losses(end+1) = loss;
        end
        
        % 记录结果
        agent.episode_rewards(end+1) = episode_reward;
        results.episode_rewards(end+1) = episode_reward;
        results.energy_consumption(end+1) = episode_energy;
        results.pdr_values(end+1) = successful_transmissions / total_transmissions;
        
        if mod(episode, 10) == 0
            fprintf('Episode %d: Reward=%.2f, Energy=%.2f, PDR=%.3f\n', ...
                    episode, episode_reward, episode_energy, ...
                    successful_transmissions / total_transmissions);
        end
    end
    
    fprintf('训练完成!\n');
end

function state = reset_environment(env)
    % 重置环境状态
    env.current_step = 1;
    state = get_current_state(env);
end

function state = get_current_state(env)
    % 获取当前状态
    step = min(env.current_step, length(env.time_vector));
    
    % 构建状态向量 [ECG, EMG, IMU, RSSI, 时间特征, 历史特征]
    state = [
        env.ecg_data(step);           % 心率
        env.emg_data(step);           % 肌电
        env.imu_data(step);           % 加速度
        env.rssi_data(step);          % 信号强度
        sin(2*pi*step/100);           % 时间周期特征
        cos(2*pi*step/100);           % 时间周期特征
        step/length(env.time_vector); % 归一化时间
        0.5;                          % 缓冲区占用率
        0.8;                          % 当前PDR
        20.0;                         % 当前延迟
        0.6;                          % 网络负载
        1.0                           % 电池电量
    ];
end

function action = select_action(agent, state)
    % 选择动作 (贪婪策略用于训练)
    [upper_out, ~] = forward_pass_wban(agent.upper_weights, state);
    policy_weights = softmax_wban(upper_out);
    
    lower_input = [state; policy_weights];
    [q_values, ~] = forward_pass_wban(agent.lower_weights, lower_input);
    
    [~, action] = max(q_values);
end

function [next_state, reward, done, info] = step_environment(env, action)
    % 环境步进
    env.current_step = env.current_step + 1;
    
    % 计算奖励 (能耗 + PDR + 延迟)
    power_dbm = env.power_levels(action);
    energy = 10^(power_dbm/10); % mW
    
    % 模拟PDR (功率越高PDR越好)
    pdr = 0.5 + 0.4 * (action / env.action_dim);
    
    % 模拟延迟 (功率越高延迟越低)
    delay = 50 - 5 * (action - 1);
    
    % 多目标奖励函数
    energy_reward = -energy * 0.01; % 能耗惩罚
    pdr_reward = pdr * 10;          % PDR奖励
    delay_reward = -delay * 0.1;    % 延迟惩罚
    
    reward = energy_reward + pdr_reward + delay_reward;
    
    % 下一个状态
    next_state = get_current_state(env);
    
    % 结束条件
    done = env.current_step >= length(env.time_vector);
    
    % 信息
    info = struct('energy', energy, 'pdr', pdr, 'delay', delay, ...
                  'transmission_success', rand() < pdr);
end

function store_experience(agent, experience)
    % 存储经验
    agent.memory{end+1} = experience;
    
    % 限制内存大小
    if length(agent.memory) > agent.max_memory
        agent.memory(1) = [];
    end
end

function loss = update_agent_gradients(agent, params)
    % 使用梯度更新智能体
    
    % 采样批次
    batch_size = min(params.batch_size, length(agent.memory));
    indices = randperm(length(agent.memory), batch_size);
    
    total_loss = 0;
    
    % 初始化梯度
    grad_upper = zero_like_weights_wban(agent.upper_weights);
    grad_lower = zero_like_weights_wban(agent.lower_weights);
    
    for i = 1:batch_size
        exp = agent.memory{indices(i)};
        
        % 前向传播
        [upper_out, cache_up] = forward_pass_wban(agent.upper_weights, exp.state);
        policy_weights = softmax_wban(upper_out);
        lower_input = [exp.state; policy_weights];
        [q_values, cache_low] = forward_pass_wban(agent.lower_weights, lower_input);
        
        % 计算目标Q值
        if exp.done
            target_q = exp.reward;
        else
            next_action = select_action(agent, exp.next_state);
            [next_upper, ~] = forward_pass_wban(agent.upper_weights, exp.next_state);
            next_policy = softmax_wban(next_upper);
            next_lower_input = [exp.next_state; next_policy];
            [next_q_values, ~] = forward_pass_wban(agent.lower_weights, next_lower_input);
            target_q = exp.reward + agent.gamma * max(next_q_values);
        end
        
        % 计算损失和梯度
        q_pred = q_values(exp.action);
        td_error = q_pred - target_q;
        total_loss = total_loss + td_error^2;
        
        % 反向传播 (简化版本)
        grad_q = zeros(size(q_values));
        grad_q(exp.action) = 2 * td_error;
        
        % 更新下层网络
        [grad_low, grad_lower_input] = backward_pass_wban(agent.lower_weights, cache_low, grad_q);
        grad_lower = accumulate_grads_wban(grad_lower, grad_low);
        
        % 更新上层网络 (通过policy_weights梯度)
        grad_policy = grad_lower_input(end-agent.meta_dim+1:end);
        grad_upper_out = compute_softmax_gradient(policy_weights, grad_policy);
        [grad_up, ~] = backward_pass_wban(agent.upper_weights, cache_up, grad_upper_out);
        grad_upper = accumulate_grads_wban(grad_upper, grad_up);
    end
    
    % 应用梯度
    lr = agent.learning_rate;
    agent.upper_weights = apply_gradients_wban(agent.upper_weights, grad_upper, lr / batch_size);
    agent.lower_weights = apply_gradients_wban(agent.lower_weights, grad_lower, lr / batch_size);
    
    loss = total_loss / batch_size;
end

% ========== 辅助函数 ==========

function [output, cache] = forward_pass_wban(weights, input)
    % WBAN前向传播
    if size(input, 2) > size(input, 1)
        input = input';
    end

    z1 = weights.W1 * input + weights.b1;
    a1 = max(0, z1); % ReLU

    z2 = weights.W2 * a1 + weights.b2;
    a2 = max(0, z2); % ReLU

    output = weights.W3 * a2 + weights.b3;

    if nargout > 1
        cache = struct('input', input, 'z1', z1, 'a1', a1, 'z2', z2, 'a2', a2);
    end
end

function [grads, grad_input] = backward_pass_wban(weights, cache, grad_output)
    % WBAN反向传播
    input = cache.input;
    z1 = cache.z1; a1 = cache.a1;
    z2 = cache.z2; a2 = cache.a2;

    grads = struct();
    grads.W3 = grad_output * a2';
    grads.b3 = grad_output;

    grad_a2 = weights.W3' * grad_output;
    grad_z2 = grad_a2 .* (z2 > 0);
    grads.W2 = grad_z2 * a1';
    grads.b2 = grad_z2;

    grad_a1 = weights.W2' * grad_z2;
    grad_z1 = grad_a1 .* (z1 > 0);
    grads.W1 = grad_z1 * input';
    grads.b1 = grad_z1;

    grad_input = weights.W1' * grad_z1;
end

function y = softmax_wban(x)
    % Softmax函数
    exp_x = exp(x - max(x));
    y = exp_x / sum(exp_x);
end

function grad_x = compute_softmax_gradient(softmax_output, grad_softmax)
    % 计算softmax的梯度
    S = softmax_output;
    grad_x = S .* grad_softmax - S .* (S' * grad_softmax);
end

function grads = zero_like_weights_wban(weights)
    % 创建零梯度
    grads = struct();
    fields = fieldnames(weights);
    for i = 1:length(fields)
        grads.(fields{i}) = zeros(size(weights.(fields{i})));
    end
end

function dst = accumulate_grads_wban(dst, src)
    % 累积梯度
    fields = fieldnames(dst);
    for i = 1:length(fields)
        dst.(fields{i}) = dst.(fields{i}) + src.(fields{i});
    end
end

function weights = apply_gradients_wban(weights, grads, lr)
    % 应用梯度
    fields = fieldnames(weights);
    for i = 1:length(fields)
        weights.(fields{i}) = weights.(fields{i}) - lr * grads.(fields{i});
    end
end

function analyze_training_results(results)
    % 分析训练结果
    fprintf('\n=== 训练结果分析 ===\n');

    if ~isempty(results.episode_rewards)
        fprintf('平均奖励: %.2f\n', mean(results.episode_rewards));
        fprintf('最终奖励: %.2f\n', results.episode_rewards(end));
        fprintf('奖励改进: %.2f%%\n', ...
                (results.episode_rewards(end) - results.episode_rewards(1)) / ...
                abs(results.episode_rewards(1)) * 100);
    end

    if ~isempty(results.energy_consumption)
        fprintf('平均能耗: %.2f mW\n', mean(results.energy_consumption));
        fprintf('最终能耗: %.2f mW\n', results.energy_consumption(end));
    end

    if ~isempty(results.pdr_values)
        fprintf('平均PDR: %.3f\n', mean(results.pdr_values));
        fprintf('最终PDR: %.3f\n', results.pdr_values(end));
    end

    % 绘制训练曲线
    plot_training_curves(results);
end

function plot_training_curves(results)
    % 绘制训练曲线
    figure('Name', 'WBAN训练结果', 'Position', [100, 100, 1200, 400]);

    subplot(1, 3, 1);
    if ~isempty(results.episode_rewards)
        plot(results.episode_rewards, 'b-', 'LineWidth', 2);
        title('训练奖励');
        xlabel('Episode');
        ylabel('Reward');
        grid on;
    end

    subplot(1, 3, 2);
    if ~isempty(results.energy_consumption)
        plot(results.energy_consumption, 'r-', 'LineWidth', 2);
        title('能耗变化');
        xlabel('Episode');
        ylabel('Energy (mW)');
        grid on;
    end

    subplot(1, 3, 3);
    if ~isempty(results.pdr_values)
        plot(results.pdr_values, 'g-', 'LineWidth', 2);
        title('PDR变化');
        xlabel('Episode');
        ylabel('PDR');
        grid on;
    end
end

function test_trained_performance(agent, env)
    % 测试训练后的性能
    fprintf('\n=== 性能测试 ===\n');

    % 重置环境
    state = reset_environment(env);
    total_reward = 0;
    total_energy = 0;
    successful_tx = 0;
    total_tx = 0;

    for step = 1:100
        action = select_action(agent, state);
        [next_state, reward, done, info] = step_environment(env, action);

        total_reward = total_reward + reward;
        total_energy = total_energy + info.energy;
        if info.transmission_success
            successful_tx = successful_tx + 1;
        end
        total_tx = total_tx + 1;

        state = next_state;
        if done
            break;
        end
    end

    fprintf('测试结果:\n');
    fprintf('  总奖励: %.2f\n', total_reward);
    fprintf('  平均能耗: %.2f mW\n', total_energy / total_tx);
    fprintf('  PDR: %.3f\n', successful_tx / total_tx);
    fprintf('  成功传输: %d/%d\n', successful_tx, total_tx);
end
