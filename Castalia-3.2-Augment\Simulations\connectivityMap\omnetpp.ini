# ********************************************************************************
# *  Copyright: National ICT Australia,  2007 - 2010                             *
# *  Developed at the ATP lab, Networked Systems research theme                  *
# *  Author(s): <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>  *
# *  This file is distributed under the terms in the attached LICENSE file.      *
# *  If you do not find this file, copies can be found by writing to:            *
# *                                                                              *
# *      NICTA, Locked Bag 9013, Alexandria, NSW 1435, Australia                 *
# *      Attention:  License Inquiry.                                            *
# *                                                                              *
# *******************************************************************************/

[General]

# ==========================================================
# Always include the main Castalia.ini file
# ==========================================================

include ../Parameters/Castalia.ini

sim-time-limit = 100s

SN.field_x = 30					# meters
SN.field_y = 30					# meters

# Specifying number of nodes and their deployment
SN.numNodes = 9
SN.deployment = "3x3"

# Removing variability from wireless channel
SN.wirelessChannel.bidirectionalSigma = 0
SN.wirelessChannel.sigma = 0

# Select a Radio and a default Tx power
SN.node[*].Communication.Radio.RadioParametersFile = "../Parameters/Radio/CC2420.txt"
SN.node[*].Communication.Radio.TxOutputPower = "-5dBm"

# Using connectivity map application module with default parameters
SN.node[*].ApplicationName = "ConnectivityMap"

[Config varyTxPower]
SN.node[*].Communication.Radio.TxOutputPower = ${TXpower="0dBm","-1dBm","-3dBm","-5dBm"}

[Config varySigma]
SN.wirelessChannel.sigma = ${Sigma=0,1,3,5}
