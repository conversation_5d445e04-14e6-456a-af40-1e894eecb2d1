% 强化学习训练管道
% 实现分层强化学习的完整训练流程
function rl_training_pipeline()
    close all;
    clear;
    clc;
    
    fprintf('=== 基于深度强化学习的WBAN功率控制训练 ===\n');
    
    % 设置随机种子以确保可重现性
    rng(42);
    
    % 初始化环境和智能体
    fprintf('初始化环境和智能体...\n');
    env = rl_environment();
    env.initialize_environment();
    agent = improved_hierarchical_agent(env);
    
    % 训练参数
    num_episodes = 200;
    max_steps_per_episode = 300;
    
    % 性能记录
    episode_rewards = zeros(num_episodes, 1);
    episode_energy = zeros(num_episodes, 1);
    episode_pdr = zeros(num_episodes, 1);
    episode_delay = zeros(num_episodes, 1);
    
    % 基准算法性能 (用于对比)
    baseline_performance = run_baseline_algorithms(env);
    
    fprintf('开始训练...\n');
    
    % 主训练循环
    for episode = 1:num_episodes
        fprintf('Episode %d/%d\n', episode, num_episodes);
        
        % 重置环境
        state = env.reset();
        episode_reward = 0;
        
        % 每轮训练
        for step = 1:max_steps_per_episode
            % 上层智能体选择策略
            meta_action = agent.select_meta_action(state);

            % 下层智能体选择具体动作
            local_state = env.get_local_state(meta_action);
            action = agent.select_local_action(local_state);

            % 执行动作
            [next_state, reward, done, info] = env.step(action);

            % 更新智能体
            agent.update_meta_q_table(reward);
            agent.update_local_q_table(reward);

            % 更新状态和奖励
            state = next_state;
            episode_reward = episode_reward + reward;

            if done
                break;
            end
        end
        
        % 记录性能
        env_info = env.get_environment_info();
        episode_rewards(episode) = episode_reward;
        episode_energy(episode) = env_info.total_energy;
        episode_pdr(episode) = env_info.average_pdr;
        episode_delay(episode) = env_info.average_delay;
        
        % 回合结束处理
        env_info = env.get_environment_info();
        agent.episode_end(episode_reward, env_info.total_energy, env_info.average_pdr);
        
        % 打印进度
        if mod(episode, 20) == 0
            fprintf('Episode %d: Reward=%.2f, Energy=%.2f, PDR=%.3f, Delay=%.1f\n', ...
                   episode, episode_reward, env_info.total_energy, ...
                   env_info.average_pdr, env_info.average_delay);
        end
    end
    
    fprintf('训练完成！\n');
    
    % 评估最终性能
    final_performance = evaluate_trained_agent(env, agent);
    
    % 绘制训练结果
    plot_training_results(episode_rewards, episode_energy, episode_pdr, episode_delay, ...
                         baseline_performance, final_performance);
    
    % 保存训练结果
    save_training_results(agent, episode_rewards, episode_energy, episode_pdr, episode_delay, ...
                         baseline_performance, final_performance);
    
    fprintf('结果已保存！\n');
end

function baseline_performance = run_baseline_algorithms(env)
    % 运行基准算法获得对比性能
    fprintf('运行基准算法...\n');
    
    baseline_performance = struct();
    
    % 1. 固定功率算法
    baseline_performance.fixed_power = test_fixed_power_algorithm(env);
    
    % 2. 路径损耗阈值算法 (替代EMG-TPC)
    baseline_performance.pathloss_tpc = test_emg_tpc_algorithm(env);

    % 3. RSSI自适应算法 (替代HR-TPC)
    baseline_performance.rssi_tpc = test_hr_tpc_algorithm(env);
    
    % 4. 简单DQN算法
    baseline_performance.simple_dqn = test_simple_dqn_algorithm(env);
    
    fprintf('基准算法测试完成\n');
end

function performance = test_fixed_power_algorithm(env)
    % 测试固定功率算法
    state = env.reset();
    total_reward = 0;
    
    % 使用中等功率等级 (0dBm)
    fixed_action = 5; % 对应0dBm
    
    for step = 1:300
        [next_state, reward, done, info] = env.step(fixed_action);
        total_reward = total_reward + reward;
        state = next_state;
        
        if done
            break;
        end
    end
    
    env_info = env.get_environment_info();
    performance = struct('reward', total_reward, 'energy', env_info.total_energy, ...
                        'pdr', env_info.average_pdr, 'delay', env_info.average_delay);
end

function performance = test_emg_tpc_algorithm(env)
    % 测试基于路径损耗变化的TPC算法 (替代EMG-TPC)
    state = env.reset();
    total_reward = 0;

    % 路径损耗变化历史
    pl_history = [];

    for step = 1:300
        % 基于路径损耗变化选择功率 (模拟EMG-TPC的逻辑)
        if step <= length(env.pathloss_data)
            current_pl = env.pathloss_data(step);
        else
            current_pl = env.pathloss_data(end);
        end

        pl_history = [pl_history, current_pl];

        % 动态阈值调整
        if length(pl_history) > 10
            pl_mean = mean(pl_history(end-9:end));
            pl_std = std(pl_history(end-9:end));
            high_threshold = pl_mean + 0.5 * pl_std;
            low_threshold = pl_mean - 0.5 * pl_std;
        else
            high_threshold = -70;
            low_threshold = -80;
        end

        % 基于路径损耗变化的功率选择
        if current_pl > high_threshold
            action = 5; % 中高功率 0dBm
        elseif current_pl > low_threshold
            action = 3; % 中功率 -10dBm
        else
            action = 2; % 低功率 -15dBm
        end

        % 添加信道自适应
        if step > 1
            current_rssi = env.rssi_data(min(step, length(env.rssi_data)));
            if current_rssi < -85 % 信道质量差时提高功率
                action = min(6, action + 1);
            elseif current_rssi > -70 % 信道质量好时降低功率
                action = max(1, action - 1);
            end
        end

        [next_state, reward, done, info] = env.step(action);
        total_reward = total_reward + reward;
        state = next_state;

        if done
            break;
        end
    end

    env_info = env.get_environment_info();
    performance = struct('reward', total_reward, 'energy', env_info.total_energy, ...
                        'pdr', env_info.average_pdr, 'delay', env_info.average_delay);
end

function performance = test_hr_tpc_algorithm(env)
    % 测试基于RSSI变化的TPC算法 (替代HR-TPC)
    state = env.reset();
    total_reward = 0;

    % RSSI变化历史和自适应参数
    rssi_history = [];
    baseline_rssi = -75; % 基础RSSI

    for step = 1:300
        % 基于RSSI变化选择功率 (模拟HR-TPC的逻辑)
        if step <= length(env.rssi_data)
            current_rssi = env.rssi_data(step);
        else
            current_rssi = env.rssi_data(end);
        end

        rssi_history = [rssi_history, current_rssi];

        % 计算RSSI变化率
        if length(rssi_history) > 5
            recent_rssi = mean(rssi_history(end-4:end));
            rssi_trend = current_rssi - mean(rssi_history(max(1,end-9):end-5));
        else
            recent_rssi = current_rssi;
            rssi_trend = 0;
        end

        % 基于RSSI和变化趋势的智能功率选择
        rssi_deviation = abs(recent_rssi - baseline_rssi);

        if rssi_deviation > 15 || abs(rssi_trend) > 5
            % RSSI异常或快速变化时，需要可靠传输
            action = 4; % 中高功率 -5dBm
        elseif rssi_deviation > 8
            % 中等RSSI变化
            action = 3; % 中功率 -10dBm
        else
            % RSSI稳定时节能
            action = 2; % 低功率 -15dBm
        end

        % 结合路径损耗调整
        if step <= length(env.pathloss_data)
            current_pl = env.pathloss_data(step);
            if current_pl > -70 % 路径损耗高时提高功率
                action = min(6, action + 1);
            elseif current_pl < -85 && rssi_deviation < 5 % 路径损耗低且RSSI稳定时降低功率
                action = max(1, action - 1);
            end
        end

        [next_state, reward, done, info] = env.step(action);
        total_reward = total_reward + reward;
        state = next_state;

        if done
            break;
        end
    end

    env_info = env.get_environment_info();
    performance = struct('reward', total_reward, 'energy', env_info.total_energy, ...
                        'pdr', env_info.average_pdr, 'delay', env_info.average_delay);
end

function performance = test_simple_dqn_algorithm(env)
    % 测试改进的简单DQN算法 (作为强化学习基准)
    state = env.reset();
    total_reward = 0;

    % 改进的Q表 (更细粒度的状态离散化)
    num_states = 20; % 增加状态数量
    q_table = zeros(num_states, 6); % 初始化为0而非随机值
    epsilon = 0.3; % 提高初始探索率
    epsilon_decay = 0.995;
    epsilon_min = 0.05;
    learning_rate = 0.1;
    gamma = 0.9;

    % 简单的经验回放
    memory = [];
    memory_size = 100;

    for step = 1:300
        % 改进的状态离散化 (基于多个特征)
        % 使用能耗相关特征和信道质量
        energy_feature = min(1, max(0, state(1))); % 归一化第一个特征
        channel_feature = min(1, max(0, state(5))); % 归一化信道特征
        combined_feature = 0.7 * energy_feature + 0.3 * channel_feature;
        state_idx = min(num_states, max(1, round(combined_feature * (num_states-1)) + 1));

        % epsilon-贪心策略 + 节能偏向
        if rand() < epsilon
            % 探索时偏向低功率动作
            if rand() < 0.6
                action = randi(3); % 偏向低功率动作1-3
            else
                action = randi(6); % 完全随机
            end
        else
            % 利用最佳动作
            [~, action] = max(q_table(state_idx, :));
        end

        [next_state, reward, done, info] = env.step(action);

        % 存储经验
        experience = struct('state_idx', state_idx, 'action', action, ...
                           'reward', reward, 'next_state', next_state, 'done', done);
        memory = [memory; experience];
        if length(memory) > memory_size
            memory = memory(2:end);
        end

        % Q学习更新 (使用经验回放)
        if length(memory) >= 10
            % 随机采样经验进行学习
            sample_idx = randi(length(memory));
            exp = memory(sample_idx);

            % 计算下一状态索引
            if ~exp.done
                next_energy = min(1, max(0, exp.next_state(1)));
                next_channel = min(1, max(0, exp.next_state(5)));
                next_combined = 0.7 * next_energy + 0.3 * next_channel;
                next_state_idx = min(num_states, max(1, round(next_combined * (num_states-1)) + 1));
                target = exp.reward + gamma * max(q_table(next_state_idx, :));
            else
                target = exp.reward;
            end

            % Q值更新
            q_table(exp.state_idx, exp.action) = q_table(exp.state_idx, exp.action) + ...
                learning_rate * (target - q_table(exp.state_idx, exp.action));
        end

        % 衰减探索率
        epsilon = max(epsilon_min, epsilon * epsilon_decay);

        total_reward = total_reward + reward;
        state = next_state;

        if done
            break;
        end
    end

    env_info = env.get_environment_info();
    performance = struct('reward', total_reward, 'energy', env_info.total_energy, ...
                        'pdr', env_info.average_pdr, 'delay', env_info.average_delay);
end

function final_performance = evaluate_trained_agent(env, agent)
    % 评估训练后的智能体性能
    fprintf('评估训练后的智能体...\n');

    % 关闭探索，使用纯利用策略
    original_epsilon = agent.epsilon;
    agent.epsilon = 0;

    state = env.reset();
    total_reward = 0;

    for step = 1:300
        meta_action = agent.select_meta_action(state);
        local_state = env.get_local_state(meta_action);
        action = agent.select_local_action(local_state);

        [next_state, reward, done, info] = env.step(action);
        total_reward = total_reward + reward;
        state = next_state;

        if done
            break;
        end
    end

    % 恢复探索率
    agent.epsilon = original_epsilon;

    env_info = env.get_environment_info();
    final_performance = struct('reward', total_reward, 'energy', env_info.total_energy, ...
                              'pdr', env_info.average_pdr, 'delay', env_info.average_delay);

    fprintf('最终性能: Reward=%.2f, Energy=%.2f, PDR=%.3f, Delay=%.1f\n', ...
           total_reward, env_info.total_energy, env_info.average_pdr, env_info.average_delay);
end

function plot_training_results(episode_rewards, episode_energy, episode_pdr, episode_delay, ...
                              baseline_performance, final_performance)
    % 绘制训练结果
    figure('Position', [100, 100, 1400, 1000]);
    
    % 奖励曲线
    subplot(2,3,1);
    plot(1:length(episode_rewards), episode_rewards, 'b-', 'LineWidth', 1.5);
    title('训练奖励曲线');
    xlabel('Episode');
    ylabel('累积奖励');
    grid on;
    
    % 能耗对比
    subplot(2,3,2);
    algorithms = {'固定功率', '路径损耗TPC', 'RSSI-TPC', 'DQN', '分层RL'};
    energy_values = [baseline_performance.fixed_power.energy, ...
                    baseline_performance.pathloss_tpc.energy, ...
                    baseline_performance.rssi_tpc.energy, ...
                    baseline_performance.simple_dqn.energy, ...
                    final_performance.energy];
    
    bar(energy_values, 'FaceColor', [0.2, 0.6, 0.8]);
    set(gca, 'XTickLabel', algorithms, 'XTickLabelRotation', 45);
    title('能耗对比');
    ylabel('总能耗 (mJ)');
    grid on;
    
    % PDR对比
    subplot(2,3,3);
    pdr_values = [baseline_performance.fixed_power.pdr, ...
                 baseline_performance.pathloss_tpc.pdr, ...
                 baseline_performance.rssi_tpc.pdr, ...
                 baseline_performance.simple_dqn.pdr, ...
                 final_performance.pdr];
    
    bar(pdr_values, 'FaceColor', [0.8, 0.4, 0.2]);
    set(gca, 'XTickLabel', algorithms, 'XTickLabelRotation', 45);
    title('包递交率对比');
    ylabel('PDR');
    ylim([0, 1]);
    grid on;
    
    % 延迟对比
    subplot(2,3,4);
    delay_values = [baseline_performance.fixed_power.delay, ...
                   baseline_performance.pathloss_tpc.delay, ...
                   baseline_performance.rssi_tpc.delay, ...
                   baseline_performance.simple_dqn.delay, ...
                   final_performance.delay];
    
    bar(delay_values, 'FaceColor', [0.4, 0.8, 0.4]);
    set(gca, 'XTickLabel', algorithms, 'XTickLabelRotation', 45);
    title('平均延迟对比');
    ylabel('延迟 (ms)');
    grid on;
    
    % 训练过程中的PDR变化
    subplot(2,3,5);
    plot(1:length(episode_pdr), episode_pdr, 'r-', 'LineWidth', 1.5);
    title('训练过程PDR变化');
    xlabel('Episode');
    ylabel('PDR');
    ylim([0, 1]);
    grid on;
    
    % 训练过程中的能耗变化
    subplot(2,3,6);
    plot(1:length(episode_energy), episode_energy, 'g-', 'LineWidth', 1.5);
    title('训练过程能耗变化');
    xlabel('Episode');
    ylabel('能耗 (mJ)');
    grid on;
    
    sgtitle('基于分层强化学习的WBAN功率控制训练结果', 'FontSize', 16);
end

function save_training_results(agent, episode_rewards, episode_energy, episode_pdr, episode_delay, ...
                              baseline_performance, final_performance)
    % 保存训练结果
    results = struct();
    results.episode_rewards = episode_rewards;
    results.episode_energy = episode_energy;
    results.episode_pdr = episode_pdr;
    results.episode_delay = episode_delay;
    results.baseline_performance = baseline_performance;
    results.final_performance = final_performance;
    results.agent = agent;
    
    save('rl_training_results.mat', 'results');
    
    % 保存性能对比CSV
    performance_table = table();
    performance_table.Algorithm = {'固定功率'; '路径损耗TPC'; 'RSSI-TPC'; '简单DQN'; '分层RL'};
    performance_table.Energy_mJ = [baseline_performance.fixed_power.energy; ...
                                  baseline_performance.pathloss_tpc.energy; ...
                                  baseline_performance.rssi_tpc.energy; ...
                                  baseline_performance.simple_dqn.energy; ...
                                  final_performance.energy];
    performance_table.PDR = [baseline_performance.fixed_power.pdr; ...
                            baseline_performance.pathloss_tpc.pdr; ...
                            baseline_performance.rssi_tpc.pdr; ...
                            baseline_performance.simple_dqn.pdr; ...
                            final_performance.pdr];
    performance_table.Delay_ms = [baseline_performance.fixed_power.delay; ...
                                 baseline_performance.pathloss_tpc.delay; ...
                                 baseline_performance.rssi_tpc.delay; ...
                                 baseline_performance.simple_dqn.delay; ...
                                 final_performance.delay];
    
    writetable(performance_table, 'rl_performance_comparison.csv');
end
