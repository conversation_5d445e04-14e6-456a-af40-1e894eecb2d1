<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<?scdStore version="2"?><scannerInfo id="org.eclipse.cdt.make.core.discoveredScannerInfo">
<instance id="org.omnetpp.cdt.gnu.config.debug.718612962">
<collector id="org.eclipse.cdt.make.core.PerProjectSICollector">
<includePath path="C:/omnetpp-4.6/tools/win32/mingw32/bin/../lib/gcc/i686-w64-mingw32/4.9.2/include"/>
<includePath path="C:/omnetpp-4.6/tools/win32/mingw32/bin/../lib/gcc/i686-w64-mingw32/4.9.2/../../../../include"/>
<includePath path="C:/omnetpp-4.6/tools/win32/mingw32/bin/../lib/gcc/i686-w64-mingw32/4.9.2/include-fixed"/>
<includePath path="C:/omnetpp-4.6/tools/win32/mingw32/bin/../lib/gcc/i686-w64-mingw32/4.9.2/../../../../i686-w64-mingw32/include"/>
<includePath path="C:/omnetpp-4.6/tools/win32/mingw32/lib/gcc/../../include/c++/4.9.2"/>
<includePath path="C:/omnetpp-4.6/tools/win32/mingw32/lib/gcc/../../include/c++/4.9.2/i686-w64-mingw32"/>
<includePath path="C:/omnetpp-4.6/tools/win32/mingw32/lib/gcc/../../include/c++/4.9.2/backward"/>
<includePath path="C:\omnetpp-4.6\include"/>
<definedSymbol symbol="__STDC__=1"/>
<definedSymbol symbol="__cplusplus=199711L"/>
<definedSymbol symbol="__STDC_HOSTED__=1"/>
<definedSymbol symbol="__GNUC__=4"/>
<definedSymbol symbol="__GNUC_MINOR__=9"/>
<definedSymbol symbol="__GNUC_PATCHLEVEL__=2"/>
<definedSymbol symbol="__VERSION__=&quot;4.9.2&quot;"/>
<definedSymbol symbol="__ATOMIC_RELAXED=0"/>
<definedSymbol symbol="__ATOMIC_SEQ_CST=5"/>
<definedSymbol symbol="__ATOMIC_ACQUIRE=2"/>
<definedSymbol symbol="__ATOMIC_RELEASE=3"/>
<definedSymbol symbol="__ATOMIC_ACQ_REL=4"/>
<definedSymbol symbol="__ATOMIC_CONSUME=1"/>
<definedSymbol symbol="__FINITE_MATH_ONLY__=0"/>
<definedSymbol symbol="__SIZEOF_INT__=4"/>
<definedSymbol symbol="__SIZEOF_LONG__=4"/>
<definedSymbol symbol="__SIZEOF_LONG_LONG__=8"/>
<definedSymbol symbol="__SIZEOF_SHORT__=2"/>
<definedSymbol symbol="__SIZEOF_FLOAT__=4"/>
<definedSymbol symbol="__SIZEOF_DOUBLE__=8"/>
<definedSymbol symbol="__SIZEOF_LONG_DOUBLE__=12"/>
<definedSymbol symbol="__SIZEOF_SIZE_T__=4"/>
<definedSymbol symbol="__CHAR_BIT__=8"/>
<definedSymbol symbol="__BIGGEST_ALIGNMENT__=16"/>
<definedSymbol symbol="__ORDER_LITTLE_ENDIAN__=1234"/>
<definedSymbol symbol="__ORDER_BIG_ENDIAN__=4321"/>
<definedSymbol symbol="__ORDER_PDP_ENDIAN__=3412"/>
<definedSymbol symbol="__BYTE_ORDER__=__ORDER_LITTLE_ENDIAN__"/>
<definedSymbol symbol="__FLOAT_WORD_ORDER__=__ORDER_LITTLE_ENDIAN__"/>
<definedSymbol symbol="__SIZEOF_POINTER__=4"/>
<definedSymbol symbol="__GNUG__=4"/>
<definedSymbol symbol="__SIZE_TYPE__=unsigned int"/>
<definedSymbol symbol="__PTRDIFF_TYPE__=int"/>
<definedSymbol symbol="__WCHAR_TYPE__=short unsigned int"/>
<definedSymbol symbol="__WINT_TYPE__=short unsigned int"/>
<definedSymbol symbol="__INTMAX_TYPE__=long long int"/>
<definedSymbol symbol="__UINTMAX_TYPE__=long long unsigned int"/>
<definedSymbol symbol="__CHAR16_TYPE__=short unsigned int"/>
<definedSymbol symbol="__CHAR32_TYPE__=unsigned int"/>
<definedSymbol symbol="__SIG_ATOMIC_TYPE__=int"/>
<definedSymbol symbol="__INT8_TYPE__=signed char"/>
<definedSymbol symbol="__INT16_TYPE__=short int"/>
<definedSymbol symbol="__INT32_TYPE__=int"/>
<definedSymbol symbol="__INT64_TYPE__=long long int"/>
<definedSymbol symbol="__UINT8_TYPE__=unsigned char"/>
<definedSymbol symbol="__UINT16_TYPE__=short unsigned int"/>
<definedSymbol symbol="__UINT32_TYPE__=unsigned int"/>
<definedSymbol symbol="__UINT64_TYPE__=long long unsigned int"/>
<definedSymbol symbol="__INT_LEAST8_TYPE__=signed char"/>
<definedSymbol symbol="__INT_LEAST16_TYPE__=short int"/>
<definedSymbol symbol="__INT_LEAST32_TYPE__=int"/>
<definedSymbol symbol="__INT_LEAST64_TYPE__=long long int"/>
<definedSymbol symbol="__UINT_LEAST8_TYPE__=unsigned char"/>
<definedSymbol symbol="__UINT_LEAST16_TYPE__=short unsigned int"/>
<definedSymbol symbol="__UINT_LEAST32_TYPE__=unsigned int"/>
<definedSymbol symbol="__UINT_LEAST64_TYPE__=long long unsigned int"/>
<definedSymbol symbol="__INT_FAST8_TYPE__=signed char"/>
<definedSymbol symbol="__INT_FAST16_TYPE__=short int"/>
<definedSymbol symbol="__INT_FAST32_TYPE__=int"/>
<definedSymbol symbol="__INT_FAST64_TYPE__=long long int"/>
<definedSymbol symbol="__UINT_FAST8_TYPE__=unsigned char"/>
<definedSymbol symbol="__UINT_FAST16_TYPE__=short unsigned int"/>
<definedSymbol symbol="__UINT_FAST32_TYPE__=unsigned int"/>
<definedSymbol symbol="__UINT_FAST64_TYPE__=long long unsigned int"/>
<definedSymbol symbol="__INTPTR_TYPE__=int"/>
<definedSymbol symbol="__UINTPTR_TYPE__=unsigned int"/>
<definedSymbol symbol="__has_include(STR)=__has_include__(STR)"/>
<definedSymbol symbol="__has_include_next(STR)=__has_include_next__(STR)"/>
<definedSymbol symbol="__GXX_WEAK__=1"/>
<definedSymbol symbol="__DEPRECATED=1"/>
<definedSymbol symbol="__GXX_RTTI=1"/>
<definedSymbol symbol="__cpp_binary_literals=201304"/>
<definedSymbol symbol="__EXCEPTIONS=1"/>
<definedSymbol symbol="__GXX_ABI_VERSION=1002"/>
<definedSymbol symbol="__SCHAR_MAX__=127"/>
<definedSymbol symbol="__SHRT_MAX__=32767"/>
<definedSymbol symbol="__INT_MAX__=2147483647"/>
<definedSymbol symbol="__LONG_MAX__=2147483647L"/>
<definedSymbol symbol="__LONG_LONG_MAX__=9223372036854775807LL"/>
<definedSymbol symbol="__WCHAR_MAX__=65535"/>
<definedSymbol symbol="__WCHAR_MIN__=0"/>
<definedSymbol symbol="__WINT_MAX__=65535"/>
<definedSymbol symbol="__WINT_MIN__=0"/>
<definedSymbol symbol="__PTRDIFF_MAX__=2147483647"/>
<definedSymbol symbol="__SIZE_MAX__=4294967295U"/>
<definedSymbol symbol="__INTMAX_MAX__=9223372036854775807LL"/>
<definedSymbol symbol="__INTMAX_C(c)=c ## LL"/>
<definedSymbol symbol="__UINTMAX_MAX__=18446744073709551615ULL"/>
<definedSymbol symbol="__UINTMAX_C(c)=c ## ULL"/>
<definedSymbol symbol="__SIG_ATOMIC_MAX__=2147483647"/>
<definedSymbol symbol="__SIG_ATOMIC_MIN__=(-__SIG_ATOMIC_MAX__ - 1)"/>
<definedSymbol symbol="__INT8_MAX__=127"/>
<definedSymbol symbol="__INT16_MAX__=32767"/>
<definedSymbol symbol="__INT32_MAX__=2147483647"/>
<definedSymbol symbol="__INT64_MAX__=9223372036854775807LL"/>
<definedSymbol symbol="__UINT8_MAX__=255"/>
<definedSymbol symbol="__UINT16_MAX__=65535"/>
<definedSymbol symbol="__UINT32_MAX__=4294967295U"/>
<definedSymbol symbol="__UINT64_MAX__=18446744073709551615ULL"/>
<definedSymbol symbol="__INT_LEAST8_MAX__=127"/>
<definedSymbol symbol="__INT8_C(c)=c"/>
<definedSymbol symbol="__INT_LEAST16_MAX__=32767"/>
<definedSymbol symbol="__INT16_C(c)=c"/>
<definedSymbol symbol="__INT_LEAST32_MAX__=2147483647"/>
<definedSymbol symbol="__INT32_C(c)=c"/>
<definedSymbol symbol="__INT_LEAST64_MAX__=9223372036854775807LL"/>
<definedSymbol symbol="__INT64_C(c)=c ## LL"/>
<definedSymbol symbol="__UINT_LEAST8_MAX__=255"/>
<definedSymbol symbol="__UINT8_C(c)=c"/>
<definedSymbol symbol="__UINT_LEAST16_MAX__=65535"/>
<definedSymbol symbol="__UINT16_C(c)=c"/>
<definedSymbol symbol="__UINT_LEAST32_MAX__=4294967295U"/>
<definedSymbol symbol="__UINT32_C(c)=c ## U"/>
<definedSymbol symbol="__UINT_LEAST64_MAX__=18446744073709551615ULL"/>
<definedSymbol symbol="__UINT64_C(c)=c ## ULL"/>
<definedSymbol symbol="__INT_FAST8_MAX__=127"/>
<definedSymbol symbol="__INT_FAST16_MAX__=32767"/>
<definedSymbol symbol="__INT_FAST32_MAX__=2147483647"/>
<definedSymbol symbol="__INT_FAST64_MAX__=9223372036854775807LL"/>
<definedSymbol symbol="__UINT_FAST8_MAX__=255"/>
<definedSymbol symbol="__UINT_FAST16_MAX__=65535"/>
<definedSymbol symbol="__UINT_FAST32_MAX__=4294967295U"/>
<definedSymbol symbol="__UINT_FAST64_MAX__=18446744073709551615ULL"/>
<definedSymbol symbol="__INTPTR_MAX__=2147483647"/>
<definedSymbol symbol="__UINTPTR_MAX__=4294967295U"/>
<definedSymbol symbol="__GCC_IEC_559=2"/>
<definedSymbol symbol="__GCC_IEC_559_COMPLEX=2"/>
<definedSymbol symbol="__FLT_EVAL_METHOD__=2"/>
<definedSymbol symbol="__DEC_EVAL_METHOD__=2"/>
<definedSymbol symbol="__FLT_RADIX__=2"/>
<definedSymbol symbol="__FLT_MANT_DIG__=24"/>
<definedSymbol symbol="__FLT_DIG__=6"/>
<definedSymbol symbol="__FLT_MIN_EXP__=(-125)"/>
<definedSymbol symbol="__FLT_MIN_10_EXP__=(-37)"/>
<definedSymbol symbol="__FLT_MAX_EXP__=128"/>
<definedSymbol symbol="__FLT_MAX_10_EXP__=38"/>
<definedSymbol symbol="__FLT_DECIMAL_DIG__=9"/>
<definedSymbol symbol="__FLT_MAX__=3.40282346638528859812e+38F"/>
<definedSymbol symbol="__FLT_MIN__=1.17549435082228750797e-38F"/>
<definedSymbol symbol="__FLT_EPSILON__=1.19209289550781250000e-7F"/>
<definedSymbol symbol="__FLT_DENORM_MIN__=1.40129846432481707092e-45F"/>
<definedSymbol symbol="__FLT_HAS_DENORM__=1"/>
<definedSymbol symbol="__FLT_HAS_INFINITY__=1"/>
<definedSymbol symbol="__FLT_HAS_QUIET_NAN__=1"/>
<definedSymbol symbol="__DBL_MANT_DIG__=53"/>
<definedSymbol symbol="__DBL_DIG__=15"/>
<definedSymbol symbol="__DBL_MIN_EXP__=(-1021)"/>
<definedSymbol symbol="__DBL_MIN_10_EXP__=(-307)"/>
<definedSymbol symbol="__DBL_MAX_EXP__=1024"/>
<definedSymbol symbol="__DBL_MAX_10_EXP__=308"/>
<definedSymbol symbol="__DBL_DECIMAL_DIG__=17"/>
<definedSymbol symbol="__DBL_MAX__=double(1.79769313486231570815e+308L)"/>
<definedSymbol symbol="__DBL_MIN__=double(2.22507385850720138309e-308L)"/>
<definedSymbol symbol="__DBL_EPSILON__=double(2.22044604925031308085e-16L)"/>
<definedSymbol symbol="__DBL_DENORM_MIN__=double(4.94065645841246544177e-324L)"/>
<definedSymbol symbol="__DBL_HAS_DENORM__=1"/>
<definedSymbol symbol="__DBL_HAS_INFINITY__=1"/>
<definedSymbol symbol="__DBL_HAS_QUIET_NAN__=1"/>
<definedSymbol symbol="__LDBL_MANT_DIG__=64"/>
<definedSymbol symbol="__LDBL_DIG__=18"/>
<definedSymbol symbol="__LDBL_MIN_EXP__=(-16381)"/>
<definedSymbol symbol="__LDBL_MIN_10_EXP__=(-4931)"/>
<definedSymbol symbol="__LDBL_MAX_EXP__=16384"/>
<definedSymbol symbol="__LDBL_MAX_10_EXP__=4932"/>
<definedSymbol symbol="__DECIMAL_DIG__=21"/>
<definedSymbol symbol="__LDBL_MAX__=1.18973149535723176502e+4932L"/>
<definedSymbol symbol="__LDBL_MIN__=3.36210314311209350626e-4932L"/>
<definedSymbol symbol="__LDBL_EPSILON__=1.08420217248550443401e-19L"/>
<definedSymbol symbol="__LDBL_DENORM_MIN__=3.64519953188247460253e-4951L"/>
<definedSymbol symbol="__LDBL_HAS_DENORM__=1"/>
<definedSymbol symbol="__LDBL_HAS_INFINITY__=1"/>
<definedSymbol symbol="__LDBL_HAS_QUIET_NAN__=1"/>
<definedSymbol symbol="__DEC32_MANT_DIG__=7"/>
<definedSymbol symbol="__DEC32_MIN_EXP__=(-94)"/>
<definedSymbol symbol="__DEC32_MAX_EXP__=97"/>
<definedSymbol symbol="__DEC32_MIN__=1E-95DF"/>
<definedSymbol symbol="__DEC32_MAX__=9.999999E96DF"/>
<definedSymbol symbol="__DEC32_EPSILON__=1E-6DF"/>
<definedSymbol symbol="__DEC32_SUBNORMAL_MIN__=0.000001E-95DF"/>
<definedSymbol symbol="__DEC64_MANT_DIG__=16"/>
<definedSymbol symbol="__DEC64_MIN_EXP__=(-382)"/>
<definedSymbol symbol="__DEC64_MAX_EXP__=385"/>
<definedSymbol symbol="__DEC64_MIN__=1E-383DD"/>
<definedSymbol symbol="__DEC64_MAX__=9.999999999999999E384DD"/>
<definedSymbol symbol="__DEC64_EPSILON__=1E-15DD"/>
<definedSymbol symbol="__DEC64_SUBNORMAL_MIN__=0.000000000000001E-383DD"/>
<definedSymbol symbol="__DEC128_MANT_DIG__=34"/>
<definedSymbol symbol="__DEC128_MIN_EXP__=(-6142)"/>
<definedSymbol symbol="__DEC128_MAX_EXP__=6145"/>
<definedSymbol symbol="__DEC128_MIN__=1E-6143DL"/>
<definedSymbol symbol="__DEC128_MAX__=9.999999999999999999999999999999999E6144DL"/>
<definedSymbol symbol="__DEC128_EPSILON__=1E-33DL"/>
<definedSymbol symbol="__DEC128_SUBNORMAL_MIN__=0.000000000000000000000000000000001E-6143DL"/>
<definedSymbol symbol="__REGISTER_PREFIX__="/>
<definedSymbol symbol="__USER_LABEL_PREFIX__=_"/>
<definedSymbol symbol="__GNUC_GNU_INLINE__=1"/>
<definedSymbol symbol="__NO_INLINE__=1"/>
<definedSymbol symbol="__WCHAR_UNSIGNED__=1"/>
<definedSymbol symbol="__GCC_HAVE_SYNC_COMPARE_AND_SWAP_1=1"/>
<definedSymbol symbol="__GCC_HAVE_SYNC_COMPARE_AND_SWAP_2=1"/>
<definedSymbol symbol="__GCC_HAVE_SYNC_COMPARE_AND_SWAP_4=1"/>
<definedSymbol symbol="__GCC_HAVE_SYNC_COMPARE_AND_SWAP_8=1"/>
<definedSymbol symbol="__GCC_ATOMIC_BOOL_LOCK_FREE=2"/>
<definedSymbol symbol="__GCC_ATOMIC_CHAR_LOCK_FREE=2"/>
<definedSymbol symbol="__GCC_ATOMIC_CHAR16_T_LOCK_FREE=2"/>
<definedSymbol symbol="__GCC_ATOMIC_CHAR32_T_LOCK_FREE=2"/>
<definedSymbol symbol="__GCC_ATOMIC_WCHAR_T_LOCK_FREE=2"/>
<definedSymbol symbol="__GCC_ATOMIC_SHORT_LOCK_FREE=2"/>
<definedSymbol symbol="__GCC_ATOMIC_INT_LOCK_FREE=2"/>
<definedSymbol symbol="__GCC_ATOMIC_LONG_LOCK_FREE=2"/>
<definedSymbol symbol="__GCC_ATOMIC_LLONG_LOCK_FREE=2"/>
<definedSymbol symbol="__GCC_ATOMIC_TEST_AND_SET_TRUEVAL=1"/>
<definedSymbol symbol="__GCC_ATOMIC_POINTER_LOCK_FREE=2"/>
<definedSymbol symbol="__GCC_HAVE_DWARF2_CFI_ASM=1"/>
<definedSymbol symbol="__PRAGMA_REDEFINE_EXTNAME=1"/>
<definedSymbol symbol="__SIZEOF_WCHAR_T__=2"/>
<definedSymbol symbol="__SIZEOF_WINT_T__=2"/>
<definedSymbol symbol="__SIZEOF_PTRDIFF_T__=4"/>
<definedSymbol symbol="__i386=1"/>
<definedSymbol symbol="__i386__=1"/>
<definedSymbol symbol="i386=1"/>
<definedSymbol symbol="__ATOMIC_HLE_ACQUIRE=65536"/>
<definedSymbol symbol="__ATOMIC_HLE_RELEASE=131072"/>
<definedSymbol symbol="__i686=1"/>
<definedSymbol symbol="__i686__=1"/>
<definedSymbol symbol="__pentiumpro=1"/>
<definedSymbol symbol="__pentiumpro__=1"/>
<definedSymbol symbol="__code_model_32__=1"/>
<definedSymbol symbol="_X86_=1"/>
<definedSymbol symbol="__stdcall=__attribute__((__stdcall__))"/>
<definedSymbol symbol="__fastcall=__attribute__((__fastcall__))"/>
<definedSymbol symbol="__thiscall=__attribute__((__thiscall__))"/>
<definedSymbol symbol="__cdecl=__attribute__((__cdecl__))"/>
<definedSymbol symbol="_stdcall=__attribute__((__stdcall__))"/>
<definedSymbol symbol="_fastcall=__attribute__((__fastcall__))"/>
<definedSymbol symbol="_thiscall=__attribute__((__thiscall__))"/>
<definedSymbol symbol="_cdecl=__attribute__((__cdecl__))"/>
<definedSymbol symbol="__GXX_MERGED_TYPEINFO_NAMES=0"/>
<definedSymbol symbol="__GXX_TYPEINFO_EQUALITY_INLINE=0"/>
<definedSymbol symbol="__MSVCRT__=1"/>
<definedSymbol symbol="__MINGW32__=1"/>
<definedSymbol symbol="_WIN32=1"/>
<definedSymbol symbol="__WIN32=1"/>
<definedSymbol symbol="__WIN32__=1"/>
<definedSymbol symbol="WIN32=1"/>
<definedSymbol symbol="__WINNT=1"/>
<definedSymbol symbol="__WINNT__=1"/>
<definedSymbol symbol="WINNT=1"/>
<definedSymbol symbol="_INTEGRAL_MAX_BITS=64"/>
<definedSymbol symbol="__declspec(x)=__attribute__((x))"/>
<definedSymbol symbol="__DECIMAL_BID_FORMAT__=1"/>
<definedSymbol symbol="_REENTRANT=1"/>
</collector>
</instance>
<instance id="org.omnetpp.cdt.gnu.config.release.161839981">
<collector id="org.eclipse.cdt.make.core.PerProjectSICollector">
<includePath path="C:/omnetpp-4.6/tools/win32/mingw32/bin/../lib/gcc/i686-w64-mingw32/4.9.2/include"/>
<includePath path="C:/omnetpp-4.6/tools/win32/mingw32/bin/../lib/gcc/i686-w64-mingw32/4.9.2/../../../../include"/>
<includePath path="C:/omnetpp-4.6/tools/win32/mingw32/bin/../lib/gcc/i686-w64-mingw32/4.9.2/include-fixed"/>
<includePath path="C:/omnetpp-4.6/tools/win32/mingw32/bin/../lib/gcc/i686-w64-mingw32/4.9.2/../../../../i686-w64-mingw32/include"/>
<includePath path="C:/omnetpp-4.6/tools/win32/mingw32/lib/gcc/../../include/c++/4.9.2"/>
<includePath path="C:/omnetpp-4.6/tools/win32/mingw32/lib/gcc/../../include/c++/4.9.2/i686-w64-mingw32"/>
<includePath path="C:/omnetpp-4.6/tools/win32/mingw32/lib/gcc/../../include/c++/4.9.2/backward"/>
<includePath path="C:\omnetpp-4.6\include"/>
<definedSymbol symbol="__STDC__=1"/>
<definedSymbol symbol="__cplusplus=199711L"/>
<definedSymbol symbol="__STDC_HOSTED__=1"/>
<definedSymbol symbol="__GNUC__=4"/>
<definedSymbol symbol="__GNUC_MINOR__=9"/>
<definedSymbol symbol="__GNUC_PATCHLEVEL__=2"/>
<definedSymbol symbol="__VERSION__=&quot;4.9.2&quot;"/>
<definedSymbol symbol="__ATOMIC_RELAXED=0"/>
<definedSymbol symbol="__ATOMIC_SEQ_CST=5"/>
<definedSymbol symbol="__ATOMIC_ACQUIRE=2"/>
<definedSymbol symbol="__ATOMIC_RELEASE=3"/>
<definedSymbol symbol="__ATOMIC_ACQ_REL=4"/>
<definedSymbol symbol="__ATOMIC_CONSUME=1"/>
<definedSymbol symbol="__FINITE_MATH_ONLY__=0"/>
<definedSymbol symbol="__SIZEOF_INT__=4"/>
<definedSymbol symbol="__SIZEOF_LONG__=4"/>
<definedSymbol symbol="__SIZEOF_LONG_LONG__=8"/>
<definedSymbol symbol="__SIZEOF_SHORT__=2"/>
<definedSymbol symbol="__SIZEOF_FLOAT__=4"/>
<definedSymbol symbol="__SIZEOF_DOUBLE__=8"/>
<definedSymbol symbol="__SIZEOF_LONG_DOUBLE__=12"/>
<definedSymbol symbol="__SIZEOF_SIZE_T__=4"/>
<definedSymbol symbol="__CHAR_BIT__=8"/>
<definedSymbol symbol="__BIGGEST_ALIGNMENT__=16"/>
<definedSymbol symbol="__ORDER_LITTLE_ENDIAN__=1234"/>
<definedSymbol symbol="__ORDER_BIG_ENDIAN__=4321"/>
<definedSymbol symbol="__ORDER_PDP_ENDIAN__=3412"/>
<definedSymbol symbol="__BYTE_ORDER__=__ORDER_LITTLE_ENDIAN__"/>
<definedSymbol symbol="__FLOAT_WORD_ORDER__=__ORDER_LITTLE_ENDIAN__"/>
<definedSymbol symbol="__SIZEOF_POINTER__=4"/>
<definedSymbol symbol="__GNUG__=4"/>
<definedSymbol symbol="__SIZE_TYPE__=unsigned int"/>
<definedSymbol symbol="__PTRDIFF_TYPE__=int"/>
<definedSymbol symbol="__WCHAR_TYPE__=short unsigned int"/>
<definedSymbol symbol="__WINT_TYPE__=short unsigned int"/>
<definedSymbol symbol="__INTMAX_TYPE__=long long int"/>
<definedSymbol symbol="__UINTMAX_TYPE__=long long unsigned int"/>
<definedSymbol symbol="__CHAR16_TYPE__=short unsigned int"/>
<definedSymbol symbol="__CHAR32_TYPE__=unsigned int"/>
<definedSymbol symbol="__SIG_ATOMIC_TYPE__=int"/>
<definedSymbol symbol="__INT8_TYPE__=signed char"/>
<definedSymbol symbol="__INT16_TYPE__=short int"/>
<definedSymbol symbol="__INT32_TYPE__=int"/>
<definedSymbol symbol="__INT64_TYPE__=long long int"/>
<definedSymbol symbol="__UINT8_TYPE__=unsigned char"/>
<definedSymbol symbol="__UINT16_TYPE__=short unsigned int"/>
<definedSymbol symbol="__UINT32_TYPE__=unsigned int"/>
<definedSymbol symbol="__UINT64_TYPE__=long long unsigned int"/>
<definedSymbol symbol="__INT_LEAST8_TYPE__=signed char"/>
<definedSymbol symbol="__INT_LEAST16_TYPE__=short int"/>
<definedSymbol symbol="__INT_LEAST32_TYPE__=int"/>
<definedSymbol symbol="__INT_LEAST64_TYPE__=long long int"/>
<definedSymbol symbol="__UINT_LEAST8_TYPE__=unsigned char"/>
<definedSymbol symbol="__UINT_LEAST16_TYPE__=short unsigned int"/>
<definedSymbol symbol="__UINT_LEAST32_TYPE__=unsigned int"/>
<definedSymbol symbol="__UINT_LEAST64_TYPE__=long long unsigned int"/>
<definedSymbol symbol="__INT_FAST8_TYPE__=signed char"/>
<definedSymbol symbol="__INT_FAST16_TYPE__=short int"/>
<definedSymbol symbol="__INT_FAST32_TYPE__=int"/>
<definedSymbol symbol="__INT_FAST64_TYPE__=long long int"/>
<definedSymbol symbol="__UINT_FAST8_TYPE__=unsigned char"/>
<definedSymbol symbol="__UINT_FAST16_TYPE__=short unsigned int"/>
<definedSymbol symbol="__UINT_FAST32_TYPE__=unsigned int"/>
<definedSymbol symbol="__UINT_FAST64_TYPE__=long long unsigned int"/>
<definedSymbol symbol="__INTPTR_TYPE__=int"/>
<definedSymbol symbol="__UINTPTR_TYPE__=unsigned int"/>
<definedSymbol symbol="__has_include(STR)=__has_include__(STR)"/>
<definedSymbol symbol="__has_include_next(STR)=__has_include_next__(STR)"/>
<definedSymbol symbol="__GXX_WEAK__=1"/>
<definedSymbol symbol="__DEPRECATED=1"/>
<definedSymbol symbol="__GXX_RTTI=1"/>
<definedSymbol symbol="__cpp_binary_literals=201304"/>
<definedSymbol symbol="__EXCEPTIONS=1"/>
<definedSymbol symbol="__GXX_ABI_VERSION=1002"/>
<definedSymbol symbol="__SCHAR_MAX__=127"/>
<definedSymbol symbol="__SHRT_MAX__=32767"/>
<definedSymbol symbol="__INT_MAX__=2147483647"/>
<definedSymbol symbol="__LONG_MAX__=2147483647L"/>
<definedSymbol symbol="__LONG_LONG_MAX__=9223372036854775807LL"/>
<definedSymbol symbol="__WCHAR_MAX__=65535"/>
<definedSymbol symbol="__WCHAR_MIN__=0"/>
<definedSymbol symbol="__WINT_MAX__=65535"/>
<definedSymbol symbol="__WINT_MIN__=0"/>
<definedSymbol symbol="__PTRDIFF_MAX__=2147483647"/>
<definedSymbol symbol="__SIZE_MAX__=4294967295U"/>
<definedSymbol symbol="__INTMAX_MAX__=9223372036854775807LL"/>
<definedSymbol symbol="__INTMAX_C(c)=c ## LL"/>
<definedSymbol symbol="__UINTMAX_MAX__=18446744073709551615ULL"/>
<definedSymbol symbol="__UINTMAX_C(c)=c ## ULL"/>
<definedSymbol symbol="__SIG_ATOMIC_MAX__=2147483647"/>
<definedSymbol symbol="__SIG_ATOMIC_MIN__=(-__SIG_ATOMIC_MAX__ - 1)"/>
<definedSymbol symbol="__INT8_MAX__=127"/>
<definedSymbol symbol="__INT16_MAX__=32767"/>
<definedSymbol symbol="__INT32_MAX__=2147483647"/>
<definedSymbol symbol="__INT64_MAX__=9223372036854775807LL"/>
<definedSymbol symbol="__UINT8_MAX__=255"/>
<definedSymbol symbol="__UINT16_MAX__=65535"/>
<definedSymbol symbol="__UINT32_MAX__=4294967295U"/>
<definedSymbol symbol="__UINT64_MAX__=18446744073709551615ULL"/>
<definedSymbol symbol="__INT_LEAST8_MAX__=127"/>
<definedSymbol symbol="__INT8_C(c)=c"/>
<definedSymbol symbol="__INT_LEAST16_MAX__=32767"/>
<definedSymbol symbol="__INT16_C(c)=c"/>
<definedSymbol symbol="__INT_LEAST32_MAX__=2147483647"/>
<definedSymbol symbol="__INT32_C(c)=c"/>
<definedSymbol symbol="__INT_LEAST64_MAX__=9223372036854775807LL"/>
<definedSymbol symbol="__INT64_C(c)=c ## LL"/>
<definedSymbol symbol="__UINT_LEAST8_MAX__=255"/>
<definedSymbol symbol="__UINT8_C(c)=c"/>
<definedSymbol symbol="__UINT_LEAST16_MAX__=65535"/>
<definedSymbol symbol="__UINT16_C(c)=c"/>
<definedSymbol symbol="__UINT_LEAST32_MAX__=4294967295U"/>
<definedSymbol symbol="__UINT32_C(c)=c ## U"/>
<definedSymbol symbol="__UINT_LEAST64_MAX__=18446744073709551615ULL"/>
<definedSymbol symbol="__UINT64_C(c)=c ## ULL"/>
<definedSymbol symbol="__INT_FAST8_MAX__=127"/>
<definedSymbol symbol="__INT_FAST16_MAX__=32767"/>
<definedSymbol symbol="__INT_FAST32_MAX__=2147483647"/>
<definedSymbol symbol="__INT_FAST64_MAX__=9223372036854775807LL"/>
<definedSymbol symbol="__UINT_FAST8_MAX__=255"/>
<definedSymbol symbol="__UINT_FAST16_MAX__=65535"/>
<definedSymbol symbol="__UINT_FAST32_MAX__=4294967295U"/>
<definedSymbol symbol="__UINT_FAST64_MAX__=18446744073709551615ULL"/>
<definedSymbol symbol="__INTPTR_MAX__=2147483647"/>
<definedSymbol symbol="__UINTPTR_MAX__=4294967295U"/>
<definedSymbol symbol="__GCC_IEC_559=2"/>
<definedSymbol symbol="__GCC_IEC_559_COMPLEX=2"/>
<definedSymbol symbol="__FLT_EVAL_METHOD__=2"/>
<definedSymbol symbol="__DEC_EVAL_METHOD__=2"/>
<definedSymbol symbol="__FLT_RADIX__=2"/>
<definedSymbol symbol="__FLT_MANT_DIG__=24"/>
<definedSymbol symbol="__FLT_DIG__=6"/>
<definedSymbol symbol="__FLT_MIN_EXP__=(-125)"/>
<definedSymbol symbol="__FLT_MIN_10_EXP__=(-37)"/>
<definedSymbol symbol="__FLT_MAX_EXP__=128"/>
<definedSymbol symbol="__FLT_MAX_10_EXP__=38"/>
<definedSymbol symbol="__FLT_DECIMAL_DIG__=9"/>
<definedSymbol symbol="__FLT_MAX__=3.40282346638528859812e+38F"/>
<definedSymbol symbol="__FLT_MIN__=1.17549435082228750797e-38F"/>
<definedSymbol symbol="__FLT_EPSILON__=1.19209289550781250000e-7F"/>
<definedSymbol symbol="__FLT_DENORM_MIN__=1.40129846432481707092e-45F"/>
<definedSymbol symbol="__FLT_HAS_DENORM__=1"/>
<definedSymbol symbol="__FLT_HAS_INFINITY__=1"/>
<definedSymbol symbol="__FLT_HAS_QUIET_NAN__=1"/>
<definedSymbol symbol="__DBL_MANT_DIG__=53"/>
<definedSymbol symbol="__DBL_DIG__=15"/>
<definedSymbol symbol="__DBL_MIN_EXP__=(-1021)"/>
<definedSymbol symbol="__DBL_MIN_10_EXP__=(-307)"/>
<definedSymbol symbol="__DBL_MAX_EXP__=1024"/>
<definedSymbol symbol="__DBL_MAX_10_EXP__=308"/>
<definedSymbol symbol="__DBL_DECIMAL_DIG__=17"/>
<definedSymbol symbol="__DBL_MAX__=double(1.79769313486231570815e+308L)"/>
<definedSymbol symbol="__DBL_MIN__=double(2.22507385850720138309e-308L)"/>
<definedSymbol symbol="__DBL_EPSILON__=double(2.22044604925031308085e-16L)"/>
<definedSymbol symbol="__DBL_DENORM_MIN__=double(4.94065645841246544177e-324L)"/>
<definedSymbol symbol="__DBL_HAS_DENORM__=1"/>
<definedSymbol symbol="__DBL_HAS_INFINITY__=1"/>
<definedSymbol symbol="__DBL_HAS_QUIET_NAN__=1"/>
<definedSymbol symbol="__LDBL_MANT_DIG__=64"/>
<definedSymbol symbol="__LDBL_DIG__=18"/>
<definedSymbol symbol="__LDBL_MIN_EXP__=(-16381)"/>
<definedSymbol symbol="__LDBL_MIN_10_EXP__=(-4931)"/>
<definedSymbol symbol="__LDBL_MAX_EXP__=16384"/>
<definedSymbol symbol="__LDBL_MAX_10_EXP__=4932"/>
<definedSymbol symbol="__DECIMAL_DIG__=21"/>
<definedSymbol symbol="__LDBL_MAX__=1.18973149535723176502e+4932L"/>
<definedSymbol symbol="__LDBL_MIN__=3.36210314311209350626e-4932L"/>
<definedSymbol symbol="__LDBL_EPSILON__=1.08420217248550443401e-19L"/>
<definedSymbol symbol="__LDBL_DENORM_MIN__=3.64519953188247460253e-4951L"/>
<definedSymbol symbol="__LDBL_HAS_DENORM__=1"/>
<definedSymbol symbol="__LDBL_HAS_INFINITY__=1"/>
<definedSymbol symbol="__LDBL_HAS_QUIET_NAN__=1"/>
<definedSymbol symbol="__DEC32_MANT_DIG__=7"/>
<definedSymbol symbol="__DEC32_MIN_EXP__=(-94)"/>
<definedSymbol symbol="__DEC32_MAX_EXP__=97"/>
<definedSymbol symbol="__DEC32_MIN__=1E-95DF"/>
<definedSymbol symbol="__DEC32_MAX__=9.999999E96DF"/>
<definedSymbol symbol="__DEC32_EPSILON__=1E-6DF"/>
<definedSymbol symbol="__DEC32_SUBNORMAL_MIN__=0.000001E-95DF"/>
<definedSymbol symbol="__DEC64_MANT_DIG__=16"/>
<definedSymbol symbol="__DEC64_MIN_EXP__=(-382)"/>
<definedSymbol symbol="__DEC64_MAX_EXP__=385"/>
<definedSymbol symbol="__DEC64_MIN__=1E-383DD"/>
<definedSymbol symbol="__DEC64_MAX__=9.999999999999999E384DD"/>
<definedSymbol symbol="__DEC64_EPSILON__=1E-15DD"/>
<definedSymbol symbol="__DEC64_SUBNORMAL_MIN__=0.000000000000001E-383DD"/>
<definedSymbol symbol="__DEC128_MANT_DIG__=34"/>
<definedSymbol symbol="__DEC128_MIN_EXP__=(-6142)"/>
<definedSymbol symbol="__DEC128_MAX_EXP__=6145"/>
<definedSymbol symbol="__DEC128_MIN__=1E-6143DL"/>
<definedSymbol symbol="__DEC128_MAX__=9.999999999999999999999999999999999E6144DL"/>
<definedSymbol symbol="__DEC128_EPSILON__=1E-33DL"/>
<definedSymbol symbol="__DEC128_SUBNORMAL_MIN__=0.000000000000000000000000000000001E-6143DL"/>
<definedSymbol symbol="__REGISTER_PREFIX__="/>
<definedSymbol symbol="__USER_LABEL_PREFIX__=_"/>
<definedSymbol symbol="__GNUC_GNU_INLINE__=1"/>
<definedSymbol symbol="__NO_INLINE__=1"/>
<definedSymbol symbol="__WCHAR_UNSIGNED__=1"/>
<definedSymbol symbol="__GCC_HAVE_SYNC_COMPARE_AND_SWAP_1=1"/>
<definedSymbol symbol="__GCC_HAVE_SYNC_COMPARE_AND_SWAP_2=1"/>
<definedSymbol symbol="__GCC_HAVE_SYNC_COMPARE_AND_SWAP_4=1"/>
<definedSymbol symbol="__GCC_HAVE_SYNC_COMPARE_AND_SWAP_8=1"/>
<definedSymbol symbol="__GCC_ATOMIC_BOOL_LOCK_FREE=2"/>
<definedSymbol symbol="__GCC_ATOMIC_CHAR_LOCK_FREE=2"/>
<definedSymbol symbol="__GCC_ATOMIC_CHAR16_T_LOCK_FREE=2"/>
<definedSymbol symbol="__GCC_ATOMIC_CHAR32_T_LOCK_FREE=2"/>
<definedSymbol symbol="__GCC_ATOMIC_WCHAR_T_LOCK_FREE=2"/>
<definedSymbol symbol="__GCC_ATOMIC_SHORT_LOCK_FREE=2"/>
<definedSymbol symbol="__GCC_ATOMIC_INT_LOCK_FREE=2"/>
<definedSymbol symbol="__GCC_ATOMIC_LONG_LOCK_FREE=2"/>
<definedSymbol symbol="__GCC_ATOMIC_LLONG_LOCK_FREE=2"/>
<definedSymbol symbol="__GCC_ATOMIC_TEST_AND_SET_TRUEVAL=1"/>
<definedSymbol symbol="__GCC_ATOMIC_POINTER_LOCK_FREE=2"/>
<definedSymbol symbol="__GCC_HAVE_DWARF2_CFI_ASM=1"/>
<definedSymbol symbol="__PRAGMA_REDEFINE_EXTNAME=1"/>
<definedSymbol symbol="__SIZEOF_WCHAR_T__=2"/>
<definedSymbol symbol="__SIZEOF_WINT_T__=2"/>
<definedSymbol symbol="__SIZEOF_PTRDIFF_T__=4"/>
<definedSymbol symbol="__i386=1"/>
<definedSymbol symbol="__i386__=1"/>
<definedSymbol symbol="i386=1"/>
<definedSymbol symbol="__ATOMIC_HLE_ACQUIRE=65536"/>
<definedSymbol symbol="__ATOMIC_HLE_RELEASE=131072"/>
<definedSymbol symbol="__i686=1"/>
<definedSymbol symbol="__i686__=1"/>
<definedSymbol symbol="__pentiumpro=1"/>
<definedSymbol symbol="__pentiumpro__=1"/>
<definedSymbol symbol="__code_model_32__=1"/>
<definedSymbol symbol="_X86_=1"/>
<definedSymbol symbol="__stdcall=__attribute__((__stdcall__))"/>
<definedSymbol symbol="__fastcall=__attribute__((__fastcall__))"/>
<definedSymbol symbol="__thiscall=__attribute__((__thiscall__))"/>
<definedSymbol symbol="__cdecl=__attribute__((__cdecl__))"/>
<definedSymbol symbol="_stdcall=__attribute__((__stdcall__))"/>
<definedSymbol symbol="_fastcall=__attribute__((__fastcall__))"/>
<definedSymbol symbol="_thiscall=__attribute__((__thiscall__))"/>
<definedSymbol symbol="_cdecl=__attribute__((__cdecl__))"/>
<definedSymbol symbol="__GXX_MERGED_TYPEINFO_NAMES=0"/>
<definedSymbol symbol="__GXX_TYPEINFO_EQUALITY_INLINE=0"/>
<definedSymbol symbol="__MSVCRT__=1"/>
<definedSymbol symbol="__MINGW32__=1"/>
<definedSymbol symbol="_WIN32=1"/>
<definedSymbol symbol="__WIN32=1"/>
<definedSymbol symbol="__WIN32__=1"/>
<definedSymbol symbol="WIN32=1"/>
<definedSymbol symbol="__WINNT=1"/>
<definedSymbol symbol="__WINNT__=1"/>
<definedSymbol symbol="WINNT=1"/>
<definedSymbol symbol="_INTEGRAL_MAX_BITS=64"/>
<definedSymbol symbol="__declspec(x)=__attribute__((x))"/>
<definedSymbol symbol="__DECIMAL_BID_FORMAT__=1"/>
<definedSymbol symbol="_REENTRANT=1"/>
</collector>
</instance>
</scannerInfo>
