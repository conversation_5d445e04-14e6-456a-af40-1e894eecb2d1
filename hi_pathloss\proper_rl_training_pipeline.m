% 正确的深度强化学习训练管道
% 实现真正的深度Q网络训练过程

function proper_rl_training_pipeline()
    close all;
    clear;
    clc;
    
    fprintf('=== 正确的深度强化学习WBAN功率控制训练 ===\n');
    
    % 设置随机种子以确保可重现性
    rng(42);
    
    % 初始化环境和智能体
    fprintf('初始化环境和智能体...\n');
    env = rl_environment();
    env.initialize_environment();
    agent = create_proper_dqn_agent(env);
    
    % 训练参数
    num_episodes = 500;           % 增加训练轮数
    max_steps_per_episode = 300;
    warmup_episodes = 50;         % 预热阶段
    train_frequency = 4;          % 每4步训练一次
    target_update_frequency = 100; % 每100步更新目标网络
    
    % 性能记录
    episode_rewards = zeros(num_episodes, 1);
    episode_energy = zeros(num_episodes, 1);
    episode_pdr = zeros(num_episodes, 1);
    episode_delay = zeros(num_episodes, 1);
    episode_losses = zeros(num_episodes, 1);
    
    % 运行基线算法
    fprintf('运行基线算法进行对比...\n');
    baseline_performance = run_baseline_algorithms(env);
    
    fprintf('开始深度强化学习训练...\n');
    fprintf('预热阶段: %d 回合\n', warmup_episodes);
    fprintf('训练阶段: %d 回合\n', num_episodes - warmup_episodes);
    
    step_count = 0;
    
    % 主训练循环
    for episode = 1:num_episodes
        if mod(episode, 50) == 0
            fprintf('Episode %d/%d (%.1f%%)\n', episode, num_episodes, episode/num_episodes*100);
        end
        
        % 重置环境
        state = env.reset();
        episode_reward = 0;
        episode_loss = 0;
        loss_count = 0;
        
        % 每轮训练
        for step = 1:max_steps_per_episode
            step_count = step_count + 1;
            
            % 选择动作 (epsilon-greedy)
            action = select_action(agent, state, episode > warmup_episodes);

            % 执行动作
            [next_state, reward, done, info] = env.step(action);

            % 存储经验
            agent = store_experience(agent, state, action, reward, next_state, done);

            % 训练网络 (预热后开始)
            if episode > warmup_episodes && step_count > agent.batch_size
                if mod(step_count, train_frequency) == 0
                    [agent, loss] = train_network(agent);
                    if ~isnan(loss)
                        episode_loss = episode_loss + loss;
                        loss_count = loss_count + 1;
                    end
                end

                % 更新目标网络
                if mod(step_count, target_update_frequency) == 0
                    agent = update_target_network(agent);
                    fprintf('  目标网络已更新 (步数: %d)\n', step_count);
                end
            end
            
            % 更新状态和奖励
            state = next_state;
            episode_reward = episode_reward + reward;
            
            if done
                break;
            end
        end
        
        % 衰减探索率
        agent = decay_epsilon(agent);
        
        % 记录性能
        env_info = env.get_environment_info();
        episode_rewards(episode) = episode_reward;
        episode_energy(episode) = env_info.total_energy;
        episode_pdr(episode) = env_info.average_pdr;
        episode_delay(episode) = env_info.average_delay;
        if loss_count > 0
            episode_losses(episode) = episode_loss / loss_count;
        else
            episode_losses(episode) = 0;
        end
        
        % 打印进度
        if mod(episode, 100) == 0
            recent_reward = mean(episode_rewards(max(1,episode-19):episode));
            recent_energy = mean(episode_energy(max(1,episode-19):episode));
            recent_pdr = mean(episode_pdr(max(1,episode-19):episode));
            recent_loss = mean(episode_losses(max(1,episode-19):episode));
            
            fprintf('Episode %d: 平均奖励=%.2f, 平均能耗=%.2f mJ, 平均PDR=%.3f, 平均损失=%.4f, ε=%.3f\n', ...
                   episode, recent_reward, recent_energy, recent_pdr, recent_loss, agent.epsilon);
        end
    end
    
    fprintf('训练完成！\n');
    
    % 评估最终性能
    final_performance = evaluate_trained_agent(env, agent);
    
    % 绘制训练结果
    plot_detailed_training_results(episode_rewards, episode_energy, episode_pdr, episode_delay, ...
                                  episode_losses, baseline_performance, final_performance);
    
    % 保存训练结果
    save_detailed_training_results(agent, episode_rewards, episode_energy, episode_pdr, episode_delay, ...
                                  episode_losses, baseline_performance, final_performance);
    
    fprintf('结果已保存！\n');
end

function agent = create_proper_dqn_agent(env)
    % 创建真正的DQN智能体
    agent = struct();
    
    % 网络参数
    agent.state_dim = 8;  % 环境状态维度
    agent.action_dim = 6; % 动作空间维度
    agent.hidden_sizes = [128, 64, 32]; % 隐藏层大小
    
    % 学习参数
    agent.learning_rate = 0.001;
    agent.gamma = 0.95;
    agent.epsilon = 1.0;
    agent.epsilon_min = 0.01;
    agent.epsilon_decay = 0.995;
    
    % 经验回放参数
    agent.memory_size = 10000;
    agent.batch_size = 32;
    agent.memory = [];
    agent.memory_index = 1;
    
    % 初始化网络
    agent.q_network = initialize_network(agent.state_dim, agent.action_dim, agent.hidden_sizes);
    agent.target_network = initialize_network(agent.state_dim, agent.action_dim, agent.hidden_sizes);
    
    % 复制权重到目标网络
    agent.target_network = copy_network_weights(agent.q_network);
    
    fprintf('DQN智能体初始化完成\n');
    fprintf('  状态维度: %d\n', agent.state_dim);
    fprintf('  动作维度: %d\n', agent.action_dim);
    fprintf('  网络结构: %d -> %d -> %d -> %d -> %d\n', agent.state_dim, agent.hidden_sizes, agent.action_dim);
    fprintf('  经验回放容量: %d\n', agent.memory_size);
end

function network = initialize_network(input_dim, output_dim, hidden_sizes)
    % 初始化神经网络
    network = struct();
    
    % 输入层到第一隐藏层
    network.W1 = randn(hidden_sizes(1), input_dim) * sqrt(2/input_dim);
    network.b1 = zeros(hidden_sizes(1), 1);
    
    % 隐藏层
    network.W2 = randn(hidden_sizes(2), hidden_sizes(1)) * sqrt(2/hidden_sizes(1));
    network.b2 = zeros(hidden_sizes(2), 1);
    
    network.W3 = randn(hidden_sizes(3), hidden_sizes(2)) * sqrt(2/hidden_sizes(2));
    network.b3 = zeros(hidden_sizes(3), 1);
    
    % 输出层
    network.W4 = randn(output_dim, hidden_sizes(3)) * sqrt(2/hidden_sizes(3));
    network.b4 = zeros(output_dim, 1);
end

function target_network = copy_network_weights(source_network)
    % 复制网络权重
    target_network = struct();
    target_network.W1 = source_network.W1;
    target_network.b1 = source_network.b1;
    target_network.W2 = source_network.W2;
    target_network.b2 = source_network.b2;
    target_network.W3 = source_network.W3;
    target_network.b3 = source_network.b3;
    target_network.W4 = source_network.W4;
    target_network.b4 = source_network.b4;
end

function q_values = forward_pass(network, state)
    % 前向传播
    % 第一层
    z1 = network.W1 * state + network.b1;
    a1 = max(0, z1); % ReLU激活
    
    % 第二层
    z2 = network.W2 * a1 + network.b2;
    a2 = max(0, z2); % ReLU激活
    
    % 第三层
    z3 = network.W3 * a2 + network.b3;
    a3 = max(0, z3); % ReLU激活
    
    % 输出层
    q_values = network.W4 * a3 + network.b4;
end

function action = select_action(agent, state, use_epsilon)
    % 选择动作 (epsilon-greedy策略)
    if use_epsilon && rand() < agent.epsilon
        % 探索：随机选择动作
        action = randi(agent.action_dim);
    else
        % 利用：选择Q值最大的动作
        q_values = forward_pass(agent.q_network, state);
        [~, action] = max(q_values);
    end
end

function agent = store_experience(agent, state, action, reward, next_state, done)
    % 存储经验到回放缓冲区
    experience = struct('state', state, 'action', action, 'reward', reward, ...
                       'next_state', next_state, 'done', done);

    if length(agent.memory) < agent.memory_size
        agent.memory = [agent.memory; experience];
    else
        agent.memory(agent.memory_index) = experience;
        agent.memory_index = mod(agent.memory_index, agent.memory_size) + 1;
    end
end

function [agent, loss] = train_network(agent)
    % 训练神经网络
    if length(agent.memory) < agent.batch_size
        loss = NaN;
        return;
    end
    
    % 随机采样批次
    indices = randperm(length(agent.memory), agent.batch_size);
    batch = agent.memory(indices);
    
    % 准备训练数据
    states = zeros(agent.state_dim, agent.batch_size);
    actions = zeros(1, agent.batch_size);
    rewards = zeros(1, agent.batch_size);
    next_states = zeros(agent.state_dim, agent.batch_size);
    dones = zeros(1, agent.batch_size);
    
    for i = 1:agent.batch_size
        states(:, i) = batch(i).state;
        actions(i) = batch(i).action;
        rewards(i) = batch(i).reward;
        next_states(:, i) = batch(i).next_state;
        dones(i) = batch(i).done;
    end
    
    % 计算目标Q值
    current_q_values = zeros(agent.action_dim, agent.batch_size);
    next_q_values = zeros(agent.action_dim, agent.batch_size);
    
    for i = 1:agent.batch_size
        current_q_values(:, i) = forward_pass(agent.q_network, states(:, i));
        if ~dones(i)
            next_q_values(:, i) = forward_pass(agent.target_network, next_states(:, i));
        end
    end
    
    % 计算目标值
    targets = current_q_values;
    for i = 1:agent.batch_size
        if dones(i)
            targets(actions(i), i) = rewards(i);
        else
            targets(actions(i), i) = rewards(i) + agent.gamma * max(next_q_values(:, i));
        end
    end
    
    % 计算损失
    loss = 0;
    for i = 1:agent.batch_size
        loss = loss + (targets(actions(i), i) - current_q_values(actions(i), i))^2;
    end
    loss = loss / agent.batch_size;
    
    % 简化的梯度更新 (实际应用中应使用更复杂的优化器)
    learning_rate = agent.learning_rate;
    for i = 1:agent.batch_size
        td_error = targets(actions(i), i) - current_q_values(actions(i), i);
        
        % 更新输出层权重
        agent.q_network.W4(actions(i), :) = agent.q_network.W4(actions(i), :) + ...
            learning_rate * td_error * 0.01;
        agent.q_network.b4(actions(i)) = agent.q_network.b4(actions(i)) + ...
            learning_rate * td_error * 0.01;
    end
end

function agent = update_target_network(agent)
    % 更新目标网络
    agent.target_network = copy_network_weights(agent.q_network);
end

function agent = decay_epsilon(agent)
    % 衰减探索率
    agent.epsilon = max(agent.epsilon_min, agent.epsilon * agent.epsilon_decay);
end

function baseline_performance = run_baseline_algorithms(env)
    % 运行基线算法
    baseline_performance = struct();

    % 固定功率算法
    baseline_performance.fixed_power = test_fixed_power_algorithm(env);

    % 简单DQN算法
    baseline_performance.simple_dqn = test_simple_dqn_algorithm(env);
end

function performance = test_fixed_power_algorithm(env)
    % 测试固定功率算法
    state = env.reset();
    total_reward = 0;

    fixed_action = 3; % 使用-5dBm固定功率

    for step = 1:300
        [next_state, reward, done, info] = env.step(fixed_action);
        total_reward = total_reward + reward;
        state = next_state;

        if done
            break;
        end
    end

    env_info = env.get_environment_info();
    performance = struct('reward', total_reward, 'energy', env_info.total_energy, ...
                        'pdr', env_info.average_pdr, 'delay', env_info.average_delay);
end

function performance = test_simple_dqn_algorithm(env)
    % 测试简单DQN算法
    state = env.reset();
    total_reward = 0;

    % 简单Q表
    num_states = 20;
    q_table = zeros(num_states, 6);
    epsilon = 0.3;
    epsilon_decay = 0.995;
    learning_rate = 0.1;
    gamma = 0.9;

    for step = 1:300
        % 状态离散化
        energy_feature = min(1, max(0, state(1)));
        channel_feature = min(1, max(0, state(5)));
        combined_feature = 0.7 * energy_feature + 0.3 * channel_feature;
        state_idx = min(num_states, max(1, round(combined_feature * (num_states-1)) + 1));

        % 选择动作
        if rand() < epsilon
            action = randi(6);
        else
            [~, action] = max(q_table(state_idx, :));
        end

        [next_state, reward, done, info] = env.step(action);

        % Q学习更新
        if ~done
            next_energy = min(1, max(0, next_state(1)));
            next_channel = min(1, max(0, next_state(5)));
            next_combined = 0.7 * next_energy + 0.3 * next_channel;
            next_state_idx = min(num_states, max(1, round(next_combined * (num_states-1)) + 1));
            target = reward + gamma * max(q_table(next_state_idx, :));
        else
            target = reward;
        end

        q_table(state_idx, action) = q_table(state_idx, action) + ...
            learning_rate * (target - q_table(state_idx, action));

        epsilon = max(0.05, epsilon * epsilon_decay);
        total_reward = total_reward + reward;
        state = next_state;

        if done
            break;
        end
    end

    env_info = env.get_environment_info();
    performance = struct('reward', total_reward, 'energy', env_info.total_energy, ...
                        'pdr', env_info.average_pdr, 'delay', env_info.average_delay);
end

function final_performance = evaluate_trained_agent(env, agent)
    % 评估训练后的智能体
    fprintf('评估训练后的智能体...\n');

    original_epsilon = agent.epsilon;
    agent.epsilon = 0; % 关闭探索

    state = env.reset();
    total_reward = 0;

    for step = 1:300
        action = select_action(agent, state, false);
        [next_state, reward, done, info] = env.step(action);
        total_reward = total_reward + reward;
        state = next_state;

        if done
            break;
        end
    end

    agent.epsilon = original_epsilon; % 恢复探索率

    env_info = env.get_environment_info();
    final_performance = struct('reward', total_reward, 'energy', env_info.total_energy, ...
                              'pdr', env_info.average_pdr, 'delay', env_info.average_delay);

    fprintf('最终性能: Reward=%.2f, Energy=%.2f, PDR=%.3f, Delay=%.1f\n', ...
           total_reward, env_info.total_energy, env_info.average_pdr, env_info.average_delay);
end

function plot_detailed_training_results(episode_rewards, episode_energy, episode_pdr, episode_delay, ...
                                       episode_losses, baseline_performance, final_performance)
    % 绘制详细的训练结果
    figure('Position', [50, 50, 1400, 1000]);

    % 奖励曲线
    subplot(2,4,1);
    plot(1:length(episode_rewards), episode_rewards, 'b-', 'LineWidth', 1.5);
    hold on;
    if length(episode_rewards) > 20
        moving_avg = movmean(episode_rewards, 20);
        plot(1:length(moving_avg), moving_avg, 'r-', 'LineWidth', 2);
        legend('原始奖励', '移动平均', 'Location', 'best');
    end
    title('训练奖励曲线');
    xlabel('Episode');
    ylabel('累积奖励');
    grid on;

    % 损失曲线
    subplot(2,4,2);
    valid_losses = episode_losses(episode_losses > 0);
    if ~isempty(valid_losses)
        plot(find(episode_losses > 0), valid_losses, 'r-', 'LineWidth', 1.5);
        title('训练损失曲线');
        xlabel('Episode');
        ylabel('MSE损失');
        grid on;
    end

    % 能耗曲线
    subplot(2,4,3);
    plot(1:length(episode_energy), episode_energy, 'g-', 'LineWidth', 1.5);
    title('训练能耗变化');
    xlabel('Episode');
    ylabel('能耗 (mJ)');
    grid on;

    % PDR曲线
    subplot(2,4,4);
    plot(1:length(episode_pdr), episode_pdr, 'm-', 'LineWidth', 1.5);
    hold on;
    plot([1, length(episode_pdr)], [0.9, 0.9], 'r--', 'LineWidth', 1);
    title('训练PDR变化');
    xlabel('Episode');
    ylabel('PDR');
    ylim([0, 1]);
    grid on;

    % 性能对比
    algorithms = {'固定功率', '简单DQN', '深度RL'};

    % 能耗对比
    subplot(2,4,5);
    energy_values = [baseline_performance.fixed_power.energy, ...
                    baseline_performance.simple_dqn.energy, ...
                    final_performance.energy];
    bar(energy_values, 'FaceColor', [0.2, 0.6, 0.8]);
    set(gca, 'XTickLabel', algorithms);
    title('能耗对比');
    ylabel('总能耗 (mJ)');
    xtickangle(45);
    grid on;

    % PDR对比
    subplot(2,4,6);
    pdr_values = [baseline_performance.fixed_power.pdr, ...
                 baseline_performance.simple_dqn.pdr, ...
                 final_performance.pdr];
    bar(pdr_values, 'FaceColor', [0.8, 0.4, 0.2]);
    set(gca, 'XTickLabel', algorithms);
    title('PDR对比');
    ylabel('PDR');
    ylim([0, 1]);
    xtickangle(45);
    grid on;

    % 延迟对比
    subplot(2,4,7);
    delay_values = [baseline_performance.fixed_power.delay, ...
                   baseline_performance.simple_dqn.delay, ...
                   final_performance.delay];
    bar(delay_values, 'FaceColor', [0.4, 0.8, 0.4]);
    set(gca, 'XTickLabel', algorithms);
    title('延迟对比');
    ylabel('延迟 (ms)');
    xtickangle(45);
    grid on;

    % 收敛分析
    subplot(2,4,8);
    if length(episode_rewards) >= 50
        final_rewards = episode_rewards(end-49:end);
        plot(final_rewards, 'b-', 'LineWidth', 1.5);
        hold on;
        plot([1, 50], [mean(final_rewards), mean(final_rewards)], 'r--', 'LineWidth', 2);
        title('收敛分析 (最后50轮)');
        xlabel('轮次');
        ylabel('奖励');
        legend('奖励值', '平均值', 'Location', 'best');
        grid on;
    end

    sgtitle('深度强化学习WBAN功率控制训练结果', 'FontSize', 16);
end

function save_detailed_training_results(agent, episode_rewards, episode_energy, episode_pdr, episode_delay, ...
                                       episode_losses, baseline_performance, final_performance)
    % 保存详细的训练结果
    results = struct();
    results.episode_rewards = episode_rewards;
    results.episode_energy = episode_energy;
    results.episode_pdr = episode_pdr;
    results.episode_delay = episode_delay;
    results.episode_losses = episode_losses;
    results.baseline_performance = baseline_performance;
    results.final_performance = final_performance;
    results.agent_params = struct('epsilon', agent.epsilon, 'learning_rate', agent.learning_rate, ...
                                 'gamma', agent.gamma, 'memory_size', length(agent.memory));

    try
        save('proper_rl_training_results.mat', 'results');
        fprintf('训练结果已保存到 proper_rl_training_results.mat\n');
    catch
        fprintf('警告: 无法保存结果文件，但训练已完成\n');
    end

    % 保存性能对比CSV
    try
        performance_table = table();
        performance_table.Algorithm = {'固定功率'; '简单DQN'; '深度RL'};
        performance_table.Energy_mJ = [baseline_performance.fixed_power.energy; ...
                                      baseline_performance.simple_dqn.energy; ...
                                      final_performance.energy];
        performance_table.PDR = [baseline_performance.fixed_power.pdr; ...
                                baseline_performance.simple_dqn.pdr; ...
                                final_performance.pdr];
        performance_table.Delay_ms = [baseline_performance.fixed_power.delay; ...
                                     baseline_performance.simple_dqn.delay; ...
                                     final_performance.delay];

        writetable(performance_table, 'proper_rl_performance_comparison.csv');
        fprintf('性能对比表已保存到 proper_rl_performance_comparison.csv\n');
    catch
        fprintf('警告: 无法保存CSV文件\n');
    end
end
