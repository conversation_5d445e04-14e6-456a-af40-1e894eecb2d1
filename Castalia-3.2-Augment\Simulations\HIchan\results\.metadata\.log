!SESSION 2025-06-08 09:27:48.512 -----------------------------------------------
eclipse.buildId=unknown
java.version=1.7.0_51
java.vendor=Oracle Corporation
BootLoader constants: OS=win32, ARCH=x86, WS=win32, NL=zh_CN
Command-line arguments:  -os win32 -ws win32 -arch x86

!ENTRY org.omnetpp.main 1 0 2025-06-08 09:28:18.921
!MESSAGE OMNeT++ IDE 4.6.0.141202-f785492 started.

!ENTRY org.omnetpp.main 4 0 2025-06-08 09:29:20.016
!MESSAGE Cannot download project description from http://omnetpp.org/download/contrib/descriptors/omnetpp-4.6/inet.xml
!STACK 0
java.io.IOException: Server returned HTTP response code: 403 for URL: http://omnetpp.org/download/contrib/descriptors/omnetpp-4.6/inet.xml
	at sun.net.www.protocol.http.HttpURLConnection.getInputStream(Unknown Source)
	at java.net.URL.openStream(Unknown Source)
	at org.apache.commons.io.FileUtils.copyURLToFile(FileUtils.java:1460)
	at org.omnetpp.ide.installer.InstallProjectTask.downloadProjectDescription(InstallProjectTask.java:59)
	at org.omnetpp.ide.installer.InstallProjectTask.run(InstallProjectTask.java:46)
	at org.omnetpp.ide.installer.InstallProjectJob.run(InstallProjectJob.java:32)
	at org.eclipse.core.internal.jobs.Worker.run(Worker.java:54)

!ENTRY org.eclipse.cdt.core 1 0 2025-06-08 09:31:34.067
!MESSAGE Indexed 'google-earth' (5 sources, 349 headers) in 4.1 sec: 42,756 declarations; 66,899 references; 0 unresolved inclusions; 0 syntax errors; 8 unresolved names (0.0073%)

!ENTRY org.eclipse.cdt.ui 2 0 2025-06-08 09:36:58.551
!MESSAGE Default project not found in New C/C++ Project Wizard

!ENTRY org.eclipse.cdt.core 1 0 2025-06-08 09:36:59.416
!MESSAGE Indexed 'Castalia1304' (0 sources, 0 headers) in 0.001 sec: 0 declarations; 0 references; 0 unresolved inclusions; 0 syntax errors; 0 unresolved names (0%)

!ENTRY org.omnetpp.scave 2 0 2025-06-08 09:38:22.852
!MESSAGE No Scave editor state was saved for file: C:\Castalia-3.2\Simulations\HIchan\results\Castalia1304\TMAC1304.anf.

!ENTRY org.eclipse.cdt.core 1 0 2025-06-08 09:39:31.812
!MESSAGE Indexed 'tictoc' (20 sources, 336 headers) in 3.58 sec: 43,175 declarations; 68,843 references; 0 unresolved inclusions; 0 syntax errors; 8 unresolved names (0.0071%)

!ENTRY org.omnetpp.scave 2 0 2025-06-08 09:39:37.635
!MESSAGE No Scave editor state was saved for file: C:\omnetpp-4.6\samples\tictoc\Tictoc16.anf.

!ENTRY org.eclipse.e4.ui.workbench.swt 4 2 2025-06-08 11:09:33.922
!MESSAGE Problems occurred when invoking code from plug-in: "org.eclipse.e4.ui.workbench.swt".
!STACK 0
org.eclipse.e4.core.di.InjectionException: org.eclipse.swt.SWTException: Widget is disposed
	at org.eclipse.e4.core.internal.di.MethodRequestor.execute(MethodRequestor.java:62)
	at org.eclipse.e4.core.internal.di.InjectorImpl.processAnnotated(InjectorImpl.java:888)
	at org.eclipse.e4.core.internal.di.InjectorImpl.disposed(InjectorImpl.java:390)
	at org.eclipse.e4.core.internal.di.Requestor.disposed(Requestor.java:143)
	at org.eclipse.e4.core.internal.contexts.ContextObjectSupplier$ContextInjectionListener.update(ContextObjectSupplier.java:76)
	at org.eclipse.e4.core.internal.contexts.TrackableComputationExt.update(TrackableComputationExt.java:107)
	at org.eclipse.e4.core.internal.contexts.TrackableComputationExt.handleInvalid(TrackableComputationExt.java:70)
	at org.eclipse.e4.core.internal.contexts.EclipseContext.dispose(EclipseContext.java:175)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.clearContext(PartRenderingEngine.java:974)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.safeRemoveGui(PartRenderingEngine.java:954)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.access$3(PartRenderingEngine.java:862)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine$8.run(PartRenderingEngine.java:857)
	at org.eclipse.core.runtime.SafeRunner.run(SafeRunner.java:42)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.removeGui(PartRenderingEngine.java:841)
	at org.eclipse.ui.internal.WorkbenchWindow.hardClose(WorkbenchWindow.java:1924)
	at org.eclipse.ui.internal.WorkbenchWindow.busyClose(WorkbenchWindow.java:1547)
	at org.eclipse.ui.internal.WorkbenchWindow.access$15(WorkbenchWindow.java:1514)
	at org.eclipse.ui.internal.WorkbenchWindow$10.run(WorkbenchWindow.java:1579)
	at org.eclipse.swt.custom.BusyIndicator.showWhile(BusyIndicator.java:70)
	at org.eclipse.ui.internal.WorkbenchWindow.close(WorkbenchWindow.java:1576)
	at org.eclipse.ui.internal.Workbench$14.run(Workbench.java:1155)
	at org.eclipse.core.runtime.SafeRunner.run(SafeRunner.java:42)
	at org.eclipse.ui.internal.Workbench.busyClose(Workbench.java:1137)
	at org.eclipse.ui.internal.Workbench.access$21(Workbench.java:1079)
	at org.eclipse.ui.internal.Workbench$19.run(Workbench.java:1410)
	at org.eclipse.swt.custom.BusyIndicator.showWhile(BusyIndicator.java:70)
	at org.eclipse.ui.internal.Workbench.close(Workbench.java:1407)
	at org.eclipse.ui.internal.Workbench.close(Workbench.java:1380)
	at org.eclipse.ui.internal.WorkbenchWindow.busyClose(WorkbenchWindow.java:1543)
	at org.eclipse.ui.internal.WorkbenchWindow.access$15(WorkbenchWindow.java:1514)
	at org.eclipse.ui.internal.WorkbenchWindow$10.run(WorkbenchWindow.java:1579)
	at org.eclipse.swt.custom.BusyIndicator.showWhile(BusyIndicator.java:70)
	at org.eclipse.ui.internal.WorkbenchWindow.close(WorkbenchWindow.java:1576)
	at org.eclipse.ui.internal.WorkbenchWindow.close(WorkbenchWindow.java:1590)
	at org.eclipse.ui.internal.WorkbenchWindow$6.close(WorkbenchWindow.java:520)
	at org.eclipse.e4.ui.workbench.renderers.swt.WBWRenderer$11.shellClosed(WBWRenderer.java:564)
	at org.eclipse.swt.widgets.TypedListener.handleEvent(TypedListener.java:98)
	at org.eclipse.swt.widgets.EventTable.sendEvent(EventTable.java:84)
	at org.eclipse.swt.widgets.Display.sendEvent(Display.java:4353)
	at org.eclipse.swt.widgets.Widget.sendEvent(Widget.java:1061)
	at org.eclipse.swt.widgets.Widget.sendEvent(Widget.java:1085)
	at org.eclipse.swt.widgets.Widget.sendEvent(Widget.java:1070)
	at org.eclipse.swt.widgets.Decorations.closeWidget(Decorations.java:308)
	at org.eclipse.swt.widgets.Decorations.WM_CLOSE(Decorations.java:1696)
	at org.eclipse.swt.widgets.Control.windowProc(Control.java:4612)
	at org.eclipse.swt.widgets.Canvas.windowProc(Canvas.java:339)
	at org.eclipse.swt.widgets.Decorations.windowProc(Decorations.java:1626)
	at org.eclipse.swt.widgets.Shell.windowProc(Shell.java:2075)
	at org.eclipse.swt.widgets.Display.windowProc(Display.java:5023)
	at org.eclipse.swt.internal.win32.OS.DefWindowProcW(Native Method)
	at org.eclipse.swt.internal.win32.OS.DefWindowProc(OS.java:2544)
	at org.eclipse.swt.widgets.Shell.callWindowProc(Shell.java:498)
	at org.eclipse.swt.widgets.Control.windowProc(Control.java:4705)
	at org.eclipse.swt.widgets.Canvas.windowProc(Canvas.java:339)
	at org.eclipse.swt.widgets.Decorations.windowProc(Decorations.java:1626)
	at org.eclipse.swt.widgets.Shell.windowProc(Shell.java:2075)
	at org.eclipse.swt.widgets.Display.windowProc(Display.java:5023)
	at org.eclipse.swt.internal.win32.OS.DefWindowProcW(Native Method)
	at org.eclipse.swt.internal.win32.OS.DefWindowProc(OS.java:2544)
	at org.eclipse.swt.widgets.Shell.callWindowProc(Shell.java:498)
	at org.eclipse.swt.widgets.Control.windowProc(Control.java:4705)
	at org.eclipse.swt.widgets.Canvas.windowProc(Canvas.java:339)
	at org.eclipse.swt.widgets.Decorations.windowProc(Decorations.java:1626)
	at org.eclipse.swt.widgets.Shell.windowProc(Shell.java:2075)
	at org.eclipse.swt.widgets.Display.windowProc(Display.java:5023)
	at org.eclipse.swt.internal.win32.OS.DispatchMessageW(Native Method)
	at org.eclipse.swt.internal.win32.OS.DispatchMessage(OS.java:2549)
	at org.eclipse.swt.widgets.Display.readAndDispatch(Display.java:3759)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine$9.run(PartRenderingEngine.java:1151)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:332)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.run(PartRenderingEngine.java:1032)
	at org.eclipse.e4.ui.internal.workbench.E4Workbench.createAndRunUI(E4Workbench.java:148)
	at org.eclipse.ui.internal.Workbench$5.run(Workbench.java:636)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:332)
	at org.eclipse.ui.internal.Workbench.createAndRunWorkbench(Workbench.java:579)
	at org.eclipse.ui.PlatformUI.createAndRunWorkbench(PlatformUI.java:150)
	at org.eclipse.ui.internal.ide.application.IDEApplication.start(IDEApplication.java:135)
	at org.eclipse.equinox.internal.app.EclipseAppHandle.run(EclipseAppHandle.java:196)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.runApplication(EclipseAppLauncher.java:134)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.start(EclipseAppLauncher.java:104)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:382)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:236)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.eclipse.equinox.launcher.Main.invokeFramework(Main.java:648)
	at org.eclipse.equinox.launcher.Main.basicRun(Main.java:603)
	at org.eclipse.equinox.launcher.Main.run(Main.java:1465)
Caused by: org.eclipse.swt.SWTException: Widget is disposed
	at org.eclipse.swt.SWT.error(SWT.java:4441)
	at org.eclipse.swt.SWT.error(SWT.java:4356)
	at org.eclipse.swt.SWT.error(SWT.java:4327)
	at org.eclipse.swt.widgets.Widget.error(Widget.java:476)
	at org.eclipse.swt.widgets.Widget.checkWidget(Widget.java:348)
	at org.eclipse.swt.widgets.Shell.getSize(Shell.java:1092)
	at org.eclipse.ui.internal.quickaccess.SearchField.storeDialog(SearchField.java:580)
	at org.eclipse.ui.internal.quickaccess.SearchField.dispose(SearchField.java:557)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.eclipse.e4.core.internal.di.MethodRequestor.execute(MethodRequestor.java:55)
	... 88 more

!ENTRY org.eclipse.e4.ui.workbench 4 0 2025-06-08 11:09:33.924
!MESSAGE Exception occurred while unrendering: org.eclipse.e4.ui.model.application.ui.basic.impl.TrimmedWindowImpl@d7c572 (elementId: IDEWindow, tags: [topLevel, shellMaximized], contributorURI: platform:/plugin/org.eclipse.ui.workbench) (widget: null, renderer: null, toBeRendered: true, onTop: false, visible: true, containerData: null, accessibilityPhrase: null) (label: %trimmedwindow.label.eclipseSDK, iconURI: null, tooltip: null, context: null, variables: [], x: 192, y: 192, width: 1024, height: 768)
!STACK 0
org.eclipse.e4.core.di.InjectionException: org.eclipse.swt.SWTException: Widget is disposed
	at org.eclipse.e4.core.internal.di.MethodRequestor.execute(MethodRequestor.java:62)
	at org.eclipse.e4.core.internal.di.InjectorImpl.processAnnotated(InjectorImpl.java:888)
	at org.eclipse.e4.core.internal.di.InjectorImpl.disposed(InjectorImpl.java:390)
	at org.eclipse.e4.core.internal.di.Requestor.disposed(Requestor.java:143)
	at org.eclipse.e4.core.internal.contexts.ContextObjectSupplier$ContextInjectionListener.update(ContextObjectSupplier.java:76)
	at org.eclipse.e4.core.internal.contexts.TrackableComputationExt.update(TrackableComputationExt.java:107)
	at org.eclipse.e4.core.internal.contexts.TrackableComputationExt.handleInvalid(TrackableComputationExt.java:70)
	at org.eclipse.e4.core.internal.contexts.EclipseContext.dispose(EclipseContext.java:175)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.clearContext(PartRenderingEngine.java:974)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.safeRemoveGui(PartRenderingEngine.java:954)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.access$3(PartRenderingEngine.java:862)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine$8.run(PartRenderingEngine.java:857)
	at org.eclipse.core.runtime.SafeRunner.run(SafeRunner.java:42)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.removeGui(PartRenderingEngine.java:841)
	at org.eclipse.ui.internal.WorkbenchWindow.hardClose(WorkbenchWindow.java:1924)
	at org.eclipse.ui.internal.WorkbenchWindow.busyClose(WorkbenchWindow.java:1547)
	at org.eclipse.ui.internal.WorkbenchWindow.access$15(WorkbenchWindow.java:1514)
	at org.eclipse.ui.internal.WorkbenchWindow$10.run(WorkbenchWindow.java:1579)
	at org.eclipse.swt.custom.BusyIndicator.showWhile(BusyIndicator.java:70)
	at org.eclipse.ui.internal.WorkbenchWindow.close(WorkbenchWindow.java:1576)
	at org.eclipse.ui.internal.Workbench$14.run(Workbench.java:1155)
	at org.eclipse.core.runtime.SafeRunner.run(SafeRunner.java:42)
	at org.eclipse.ui.internal.Workbench.busyClose(Workbench.java:1137)
	at org.eclipse.ui.internal.Workbench.access$21(Workbench.java:1079)
	at org.eclipse.ui.internal.Workbench$19.run(Workbench.java:1410)
	at org.eclipse.swt.custom.BusyIndicator.showWhile(BusyIndicator.java:70)
	at org.eclipse.ui.internal.Workbench.close(Workbench.java:1407)
	at org.eclipse.ui.internal.Workbench.close(Workbench.java:1380)
	at org.eclipse.ui.internal.WorkbenchWindow.busyClose(WorkbenchWindow.java:1543)
	at org.eclipse.ui.internal.WorkbenchWindow.access$15(WorkbenchWindow.java:1514)
	at org.eclipse.ui.internal.WorkbenchWindow$10.run(WorkbenchWindow.java:1579)
	at org.eclipse.swt.custom.BusyIndicator.showWhile(BusyIndicator.java:70)
	at org.eclipse.ui.internal.WorkbenchWindow.close(WorkbenchWindow.java:1576)
	at org.eclipse.ui.internal.WorkbenchWindow.close(WorkbenchWindow.java:1590)
	at org.eclipse.ui.internal.WorkbenchWindow$6.close(WorkbenchWindow.java:520)
	at org.eclipse.e4.ui.workbench.renderers.swt.WBWRenderer$11.shellClosed(WBWRenderer.java:564)
	at org.eclipse.swt.widgets.TypedListener.handleEvent(TypedListener.java:98)
	at org.eclipse.swt.widgets.EventTable.sendEvent(EventTable.java:84)
	at org.eclipse.swt.widgets.Display.sendEvent(Display.java:4353)
	at org.eclipse.swt.widgets.Widget.sendEvent(Widget.java:1061)
	at org.eclipse.swt.widgets.Widget.sendEvent(Widget.java:1085)
	at org.eclipse.swt.widgets.Widget.sendEvent(Widget.java:1070)
	at org.eclipse.swt.widgets.Decorations.closeWidget(Decorations.java:308)
	at org.eclipse.swt.widgets.Decorations.WM_CLOSE(Decorations.java:1696)
	at org.eclipse.swt.widgets.Control.windowProc(Control.java:4612)
	at org.eclipse.swt.widgets.Canvas.windowProc(Canvas.java:339)
	at org.eclipse.swt.widgets.Decorations.windowProc(Decorations.java:1626)
	at org.eclipse.swt.widgets.Shell.windowProc(Shell.java:2075)
	at org.eclipse.swt.widgets.Display.windowProc(Display.java:5023)
	at org.eclipse.swt.internal.win32.OS.DefWindowProcW(Native Method)
	at org.eclipse.swt.internal.win32.OS.DefWindowProc(OS.java:2544)
	at org.eclipse.swt.widgets.Shell.callWindowProc(Shell.java:498)
	at org.eclipse.swt.widgets.Control.windowProc(Control.java:4705)
	at org.eclipse.swt.widgets.Canvas.windowProc(Canvas.java:339)
	at org.eclipse.swt.widgets.Decorations.windowProc(Decorations.java:1626)
	at org.eclipse.swt.widgets.Shell.windowProc(Shell.java:2075)
	at org.eclipse.swt.widgets.Display.windowProc(Display.java:5023)
	at org.eclipse.swt.internal.win32.OS.DefWindowProcW(Native Method)
	at org.eclipse.swt.internal.win32.OS.DefWindowProc(OS.java:2544)
	at org.eclipse.swt.widgets.Shell.callWindowProc(Shell.java:498)
	at org.eclipse.swt.widgets.Control.windowProc(Control.java:4705)
	at org.eclipse.swt.widgets.Canvas.windowProc(Canvas.java:339)
	at org.eclipse.swt.widgets.Decorations.windowProc(Decorations.java:1626)
	at org.eclipse.swt.widgets.Shell.windowProc(Shell.java:2075)
	at org.eclipse.swt.widgets.Display.windowProc(Display.java:5023)
	at org.eclipse.swt.internal.win32.OS.DispatchMessageW(Native Method)
	at org.eclipse.swt.internal.win32.OS.DispatchMessage(OS.java:2549)
	at org.eclipse.swt.widgets.Display.readAndDispatch(Display.java:3759)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine$9.run(PartRenderingEngine.java:1151)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:332)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.run(PartRenderingEngine.java:1032)
	at org.eclipse.e4.ui.internal.workbench.E4Workbench.createAndRunUI(E4Workbench.java:148)
	at org.eclipse.ui.internal.Workbench$5.run(Workbench.java:636)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:332)
	at org.eclipse.ui.internal.Workbench.createAndRunWorkbench(Workbench.java:579)
	at org.eclipse.ui.PlatformUI.createAndRunWorkbench(PlatformUI.java:150)
	at org.eclipse.ui.internal.ide.application.IDEApplication.start(IDEApplication.java:135)
	at org.eclipse.equinox.internal.app.EclipseAppHandle.run(EclipseAppHandle.java:196)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.runApplication(EclipseAppLauncher.java:134)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.start(EclipseAppLauncher.java:104)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:382)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:236)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.eclipse.equinox.launcher.Main.invokeFramework(Main.java:648)
	at org.eclipse.equinox.launcher.Main.basicRun(Main.java:603)
	at org.eclipse.equinox.launcher.Main.run(Main.java:1465)
Caused by: org.eclipse.swt.SWTException: Widget is disposed
	at org.eclipse.swt.SWT.error(SWT.java:4441)
	at org.eclipse.swt.SWT.error(SWT.java:4356)
	at org.eclipse.swt.SWT.error(SWT.java:4327)
	at org.eclipse.swt.widgets.Widget.error(Widget.java:476)
	at org.eclipse.swt.widgets.Widget.checkWidget(Widget.java:348)
	at org.eclipse.swt.widgets.Shell.getSize(Shell.java:1092)
	at org.eclipse.ui.internal.quickaccess.SearchField.storeDialog(SearchField.java:580)
	at org.eclipse.ui.internal.quickaccess.SearchField.dispose(SearchField.java:557)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.eclipse.e4.core.internal.di.MethodRequestor.execute(MethodRequestor.java:55)
	... 88 more
!SESSION 2025-06-08 12:16:48.287 -----------------------------------------------
eclipse.buildId=unknown
java.version=1.7.0_51
java.vendor=Oracle Corporation
BootLoader constants: OS=win32, ARCH=x86, WS=win32, NL=zh_CN
Command-line arguments:  -os win32 -ws win32 -arch x86

!ENTRY org.omnetpp.main 1 0 2025-06-08 12:17:00.355
!MESSAGE OMNeT++ IDE 4.6.0.141202-f785492 started.
!SESSION 2025-06-13 10:28:38.720 -----------------------------------------------
eclipse.buildId=unknown
java.version=1.7.0_51
java.vendor=Oracle Corporation
BootLoader constants: OS=win32, ARCH=x86, WS=win32, NL=zh_CN
Command-line arguments:  -os win32 -ws win32 -arch x86

!ENTRY org.omnetpp.main 1 0 2025-06-13 10:28:44.869
!MESSAGE OMNeT++ IDE 4.6.0.141202-f785492 started.

!ENTRY org.omnetpp.main 4 0 2025-06-13 10:28:48.260
!MESSAGE Cannot download page from http://omnetpp.org/ide/omnetpp/versioncheck/?v=4.6.0.141202-f785492&d=202503101211&o=win32.x86
!STACK 0
java.io.IOException: Server returned HTTP response code: 403 for URL: http://omnetpp.org/ide/omnetpp/versioncheck/?v=4.6.0.141202-f785492&d=202503101211&o=win32.x86
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(Unknown Source)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(Unknown Source)
	at java.lang.reflect.Constructor.newInstance(Unknown Source)
	at sun.net.www.protocol.http.HttpURLConnection$6.run(Unknown Source)
	at sun.net.www.protocol.http.HttpURLConnection$6.run(Unknown Source)
	at java.security.AccessController.doPrivileged(Native Method)
	at sun.net.www.protocol.http.HttpURLConnection.getChainedException(Unknown Source)
	at sun.net.www.protocol.http.HttpURLConnection.getInputStream(Unknown Source)
	at org.omnetpp.ide.VersionChecker.getWebPage(VersionChecker.java:203)
	at org.omnetpp.ide.VersionChecker$1.run(VersionChecker.java:72)
	at org.eclipse.core.internal.jobs.Worker.run(Worker.java:54)
Caused by: java.io.IOException: Server returned HTTP response code: 403 for URL: http://omnetpp.org/ide/omnetpp/versioncheck/?v=4.6.0.141202-f785492&d=202503101211&o=win32.x86
	at sun.net.www.protocol.http.HttpURLConnection.getInputStream(Unknown Source)
	at sun.net.www.protocol.http.HttpURLConnection.getHeaderField(Unknown Source)
	at java.net.URLConnection.getContentEncoding(Unknown Source)
	at org.omnetpp.ide.VersionChecker.getWebPage(VersionChecker.java:202)
	... 2 more

!ENTRY org.eclipse.e4.ui.workbench 4 0 2025-06-13 10:36:00.311
!MESSAGE 
!STACK 0
org.eclipse.emf.common.util.BasicEList$BasicIndexOutOfBoundsException: index=0, size=0
	at org.eclipse.emf.common.util.BasicEList.get(BasicEList.java:346)
	at org.omnetpp.scave.editors.ScaveEditor.getAnalysis(ScaveEditor.java:530)
	at org.omnetpp.scave.editors.ScaveEditor.createModel(ScaveEditor.java:365)
	at org.omnetpp.scave.editors.AbstractEMFModelEditor.createPages(AbstractEMFModelEditor.java:725)
	at org.eclipse.ui.part.MultiPageEditorPart.createPartControl(MultiPageEditorPart.java:362)
	at org.eclipse.ui.internal.e4.compatibility.CompatibilityPart.createPartControl(CompatibilityPart.java:140)
	at org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor.createPartControl(CompatibilityEditor.java:99)
	at org.eclipse.ui.internal.e4.compatibility.CompatibilityPart.create(CompatibilityPart.java:321)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.eclipse.e4.core.internal.di.MethodRequestor.execute(MethodRequestor.java:55)
	at org.eclipse.e4.core.internal.di.InjectorImpl.processAnnotated(InjectorImpl.java:888)
	at org.eclipse.e4.core.internal.di.InjectorImpl.processAnnotated(InjectorImpl.java:869)
	at org.eclipse.e4.core.internal.di.InjectorImpl.inject(InjectorImpl.java:120)
	at org.eclipse.e4.core.internal.di.InjectorImpl.internalMake(InjectorImpl.java:337)
	at org.eclipse.e4.core.internal.di.InjectorImpl.make(InjectorImpl.java:258)
	at org.eclipse.e4.core.contexts.ContextInjectionFactory.make(ContextInjectionFactory.java:162)
	at org.eclipse.e4.ui.internal.workbench.ReflectionContributionFactory.createFromBundle(ReflectionContributionFactory.java:104)
	at org.eclipse.e4.ui.internal.workbench.ReflectionContributionFactory.doCreate(ReflectionContributionFactory.java:73)
	at org.eclipse.e4.ui.internal.workbench.ReflectionContributionFactory.create(ReflectionContributionFactory.java:55)
	at org.eclipse.e4.ui.workbench.renderers.swt.ContributedPartRenderer.createWidget(ContributedPartRenderer.java:127)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.createWidget(PartRenderingEngine.java:983)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.safeCreateGui(PartRenderingEngine.java:662)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.safeCreateGui(PartRenderingEngine.java:766)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.access$2(PartRenderingEngine.java:737)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine$7.run(PartRenderingEngine.java:731)
	at org.eclipse.core.runtime.SafeRunner.run(SafeRunner.java:42)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.createGui(PartRenderingEngine.java:715)
	at org.eclipse.e4.ui.workbench.renderers.swt.StackRenderer.showTab(StackRenderer.java:1251)
	at org.eclipse.e4.ui.workbench.renderers.swt.LazyStackRenderer$1.handleEvent(LazyStackRenderer.java:66)
	at org.eclipse.e4.ui.services.internal.events.UIEventHandler$1.run(UIEventHandler.java:40)
	at org.eclipse.swt.widgets.Synchronizer.syncExec(Synchronizer.java:187)
	at org.eclipse.ui.internal.UISynchronizer.syncExec(UISynchronizer.java:156)
	at org.eclipse.swt.widgets.Display.syncExec(Display.java:4734)
	at org.eclipse.e4.ui.internal.workbench.swt.E4Application$1.syncExec(E4Application.java:218)
	at org.eclipse.e4.ui.services.internal.events.UIEventHandler.handleEvent(UIEventHandler.java:36)
	at org.eclipse.equinox.internal.event.EventHandlerWrapper.handleEvent(EventHandlerWrapper.java:197)
	at org.eclipse.equinox.internal.event.EventHandlerTracker.dispatchEvent(EventHandlerTracker.java:197)
	at org.eclipse.equinox.internal.event.EventHandlerTracker.dispatchEvent(EventHandlerTracker.java:1)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:230)
	at org.eclipse.osgi.framework.eventmgr.ListenerQueue.dispatchEventSynchronous(ListenerQueue.java:148)
	at org.eclipse.equinox.internal.event.EventAdminImpl.dispatchEvent(EventAdminImpl.java:135)
	at org.eclipse.equinox.internal.event.EventAdminImpl.sendEvent(EventAdminImpl.java:78)
	at org.eclipse.equinox.internal.event.EventComponent.sendEvent(EventComponent.java:39)
	at org.eclipse.e4.ui.services.internal.events.EventBroker.send(EventBroker.java:81)
	at org.eclipse.e4.ui.internal.workbench.UIEventPublisher.notifyChanged(UIEventPublisher.java:59)
	at org.eclipse.emf.common.notify.impl.BasicNotifierImpl.eNotify(BasicNotifierImpl.java:374)
	at org.eclipse.e4.ui.model.application.ui.impl.ElementContainerImpl.setSelectedElement(ElementContainerImpl.java:171)
	at org.eclipse.e4.ui.internal.workbench.ModelServiceImpl.showElementInWindow(ModelServiceImpl.java:488)
	at org.eclipse.e4.ui.internal.workbench.ModelServiceImpl.bringToTop(ModelServiceImpl.java:454)
	at org.eclipse.e4.ui.internal.workbench.PartServiceImpl.delegateBringToTop(PartServiceImpl.java:692)
	at org.eclipse.e4.ui.internal.workbench.PartServiceImpl.bringToTop(PartServiceImpl.java:385)
	at org.eclipse.e4.ui.internal.workbench.PartServiceImpl.showPart(PartServiceImpl.java:1126)
	at org.eclipse.ui.internal.WorkbenchPage.busyOpenEditor(WorkbenchPage.java:3182)
	at org.eclipse.ui.internal.WorkbenchPage.access$23(WorkbenchPage.java:3104)
	at org.eclipse.ui.internal.WorkbenchPage$9.run(WorkbenchPage.java:3086)
	at org.eclipse.swt.custom.BusyIndicator.showWhile(BusyIndicator.java:70)
	at org.eclipse.ui.internal.WorkbenchPage.openEditor(WorkbenchPage.java:3081)
	at org.eclipse.ui.internal.WorkbenchPage.openEditor(WorkbenchPage.java:3045)
	at org.eclipse.ui.actions.OpenWithMenu.openEditor(OpenWithMenu.java:338)
	at org.eclipse.ui.actions.OpenWithMenu$3.handleEvent(OpenWithMenu.java:216)
	at org.eclipse.swt.widgets.EventTable.sendEvent(EventTable.java:84)
	at org.eclipse.swt.widgets.Display.sendEvent(Display.java:4353)
	at org.eclipse.swt.widgets.Widget.sendEvent(Widget.java:1061)
	at org.eclipse.swt.widgets.Display.runDeferredEvents(Display.java:4172)
	at org.eclipse.swt.widgets.Display.readAndDispatch(Display.java:3761)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine$9.run(PartRenderingEngine.java:1151)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:332)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.run(PartRenderingEngine.java:1032)
	at org.eclipse.e4.ui.internal.workbench.E4Workbench.createAndRunUI(E4Workbench.java:148)
	at org.eclipse.ui.internal.Workbench$5.run(Workbench.java:636)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:332)
	at org.eclipse.ui.internal.Workbench.createAndRunWorkbench(Workbench.java:579)
	at org.eclipse.ui.PlatformUI.createAndRunWorkbench(PlatformUI.java:150)
	at org.eclipse.ui.internal.ide.application.IDEApplication.start(IDEApplication.java:135)
	at org.eclipse.equinox.internal.app.EclipseAppHandle.run(EclipseAppHandle.java:196)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.runApplication(EclipseAppLauncher.java:134)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.start(EclipseAppLauncher.java:104)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:382)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:236)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.eclipse.equinox.launcher.Main.invokeFramework(Main.java:648)
	at org.eclipse.equinox.launcher.Main.basicRun(Main.java:603)
	at org.eclipse.equinox.launcher.Main.run(Main.java:1465)

!ENTRY org.eclipse.e4.ui.workbench 4 0 2025-06-13 10:36:00.312
!MESSAGE 
!STACK 0
java.lang.NullPointerException
	at org.omnetpp.scave.computed.ComputedScalarManager.dispose(ComputedScalarManager.java:63)
	at org.omnetpp.scave.editors.ScaveEditor.dispose(ScaveEditor.java:265)
	at org.eclipse.ui.internal.e4.compatibility.CompatibilityPart.createPartControl(CompatibilityPart.java:146)
	at org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor.createPartControl(CompatibilityEditor.java:99)
	at org.eclipse.ui.internal.e4.compatibility.CompatibilityPart.create(CompatibilityPart.java:321)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.eclipse.e4.core.internal.di.MethodRequestor.execute(MethodRequestor.java:55)
	at org.eclipse.e4.core.internal.di.InjectorImpl.processAnnotated(InjectorImpl.java:888)
	at org.eclipse.e4.core.internal.di.InjectorImpl.processAnnotated(InjectorImpl.java:869)
	at org.eclipse.e4.core.internal.di.InjectorImpl.inject(InjectorImpl.java:120)
	at org.eclipse.e4.core.internal.di.InjectorImpl.internalMake(InjectorImpl.java:337)
	at org.eclipse.e4.core.internal.di.InjectorImpl.make(InjectorImpl.java:258)
	at org.eclipse.e4.core.contexts.ContextInjectionFactory.make(ContextInjectionFactory.java:162)
	at org.eclipse.e4.ui.internal.workbench.ReflectionContributionFactory.createFromBundle(ReflectionContributionFactory.java:104)
	at org.eclipse.e4.ui.internal.workbench.ReflectionContributionFactory.doCreate(ReflectionContributionFactory.java:73)
	at org.eclipse.e4.ui.internal.workbench.ReflectionContributionFactory.create(ReflectionContributionFactory.java:55)
	at org.eclipse.e4.ui.workbench.renderers.swt.ContributedPartRenderer.createWidget(ContributedPartRenderer.java:127)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.createWidget(PartRenderingEngine.java:983)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.safeCreateGui(PartRenderingEngine.java:662)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.safeCreateGui(PartRenderingEngine.java:766)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.access$2(PartRenderingEngine.java:737)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine$7.run(PartRenderingEngine.java:731)
	at org.eclipse.core.runtime.SafeRunner.run(SafeRunner.java:42)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.createGui(PartRenderingEngine.java:715)
	at org.eclipse.e4.ui.workbench.renderers.swt.StackRenderer.showTab(StackRenderer.java:1251)
	at org.eclipse.e4.ui.workbench.renderers.swt.LazyStackRenderer$1.handleEvent(LazyStackRenderer.java:66)
	at org.eclipse.e4.ui.services.internal.events.UIEventHandler$1.run(UIEventHandler.java:40)
	at org.eclipse.swt.widgets.Synchronizer.syncExec(Synchronizer.java:187)
	at org.eclipse.ui.internal.UISynchronizer.syncExec(UISynchronizer.java:156)
	at org.eclipse.swt.widgets.Display.syncExec(Display.java:4734)
	at org.eclipse.e4.ui.internal.workbench.swt.E4Application$1.syncExec(E4Application.java:218)
	at org.eclipse.e4.ui.services.internal.events.UIEventHandler.handleEvent(UIEventHandler.java:36)
	at org.eclipse.equinox.internal.event.EventHandlerWrapper.handleEvent(EventHandlerWrapper.java:197)
	at org.eclipse.equinox.internal.event.EventHandlerTracker.dispatchEvent(EventHandlerTracker.java:197)
	at org.eclipse.equinox.internal.event.EventHandlerTracker.dispatchEvent(EventHandlerTracker.java:1)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:230)
	at org.eclipse.osgi.framework.eventmgr.ListenerQueue.dispatchEventSynchronous(ListenerQueue.java:148)
	at org.eclipse.equinox.internal.event.EventAdminImpl.dispatchEvent(EventAdminImpl.java:135)
	at org.eclipse.equinox.internal.event.EventAdminImpl.sendEvent(EventAdminImpl.java:78)
	at org.eclipse.equinox.internal.event.EventComponent.sendEvent(EventComponent.java:39)
	at org.eclipse.e4.ui.services.internal.events.EventBroker.send(EventBroker.java:81)
	at org.eclipse.e4.ui.internal.workbench.UIEventPublisher.notifyChanged(UIEventPublisher.java:59)
	at org.eclipse.emf.common.notify.impl.BasicNotifierImpl.eNotify(BasicNotifierImpl.java:374)
	at org.eclipse.e4.ui.model.application.ui.impl.ElementContainerImpl.setSelectedElement(ElementContainerImpl.java:171)
	at org.eclipse.e4.ui.internal.workbench.ModelServiceImpl.showElementInWindow(ModelServiceImpl.java:488)
	at org.eclipse.e4.ui.internal.workbench.ModelServiceImpl.bringToTop(ModelServiceImpl.java:454)
	at org.eclipse.e4.ui.internal.workbench.PartServiceImpl.delegateBringToTop(PartServiceImpl.java:692)
	at org.eclipse.e4.ui.internal.workbench.PartServiceImpl.bringToTop(PartServiceImpl.java:385)
	at org.eclipse.e4.ui.internal.workbench.PartServiceImpl.showPart(PartServiceImpl.java:1126)
	at org.eclipse.ui.internal.WorkbenchPage.busyOpenEditor(WorkbenchPage.java:3182)
	at org.eclipse.ui.internal.WorkbenchPage.access$23(WorkbenchPage.java:3104)
	at org.eclipse.ui.internal.WorkbenchPage$9.run(WorkbenchPage.java:3086)
	at org.eclipse.swt.custom.BusyIndicator.showWhile(BusyIndicator.java:70)
	at org.eclipse.ui.internal.WorkbenchPage.openEditor(WorkbenchPage.java:3081)
	at org.eclipse.ui.internal.WorkbenchPage.openEditor(WorkbenchPage.java:3045)
	at org.eclipse.ui.actions.OpenWithMenu.openEditor(OpenWithMenu.java:338)
	at org.eclipse.ui.actions.OpenWithMenu$3.handleEvent(OpenWithMenu.java:216)
	at org.eclipse.swt.widgets.EventTable.sendEvent(EventTable.java:84)
	at org.eclipse.swt.widgets.Display.sendEvent(Display.java:4353)
	at org.eclipse.swt.widgets.Widget.sendEvent(Widget.java:1061)
	at org.eclipse.swt.widgets.Display.runDeferredEvents(Display.java:4172)
	at org.eclipse.swt.widgets.Display.readAndDispatch(Display.java:3761)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine$9.run(PartRenderingEngine.java:1151)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:332)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.run(PartRenderingEngine.java:1032)
	at org.eclipse.e4.ui.internal.workbench.E4Workbench.createAndRunUI(E4Workbench.java:148)
	at org.eclipse.ui.internal.Workbench$5.run(Workbench.java:636)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:332)
	at org.eclipse.ui.internal.Workbench.createAndRunWorkbench(Workbench.java:579)
	at org.eclipse.ui.PlatformUI.createAndRunWorkbench(PlatformUI.java:150)
	at org.eclipse.ui.internal.ide.application.IDEApplication.start(IDEApplication.java:135)
	at org.eclipse.equinox.internal.app.EclipseAppHandle.run(EclipseAppHandle.java:196)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.runApplication(EclipseAppLauncher.java:134)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.start(EclipseAppLauncher.java:104)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:382)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:236)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.eclipse.equinox.launcher.Main.invokeFramework(Main.java:648)
	at org.eclipse.equinox.launcher.Main.basicRun(Main.java:603)
	at org.eclipse.equinox.launcher.Main.run(Main.java:1465)

!ENTRY org.eclipse.core.jobs 4 2 2025-06-13 10:36:00.313
!MESSAGE An internal error occurred during: "Updating problem markers".
!STACK 0
java.lang.NullPointerException
	at org.omnetpp.common.markers.ProblemMarkerSynchronizer.addRemoveMarkers(ProblemMarkerSynchronizer.java:199)
	at org.omnetpp.common.markers.ProblemMarkerSynchronizer$1.runInWorkspace(ProblemMarkerSynchronizer.java:177)
	at org.eclipse.core.internal.resources.InternalWorkspaceJob.run(InternalWorkspaceJob.java:38)
	at org.eclipse.core.internal.jobs.Worker.run(Worker.java:54)

!ENTRY org.eclipse.e4.ui.workbench 4 2 2025-06-13 10:36:00.349
!MESSAGE Problems occurred when invoking code from plug-in: "org.eclipse.e4.ui.workbench".
!STACK 0
java.lang.NullPointerException
	at org.omnetpp.scave.model.presentation.ScaveModelActionBarContributor.setActiveEditor(ScaveModelActionBarContributor.java:253)
	at org.eclipse.ui.internal.EditorActionBars.partChanged(EditorActionBars.java:354)
	at org.eclipse.ui.internal.WorkbenchPage.updateActivations(WorkbenchPage.java:298)
	at org.eclipse.ui.internal.WorkbenchPage.access$17(WorkbenchPage.java:280)
	at org.eclipse.ui.internal.WorkbenchPage$E4PartListener.partActivated(WorkbenchPage.java:202)
	at org.eclipse.e4.ui.internal.workbench.PartServiceImpl$3.run(PartServiceImpl.java:234)
	at org.eclipse.core.runtime.SafeRunner.run(SafeRunner.java:42)
	at org.eclipse.e4.ui.internal.workbench.PartServiceImpl.firePartActivated(PartServiceImpl.java:231)
	at org.eclipse.e4.ui.internal.workbench.PartServiceImpl.activate(PartServiceImpl.java:682)
	at org.eclipse.e4.ui.internal.workbench.PartServiceImpl.activate(PartServiceImpl.java:613)
	at org.eclipse.e4.ui.internal.workbench.PartServiceImpl.activate(PartServiceImpl.java:601)
	at org.eclipse.ui.internal.WorkbenchPage.busyOpenEditor(WorkbenchPage.java:3190)
	at org.eclipse.ui.internal.WorkbenchPage.access$23(WorkbenchPage.java:3104)
	at org.eclipse.ui.internal.WorkbenchPage$9.run(WorkbenchPage.java:3086)
	at org.eclipse.swt.custom.BusyIndicator.showWhile(BusyIndicator.java:70)
	at org.eclipse.ui.internal.WorkbenchPage.openEditor(WorkbenchPage.java:3081)
	at org.eclipse.ui.internal.WorkbenchPage.openEditor(WorkbenchPage.java:3045)
	at org.eclipse.ui.actions.OpenWithMenu.openEditor(OpenWithMenu.java:338)
	at org.eclipse.ui.actions.OpenWithMenu$3.handleEvent(OpenWithMenu.java:216)
	at org.eclipse.swt.widgets.EventTable.sendEvent(EventTable.java:84)
	at org.eclipse.swt.widgets.Display.sendEvent(Display.java:4353)
	at org.eclipse.swt.widgets.Widget.sendEvent(Widget.java:1061)
	at org.eclipse.swt.widgets.Display.runDeferredEvents(Display.java:4172)
	at org.eclipse.swt.widgets.Display.readAndDispatch(Display.java:3761)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine$9.run(PartRenderingEngine.java:1151)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:332)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.run(PartRenderingEngine.java:1032)
	at org.eclipse.e4.ui.internal.workbench.E4Workbench.createAndRunUI(E4Workbench.java:148)
	at org.eclipse.ui.internal.Workbench$5.run(Workbench.java:636)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:332)
	at org.eclipse.ui.internal.Workbench.createAndRunWorkbench(Workbench.java:579)
	at org.eclipse.ui.PlatformUI.createAndRunWorkbench(PlatformUI.java:150)
	at org.eclipse.ui.internal.ide.application.IDEApplication.start(IDEApplication.java:135)
	at org.eclipse.equinox.internal.app.EclipseAppHandle.run(EclipseAppHandle.java:196)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.runApplication(EclipseAppLauncher.java:134)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.start(EclipseAppLauncher.java:104)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:382)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:236)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.eclipse.equinox.launcher.Main.invokeFramework(Main.java:648)
	at org.eclipse.equinox.launcher.Main.basicRun(Main.java:603)
	at org.eclipse.equinox.launcher.Main.run(Main.java:1465)

!ENTRY org.eclipse.e4.ui.workbench 4 0 2025-06-13 10:36:00.350
!MESSAGE An exception occurred while notifying part listeners
!STACK 0
java.lang.NullPointerException
	at org.omnetpp.scave.model.presentation.ScaveModelActionBarContributor.setActiveEditor(ScaveModelActionBarContributor.java:253)
	at org.eclipse.ui.internal.EditorActionBars.partChanged(EditorActionBars.java:354)
	at org.eclipse.ui.internal.WorkbenchPage.updateActivations(WorkbenchPage.java:298)
	at org.eclipse.ui.internal.WorkbenchPage.access$17(WorkbenchPage.java:280)
	at org.eclipse.ui.internal.WorkbenchPage$E4PartListener.partActivated(WorkbenchPage.java:202)
	at org.eclipse.e4.ui.internal.workbench.PartServiceImpl$3.run(PartServiceImpl.java:234)
	at org.eclipse.core.runtime.SafeRunner.run(SafeRunner.java:42)
	at org.eclipse.e4.ui.internal.workbench.PartServiceImpl.firePartActivated(PartServiceImpl.java:231)
	at org.eclipse.e4.ui.internal.workbench.PartServiceImpl.activate(PartServiceImpl.java:682)
	at org.eclipse.e4.ui.internal.workbench.PartServiceImpl.activate(PartServiceImpl.java:613)
	at org.eclipse.e4.ui.internal.workbench.PartServiceImpl.activate(PartServiceImpl.java:601)
	at org.eclipse.ui.internal.WorkbenchPage.busyOpenEditor(WorkbenchPage.java:3190)
	at org.eclipse.ui.internal.WorkbenchPage.access$23(WorkbenchPage.java:3104)
	at org.eclipse.ui.internal.WorkbenchPage$9.run(WorkbenchPage.java:3086)
	at org.eclipse.swt.custom.BusyIndicator.showWhile(BusyIndicator.java:70)
	at org.eclipse.ui.internal.WorkbenchPage.openEditor(WorkbenchPage.java:3081)
	at org.eclipse.ui.internal.WorkbenchPage.openEditor(WorkbenchPage.java:3045)
	at org.eclipse.ui.actions.OpenWithMenu.openEditor(OpenWithMenu.java:338)
	at org.eclipse.ui.actions.OpenWithMenu$3.handleEvent(OpenWithMenu.java:216)
	at org.eclipse.swt.widgets.EventTable.sendEvent(EventTable.java:84)
	at org.eclipse.swt.widgets.Display.sendEvent(Display.java:4353)
	at org.eclipse.swt.widgets.Widget.sendEvent(Widget.java:1061)
	at org.eclipse.swt.widgets.Display.runDeferredEvents(Display.java:4172)
	at org.eclipse.swt.widgets.Display.readAndDispatch(Display.java:3761)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine$9.run(PartRenderingEngine.java:1151)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:332)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.run(PartRenderingEngine.java:1032)
	at org.eclipse.e4.ui.internal.workbench.E4Workbench.createAndRunUI(E4Workbench.java:148)
	at org.eclipse.ui.internal.Workbench$5.run(Workbench.java:636)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:332)
	at org.eclipse.ui.internal.Workbench.createAndRunWorkbench(Workbench.java:579)
	at org.eclipse.ui.PlatformUI.createAndRunWorkbench(PlatformUI.java:150)
	at org.eclipse.ui.internal.ide.application.IDEApplication.start(IDEApplication.java:135)
	at org.eclipse.equinox.internal.app.EclipseAppHandle.run(EclipseAppHandle.java:196)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.runApplication(EclipseAppLauncher.java:134)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.start(EclipseAppLauncher.java:104)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:382)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:236)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.eclipse.equinox.launcher.Main.invokeFramework(Main.java:648)
	at org.eclipse.equinox.launcher.Main.basicRun(Main.java:603)
	at org.eclipse.equinox.launcher.Main.run(Main.java:1465)

!ENTRY org.eclipse.e4.ui.workbench 4 0 2025-06-13 10:36:28.918
!MESSAGE 
!STACK 0
org.eclipse.emf.common.util.BasicEList$BasicIndexOutOfBoundsException: index=0, size=0
	at org.eclipse.emf.common.util.BasicEList.get(BasicEList.java:346)
	at org.omnetpp.scave.editors.ScaveEditor.getAnalysis(ScaveEditor.java:530)
	at org.omnetpp.scave.editors.ScaveEditor.createModel(ScaveEditor.java:365)
	at org.omnetpp.scave.editors.AbstractEMFModelEditor.createPages(AbstractEMFModelEditor.java:725)
	at org.eclipse.ui.part.MultiPageEditorPart.createPartControl(MultiPageEditorPart.java:362)
	at org.eclipse.ui.internal.e4.compatibility.CompatibilityPart.createPartControl(CompatibilityPart.java:140)
	at org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor.createPartControl(CompatibilityEditor.java:99)
	at org.eclipse.ui.internal.e4.compatibility.CompatibilityPart.create(CompatibilityPart.java:321)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.eclipse.e4.core.internal.di.MethodRequestor.execute(MethodRequestor.java:55)
	at org.eclipse.e4.core.internal.di.InjectorImpl.processAnnotated(InjectorImpl.java:888)
	at org.eclipse.e4.core.internal.di.InjectorImpl.processAnnotated(InjectorImpl.java:869)
	at org.eclipse.e4.core.internal.di.InjectorImpl.inject(InjectorImpl.java:120)
	at org.eclipse.e4.core.internal.di.InjectorImpl.internalMake(InjectorImpl.java:337)
	at org.eclipse.e4.core.internal.di.InjectorImpl.make(InjectorImpl.java:258)
	at org.eclipse.e4.core.contexts.ContextInjectionFactory.make(ContextInjectionFactory.java:162)
	at org.eclipse.e4.ui.internal.workbench.ReflectionContributionFactory.createFromBundle(ReflectionContributionFactory.java:104)
	at org.eclipse.e4.ui.internal.workbench.ReflectionContributionFactory.doCreate(ReflectionContributionFactory.java:73)
	at org.eclipse.e4.ui.internal.workbench.ReflectionContributionFactory.create(ReflectionContributionFactory.java:55)
	at org.eclipse.e4.ui.workbench.renderers.swt.ContributedPartRenderer.createWidget(ContributedPartRenderer.java:127)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.createWidget(PartRenderingEngine.java:983)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.safeCreateGui(PartRenderingEngine.java:662)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.safeCreateGui(PartRenderingEngine.java:766)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.access$2(PartRenderingEngine.java:737)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine$7.run(PartRenderingEngine.java:731)
	at org.eclipse.core.runtime.SafeRunner.run(SafeRunner.java:42)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.createGui(PartRenderingEngine.java:715)
	at org.eclipse.e4.ui.workbench.renderers.swt.StackRenderer.showTab(StackRenderer.java:1251)
	at org.eclipse.e4.ui.workbench.renderers.swt.LazyStackRenderer$1.handleEvent(LazyStackRenderer.java:66)
	at org.eclipse.e4.ui.services.internal.events.UIEventHandler$1.run(UIEventHandler.java:40)
	at org.eclipse.swt.widgets.Synchronizer.syncExec(Synchronizer.java:187)
	at org.eclipse.ui.internal.UISynchronizer.syncExec(UISynchronizer.java:156)
	at org.eclipse.swt.widgets.Display.syncExec(Display.java:4734)
	at org.eclipse.e4.ui.internal.workbench.swt.E4Application$1.syncExec(E4Application.java:218)
	at org.eclipse.e4.ui.services.internal.events.UIEventHandler.handleEvent(UIEventHandler.java:36)
	at org.eclipse.equinox.internal.event.EventHandlerWrapper.handleEvent(EventHandlerWrapper.java:197)
	at org.eclipse.equinox.internal.event.EventHandlerTracker.dispatchEvent(EventHandlerTracker.java:197)
	at org.eclipse.equinox.internal.event.EventHandlerTracker.dispatchEvent(EventHandlerTracker.java:1)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:230)
	at org.eclipse.osgi.framework.eventmgr.ListenerQueue.dispatchEventSynchronous(ListenerQueue.java:148)
	at org.eclipse.equinox.internal.event.EventAdminImpl.dispatchEvent(EventAdminImpl.java:135)
	at org.eclipse.equinox.internal.event.EventAdminImpl.sendEvent(EventAdminImpl.java:78)
	at org.eclipse.equinox.internal.event.EventComponent.sendEvent(EventComponent.java:39)
	at org.eclipse.e4.ui.services.internal.events.EventBroker.send(EventBroker.java:81)
	at org.eclipse.e4.ui.internal.workbench.UIEventPublisher.notifyChanged(UIEventPublisher.java:59)
	at org.eclipse.emf.common.notify.impl.BasicNotifierImpl.eNotify(BasicNotifierImpl.java:374)
	at org.eclipse.e4.ui.model.application.ui.impl.ElementContainerImpl.setSelectedElement(ElementContainerImpl.java:171)
	at org.eclipse.e4.ui.internal.workbench.ModelServiceImpl.showElementInWindow(ModelServiceImpl.java:488)
	at org.eclipse.e4.ui.internal.workbench.ModelServiceImpl.bringToTop(ModelServiceImpl.java:454)
	at org.eclipse.e4.ui.internal.workbench.PartServiceImpl.delegateBringToTop(PartServiceImpl.java:692)
	at org.eclipse.e4.ui.internal.workbench.PartServiceImpl.bringToTop(PartServiceImpl.java:385)
	at org.eclipse.e4.ui.internal.workbench.PartServiceImpl.showPart(PartServiceImpl.java:1126)
	at org.eclipse.ui.internal.WorkbenchPage.busyOpenEditor(WorkbenchPage.java:3182)
	at org.eclipse.ui.internal.WorkbenchPage.access$23(WorkbenchPage.java:3104)
	at org.eclipse.ui.internal.WorkbenchPage$9.run(WorkbenchPage.java:3086)
	at org.eclipse.swt.custom.BusyIndicator.showWhile(BusyIndicator.java:70)
	at org.eclipse.ui.internal.WorkbenchPage.openEditor(WorkbenchPage.java:3081)
	at org.eclipse.ui.internal.WorkbenchPage.openEditor(WorkbenchPage.java:3045)
	at org.eclipse.ui.actions.OpenWithMenu.openEditor(OpenWithMenu.java:338)
	at org.eclipse.ui.actions.OpenWithMenu$3.handleEvent(OpenWithMenu.java:216)
	at org.eclipse.swt.widgets.EventTable.sendEvent(EventTable.java:84)
	at org.eclipse.swt.widgets.Display.sendEvent(Display.java:4353)
	at org.eclipse.swt.widgets.Widget.sendEvent(Widget.java:1061)
	at org.eclipse.swt.widgets.Display.runDeferredEvents(Display.java:4172)
	at org.eclipse.swt.widgets.Display.readAndDispatch(Display.java:3761)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine$9.run(PartRenderingEngine.java:1151)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:332)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.run(PartRenderingEngine.java:1032)
	at org.eclipse.e4.ui.internal.workbench.E4Workbench.createAndRunUI(E4Workbench.java:148)
	at org.eclipse.ui.internal.Workbench$5.run(Workbench.java:636)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:332)
	at org.eclipse.ui.internal.Workbench.createAndRunWorkbench(Workbench.java:579)
	at org.eclipse.ui.PlatformUI.createAndRunWorkbench(PlatformUI.java:150)
	at org.eclipse.ui.internal.ide.application.IDEApplication.start(IDEApplication.java:135)
	at org.eclipse.equinox.internal.app.EclipseAppHandle.run(EclipseAppHandle.java:196)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.runApplication(EclipseAppLauncher.java:134)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.start(EclipseAppLauncher.java:104)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:382)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:236)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.eclipse.equinox.launcher.Main.invokeFramework(Main.java:648)
	at org.eclipse.equinox.launcher.Main.basicRun(Main.java:603)
	at org.eclipse.equinox.launcher.Main.run(Main.java:1465)

!ENTRY org.eclipse.e4.ui.workbench 4 0 2025-06-13 10:36:28.919
!MESSAGE 
!STACK 0
java.lang.NullPointerException
	at org.omnetpp.scave.computed.ComputedScalarManager.dispose(ComputedScalarManager.java:63)
	at org.omnetpp.scave.editors.ScaveEditor.dispose(ScaveEditor.java:265)
	at org.eclipse.ui.internal.e4.compatibility.CompatibilityPart.createPartControl(CompatibilityPart.java:146)
	at org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor.createPartControl(CompatibilityEditor.java:99)
	at org.eclipse.ui.internal.e4.compatibility.CompatibilityPart.create(CompatibilityPart.java:321)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.eclipse.e4.core.internal.di.MethodRequestor.execute(MethodRequestor.java:55)
	at org.eclipse.e4.core.internal.di.InjectorImpl.processAnnotated(InjectorImpl.java:888)
	at org.eclipse.e4.core.internal.di.InjectorImpl.processAnnotated(InjectorImpl.java:869)
	at org.eclipse.e4.core.internal.di.InjectorImpl.inject(InjectorImpl.java:120)
	at org.eclipse.e4.core.internal.di.InjectorImpl.internalMake(InjectorImpl.java:337)
	at org.eclipse.e4.core.internal.di.InjectorImpl.make(InjectorImpl.java:258)
	at org.eclipse.e4.core.contexts.ContextInjectionFactory.make(ContextInjectionFactory.java:162)
	at org.eclipse.e4.ui.internal.workbench.ReflectionContributionFactory.createFromBundle(ReflectionContributionFactory.java:104)
	at org.eclipse.e4.ui.internal.workbench.ReflectionContributionFactory.doCreate(ReflectionContributionFactory.java:73)
	at org.eclipse.e4.ui.internal.workbench.ReflectionContributionFactory.create(ReflectionContributionFactory.java:55)
	at org.eclipse.e4.ui.workbench.renderers.swt.ContributedPartRenderer.createWidget(ContributedPartRenderer.java:127)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.createWidget(PartRenderingEngine.java:983)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.safeCreateGui(PartRenderingEngine.java:662)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.safeCreateGui(PartRenderingEngine.java:766)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.access$2(PartRenderingEngine.java:737)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine$7.run(PartRenderingEngine.java:731)
	at org.eclipse.core.runtime.SafeRunner.run(SafeRunner.java:42)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.createGui(PartRenderingEngine.java:715)
	at org.eclipse.e4.ui.workbench.renderers.swt.StackRenderer.showTab(StackRenderer.java:1251)
	at org.eclipse.e4.ui.workbench.renderers.swt.LazyStackRenderer$1.handleEvent(LazyStackRenderer.java:66)
	at org.eclipse.e4.ui.services.internal.events.UIEventHandler$1.run(UIEventHandler.java:40)
	at org.eclipse.swt.widgets.Synchronizer.syncExec(Synchronizer.java:187)
	at org.eclipse.ui.internal.UISynchronizer.syncExec(UISynchronizer.java:156)
	at org.eclipse.swt.widgets.Display.syncExec(Display.java:4734)
	at org.eclipse.e4.ui.internal.workbench.swt.E4Application$1.syncExec(E4Application.java:218)
	at org.eclipse.e4.ui.services.internal.events.UIEventHandler.handleEvent(UIEventHandler.java:36)
	at org.eclipse.equinox.internal.event.EventHandlerWrapper.handleEvent(EventHandlerWrapper.java:197)
	at org.eclipse.equinox.internal.event.EventHandlerTracker.dispatchEvent(EventHandlerTracker.java:197)
	at org.eclipse.equinox.internal.event.EventHandlerTracker.dispatchEvent(EventHandlerTracker.java:1)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:230)
	at org.eclipse.osgi.framework.eventmgr.ListenerQueue.dispatchEventSynchronous(ListenerQueue.java:148)
	at org.eclipse.equinox.internal.event.EventAdminImpl.dispatchEvent(EventAdminImpl.java:135)
	at org.eclipse.equinox.internal.event.EventAdminImpl.sendEvent(EventAdminImpl.java:78)
	at org.eclipse.equinox.internal.event.EventComponent.sendEvent(EventComponent.java:39)
	at org.eclipse.e4.ui.services.internal.events.EventBroker.send(EventBroker.java:81)
	at org.eclipse.e4.ui.internal.workbench.UIEventPublisher.notifyChanged(UIEventPublisher.java:59)
	at org.eclipse.emf.common.notify.impl.BasicNotifierImpl.eNotify(BasicNotifierImpl.java:374)
	at org.eclipse.e4.ui.model.application.ui.impl.ElementContainerImpl.setSelectedElement(ElementContainerImpl.java:171)
	at org.eclipse.e4.ui.internal.workbench.ModelServiceImpl.showElementInWindow(ModelServiceImpl.java:488)
	at org.eclipse.e4.ui.internal.workbench.ModelServiceImpl.bringToTop(ModelServiceImpl.java:454)
	at org.eclipse.e4.ui.internal.workbench.PartServiceImpl.delegateBringToTop(PartServiceImpl.java:692)
	at org.eclipse.e4.ui.internal.workbench.PartServiceImpl.bringToTop(PartServiceImpl.java:385)
	at org.eclipse.e4.ui.internal.workbench.PartServiceImpl.showPart(PartServiceImpl.java:1126)
	at org.eclipse.ui.internal.WorkbenchPage.busyOpenEditor(WorkbenchPage.java:3182)
	at org.eclipse.ui.internal.WorkbenchPage.access$23(WorkbenchPage.java:3104)
	at org.eclipse.ui.internal.WorkbenchPage$9.run(WorkbenchPage.java:3086)
	at org.eclipse.swt.custom.BusyIndicator.showWhile(BusyIndicator.java:70)
	at org.eclipse.ui.internal.WorkbenchPage.openEditor(WorkbenchPage.java:3081)
	at org.eclipse.ui.internal.WorkbenchPage.openEditor(WorkbenchPage.java:3045)
	at org.eclipse.ui.actions.OpenWithMenu.openEditor(OpenWithMenu.java:338)
	at org.eclipse.ui.actions.OpenWithMenu$3.handleEvent(OpenWithMenu.java:216)
	at org.eclipse.swt.widgets.EventTable.sendEvent(EventTable.java:84)
	at org.eclipse.swt.widgets.Display.sendEvent(Display.java:4353)
	at org.eclipse.swt.widgets.Widget.sendEvent(Widget.java:1061)
	at org.eclipse.swt.widgets.Display.runDeferredEvents(Display.java:4172)
	at org.eclipse.swt.widgets.Display.readAndDispatch(Display.java:3761)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine$9.run(PartRenderingEngine.java:1151)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:332)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.run(PartRenderingEngine.java:1032)
	at org.eclipse.e4.ui.internal.workbench.E4Workbench.createAndRunUI(E4Workbench.java:148)
	at org.eclipse.ui.internal.Workbench$5.run(Workbench.java:636)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:332)
	at org.eclipse.ui.internal.Workbench.createAndRunWorkbench(Workbench.java:579)
	at org.eclipse.ui.PlatformUI.createAndRunWorkbench(PlatformUI.java:150)
	at org.eclipse.ui.internal.ide.application.IDEApplication.start(IDEApplication.java:135)
	at org.eclipse.equinox.internal.app.EclipseAppHandle.run(EclipseAppHandle.java:196)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.runApplication(EclipseAppLauncher.java:134)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.start(EclipseAppLauncher.java:104)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:382)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:236)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.eclipse.equinox.launcher.Main.invokeFramework(Main.java:648)
	at org.eclipse.equinox.launcher.Main.basicRun(Main.java:603)
	at org.eclipse.equinox.launcher.Main.run(Main.java:1465)

!ENTRY org.eclipse.core.jobs 4 2 2025-06-13 10:36:28.920
!MESSAGE An internal error occurred during: "Updating problem markers".
!STACK 0
java.lang.NullPointerException
	at org.omnetpp.common.markers.ProblemMarkerSynchronizer.addRemoveMarkers(ProblemMarkerSynchronizer.java:199)
	at org.omnetpp.common.markers.ProblemMarkerSynchronizer$1.runInWorkspace(ProblemMarkerSynchronizer.java:177)
	at org.eclipse.core.internal.resources.InternalWorkspaceJob.run(InternalWorkspaceJob.java:38)
	at org.eclipse.core.internal.jobs.Worker.run(Worker.java:54)

!ENTRY org.eclipse.e4.ui.workbench 4 2 2025-06-13 10:36:28.950
!MESSAGE Problems occurred when invoking code from plug-in: "org.eclipse.e4.ui.workbench".
!STACK 0
java.lang.NullPointerException
	at org.omnetpp.scave.model.presentation.ScaveModelActionBarContributor.setActiveEditor(ScaveModelActionBarContributor.java:253)
	at org.eclipse.ui.internal.EditorActionBars.partChanged(EditorActionBars.java:354)
	at org.eclipse.ui.internal.WorkbenchPage.updateActivations(WorkbenchPage.java:298)
	at org.eclipse.ui.internal.WorkbenchPage.access$17(WorkbenchPage.java:280)
	at org.eclipse.ui.internal.WorkbenchPage$E4PartListener.partActivated(WorkbenchPage.java:202)
	at org.eclipse.e4.ui.internal.workbench.PartServiceImpl$3.run(PartServiceImpl.java:234)
	at org.eclipse.core.runtime.SafeRunner.run(SafeRunner.java:42)
	at org.eclipse.e4.ui.internal.workbench.PartServiceImpl.firePartActivated(PartServiceImpl.java:231)
	at org.eclipse.e4.ui.internal.workbench.PartServiceImpl.activate(PartServiceImpl.java:682)
	at org.eclipse.e4.ui.internal.workbench.PartServiceImpl.activate(PartServiceImpl.java:613)
	at org.eclipse.e4.ui.internal.workbench.PartServiceImpl.activate(PartServiceImpl.java:601)
	at org.eclipse.ui.internal.WorkbenchPage.busyOpenEditor(WorkbenchPage.java:3190)
	at org.eclipse.ui.internal.WorkbenchPage.access$23(WorkbenchPage.java:3104)
	at org.eclipse.ui.internal.WorkbenchPage$9.run(WorkbenchPage.java:3086)
	at org.eclipse.swt.custom.BusyIndicator.showWhile(BusyIndicator.java:70)
	at org.eclipse.ui.internal.WorkbenchPage.openEditor(WorkbenchPage.java:3081)
	at org.eclipse.ui.internal.WorkbenchPage.openEditor(WorkbenchPage.java:3045)
	at org.eclipse.ui.actions.OpenWithMenu.openEditor(OpenWithMenu.java:338)
	at org.eclipse.ui.actions.OpenWithMenu$3.handleEvent(OpenWithMenu.java:216)
	at org.eclipse.swt.widgets.EventTable.sendEvent(EventTable.java:84)
	at org.eclipse.swt.widgets.Display.sendEvent(Display.java:4353)
	at org.eclipse.swt.widgets.Widget.sendEvent(Widget.java:1061)
	at org.eclipse.swt.widgets.Display.runDeferredEvents(Display.java:4172)
	at org.eclipse.swt.widgets.Display.readAndDispatch(Display.java:3761)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine$9.run(PartRenderingEngine.java:1151)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:332)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.run(PartRenderingEngine.java:1032)
	at org.eclipse.e4.ui.internal.workbench.E4Workbench.createAndRunUI(E4Workbench.java:148)
	at org.eclipse.ui.internal.Workbench$5.run(Workbench.java:636)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:332)
	at org.eclipse.ui.internal.Workbench.createAndRunWorkbench(Workbench.java:579)
	at org.eclipse.ui.PlatformUI.createAndRunWorkbench(PlatformUI.java:150)
	at org.eclipse.ui.internal.ide.application.IDEApplication.start(IDEApplication.java:135)
	at org.eclipse.equinox.internal.app.EclipseAppHandle.run(EclipseAppHandle.java:196)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.runApplication(EclipseAppLauncher.java:134)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.start(EclipseAppLauncher.java:104)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:382)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:236)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.eclipse.equinox.launcher.Main.invokeFramework(Main.java:648)
	at org.eclipse.equinox.launcher.Main.basicRun(Main.java:603)
	at org.eclipse.equinox.launcher.Main.run(Main.java:1465)

!ENTRY org.eclipse.e4.ui.workbench 4 0 2025-06-13 10:36:28.950
!MESSAGE An exception occurred while notifying part listeners
!STACK 0
java.lang.NullPointerException
	at org.omnetpp.scave.model.presentation.ScaveModelActionBarContributor.setActiveEditor(ScaveModelActionBarContributor.java:253)
	at org.eclipse.ui.internal.EditorActionBars.partChanged(EditorActionBars.java:354)
	at org.eclipse.ui.internal.WorkbenchPage.updateActivations(WorkbenchPage.java:298)
	at org.eclipse.ui.internal.WorkbenchPage.access$17(WorkbenchPage.java:280)
	at org.eclipse.ui.internal.WorkbenchPage$E4PartListener.partActivated(WorkbenchPage.java:202)
	at org.eclipse.e4.ui.internal.workbench.PartServiceImpl$3.run(PartServiceImpl.java:234)
	at org.eclipse.core.runtime.SafeRunner.run(SafeRunner.java:42)
	at org.eclipse.e4.ui.internal.workbench.PartServiceImpl.firePartActivated(PartServiceImpl.java:231)
	at org.eclipse.e4.ui.internal.workbench.PartServiceImpl.activate(PartServiceImpl.java:682)
	at org.eclipse.e4.ui.internal.workbench.PartServiceImpl.activate(PartServiceImpl.java:613)
	at org.eclipse.e4.ui.internal.workbench.PartServiceImpl.activate(PartServiceImpl.java:601)
	at org.eclipse.ui.internal.WorkbenchPage.busyOpenEditor(WorkbenchPage.java:3190)
	at org.eclipse.ui.internal.WorkbenchPage.access$23(WorkbenchPage.java:3104)
	at org.eclipse.ui.internal.WorkbenchPage$9.run(WorkbenchPage.java:3086)
	at org.eclipse.swt.custom.BusyIndicator.showWhile(BusyIndicator.java:70)
	at org.eclipse.ui.internal.WorkbenchPage.openEditor(WorkbenchPage.java:3081)
	at org.eclipse.ui.internal.WorkbenchPage.openEditor(WorkbenchPage.java:3045)
	at org.eclipse.ui.actions.OpenWithMenu.openEditor(OpenWithMenu.java:338)
	at org.eclipse.ui.actions.OpenWithMenu$3.handleEvent(OpenWithMenu.java:216)
	at org.eclipse.swt.widgets.EventTable.sendEvent(EventTable.java:84)
	at org.eclipse.swt.widgets.Display.sendEvent(Display.java:4353)
	at org.eclipse.swt.widgets.Widget.sendEvent(Widget.java:1061)
	at org.eclipse.swt.widgets.Display.runDeferredEvents(Display.java:4172)
	at org.eclipse.swt.widgets.Display.readAndDispatch(Display.java:3761)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine$9.run(PartRenderingEngine.java:1151)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:332)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.run(PartRenderingEngine.java:1032)
	at org.eclipse.e4.ui.internal.workbench.E4Workbench.createAndRunUI(E4Workbench.java:148)
	at org.eclipse.ui.internal.Workbench$5.run(Workbench.java:636)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:332)
	at org.eclipse.ui.internal.Workbench.createAndRunWorkbench(Workbench.java:579)
	at org.eclipse.ui.PlatformUI.createAndRunWorkbench(PlatformUI.java:150)
	at org.eclipse.ui.internal.ide.application.IDEApplication.start(IDEApplication.java:135)
	at org.eclipse.equinox.internal.app.EclipseAppHandle.run(EclipseAppHandle.java:196)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.runApplication(EclipseAppLauncher.java:134)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.start(EclipseAppLauncher.java:104)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:382)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:236)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.eclipse.equinox.launcher.Main.invokeFramework(Main.java:648)
	at org.eclipse.equinox.launcher.Main.basicRun(Main.java:603)
	at org.eclipse.equinox.launcher.Main.run(Main.java:1465)

!ENTRY org.eclipse.e4.ui.workbench 4 0 2025-06-13 10:38:59.788
!MESSAGE 
!STACK 0
org.eclipse.emf.common.util.BasicEList$BasicIndexOutOfBoundsException: index=0, size=0
	at org.eclipse.emf.common.util.BasicEList.get(BasicEList.java:346)
	at org.omnetpp.scave.editors.ScaveEditor.getAnalysis(ScaveEditor.java:530)
	at org.omnetpp.scave.editors.ScaveEditor.createModel(ScaveEditor.java:365)
	at org.omnetpp.scave.editors.AbstractEMFModelEditor.createPages(AbstractEMFModelEditor.java:725)
	at org.eclipse.ui.part.MultiPageEditorPart.createPartControl(MultiPageEditorPart.java:362)
	at org.eclipse.ui.internal.e4.compatibility.CompatibilityPart.createPartControl(CompatibilityPart.java:140)
	at org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor.createPartControl(CompatibilityEditor.java:99)
	at org.eclipse.ui.internal.e4.compatibility.CompatibilityPart.create(CompatibilityPart.java:321)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.eclipse.e4.core.internal.di.MethodRequestor.execute(MethodRequestor.java:55)
	at org.eclipse.e4.core.internal.di.InjectorImpl.processAnnotated(InjectorImpl.java:888)
	at org.eclipse.e4.core.internal.di.InjectorImpl.processAnnotated(InjectorImpl.java:869)
	at org.eclipse.e4.core.internal.di.InjectorImpl.inject(InjectorImpl.java:120)
	at org.eclipse.e4.core.internal.di.InjectorImpl.internalMake(InjectorImpl.java:337)
	at org.eclipse.e4.core.internal.di.InjectorImpl.make(InjectorImpl.java:258)
	at org.eclipse.e4.core.contexts.ContextInjectionFactory.make(ContextInjectionFactory.java:162)
	at org.eclipse.e4.ui.internal.workbench.ReflectionContributionFactory.createFromBundle(ReflectionContributionFactory.java:104)
	at org.eclipse.e4.ui.internal.workbench.ReflectionContributionFactory.doCreate(ReflectionContributionFactory.java:73)
	at org.eclipse.e4.ui.internal.workbench.ReflectionContributionFactory.create(ReflectionContributionFactory.java:55)
	at org.eclipse.e4.ui.workbench.renderers.swt.ContributedPartRenderer.createWidget(ContributedPartRenderer.java:127)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.createWidget(PartRenderingEngine.java:983)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.safeCreateGui(PartRenderingEngine.java:662)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.safeCreateGui(PartRenderingEngine.java:766)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.access$2(PartRenderingEngine.java:737)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine$7.run(PartRenderingEngine.java:731)
	at org.eclipse.core.runtime.SafeRunner.run(SafeRunner.java:42)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.createGui(PartRenderingEngine.java:715)
	at org.eclipse.e4.ui.workbench.renderers.swt.StackRenderer.showTab(StackRenderer.java:1251)
	at org.eclipse.e4.ui.workbench.renderers.swt.LazyStackRenderer$1.handleEvent(LazyStackRenderer.java:66)
	at org.eclipse.e4.ui.services.internal.events.UIEventHandler$1.run(UIEventHandler.java:40)
	at org.eclipse.swt.widgets.Synchronizer.syncExec(Synchronizer.java:187)
	at org.eclipse.ui.internal.UISynchronizer.syncExec(UISynchronizer.java:156)
	at org.eclipse.swt.widgets.Display.syncExec(Display.java:4734)
	at org.eclipse.e4.ui.internal.workbench.swt.E4Application$1.syncExec(E4Application.java:218)
	at org.eclipse.e4.ui.services.internal.events.UIEventHandler.handleEvent(UIEventHandler.java:36)
	at org.eclipse.equinox.internal.event.EventHandlerWrapper.handleEvent(EventHandlerWrapper.java:197)
	at org.eclipse.equinox.internal.event.EventHandlerTracker.dispatchEvent(EventHandlerTracker.java:197)
	at org.eclipse.equinox.internal.event.EventHandlerTracker.dispatchEvent(EventHandlerTracker.java:1)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:230)
	at org.eclipse.osgi.framework.eventmgr.ListenerQueue.dispatchEventSynchronous(ListenerQueue.java:148)
	at org.eclipse.equinox.internal.event.EventAdminImpl.dispatchEvent(EventAdminImpl.java:135)
	at org.eclipse.equinox.internal.event.EventAdminImpl.sendEvent(EventAdminImpl.java:78)
	at org.eclipse.equinox.internal.event.EventComponent.sendEvent(EventComponent.java:39)
	at org.eclipse.e4.ui.services.internal.events.EventBroker.send(EventBroker.java:81)
	at org.eclipse.e4.ui.internal.workbench.UIEventPublisher.notifyChanged(UIEventPublisher.java:59)
	at org.eclipse.emf.common.notify.impl.BasicNotifierImpl.eNotify(BasicNotifierImpl.java:374)
	at org.eclipse.e4.ui.model.application.ui.impl.ElementContainerImpl.setSelectedElement(ElementContainerImpl.java:171)
	at org.eclipse.e4.ui.internal.workbench.ModelServiceImpl.showElementInWindow(ModelServiceImpl.java:488)
	at org.eclipse.e4.ui.internal.workbench.ModelServiceImpl.bringToTop(ModelServiceImpl.java:454)
	at org.eclipse.e4.ui.internal.workbench.PartServiceImpl.delegateBringToTop(PartServiceImpl.java:692)
	at org.eclipse.e4.ui.internal.workbench.PartServiceImpl.bringToTop(PartServiceImpl.java:385)
	at org.eclipse.e4.ui.internal.workbench.PartServiceImpl.showPart(PartServiceImpl.java:1126)
	at org.eclipse.ui.internal.WorkbenchPage.busyOpenEditor(WorkbenchPage.java:3182)
	at org.eclipse.ui.internal.WorkbenchPage.access$23(WorkbenchPage.java:3104)
	at org.eclipse.ui.internal.WorkbenchPage$9.run(WorkbenchPage.java:3086)
	at org.eclipse.swt.custom.BusyIndicator.showWhile(BusyIndicator.java:70)
	at org.eclipse.ui.internal.WorkbenchPage.openEditor(WorkbenchPage.java:3081)
	at org.eclipse.ui.internal.WorkbenchPage.openEditor(WorkbenchPage.java:3045)
	at org.eclipse.ui.internal.WorkbenchPage.openEditor(WorkbenchPage.java:3035)
	at org.eclipse.ui.ide.IDE.openEditor(IDE.java:541)
	at org.eclipse.ui.ide.IDE.openEditor(IDE.java:500)
	at org.eclipse.ui.actions.OpenFileAction.openFile(OpenFileAction.java:99)
	at org.eclipse.ui.actions.OpenSystemEditorAction.run(OpenSystemEditorAction.java:99)
	at org.eclipse.ui.actions.RetargetAction.run(RetargetAction.java:229)
	at org.eclipse.ui.navigator.CommonNavigatorManager$3.open(CommonNavigatorManager.java:185)
	at org.eclipse.ui.OpenAndLinkWithEditorHelper$InternalListener.open(OpenAndLinkWithEditorHelper.java:48)
	at org.eclipse.jface.viewers.StructuredViewer$2.run(StructuredViewer.java:853)
	at org.eclipse.core.runtime.SafeRunner.run(SafeRunner.java:42)
	at org.eclipse.ui.internal.JFaceUtil$1.run(JFaceUtil.java:50)
	at org.eclipse.jface.util.SafeRunnable.run(SafeRunnable.java:178)
	at org.eclipse.jface.viewers.StructuredViewer.fireOpen(StructuredViewer.java:850)
	at org.eclipse.jface.viewers.StructuredViewer.handleOpen(StructuredViewer.java:1142)
	at org.eclipse.ui.navigator.CommonViewer.handleOpen(CommonViewer.java:462)
	at org.eclipse.jface.viewers.StructuredViewer$6.handleOpen(StructuredViewer.java:1249)
	at org.eclipse.jface.util.OpenStrategy.fireOpenEvent(OpenStrategy.java:278)
	at org.eclipse.jface.util.OpenStrategy.access$2(OpenStrategy.java:272)
	at org.eclipse.jface.util.OpenStrategy$1.handleEvent(OpenStrategy.java:313)
	at org.eclipse.swt.widgets.EventTable.sendEvent(EventTable.java:84)
	at org.eclipse.swt.widgets.Display.sendEvent(Display.java:4353)
	at org.eclipse.swt.widgets.Widget.sendEvent(Widget.java:1061)
	at org.eclipse.swt.widgets.Display.runDeferredEvents(Display.java:4172)
	at org.eclipse.swt.widgets.Display.readAndDispatch(Display.java:3761)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine$9.run(PartRenderingEngine.java:1151)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:332)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.run(PartRenderingEngine.java:1032)
	at org.eclipse.e4.ui.internal.workbench.E4Workbench.createAndRunUI(E4Workbench.java:148)
	at org.eclipse.ui.internal.Workbench$5.run(Workbench.java:636)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:332)
	at org.eclipse.ui.internal.Workbench.createAndRunWorkbench(Workbench.java:579)
	at org.eclipse.ui.PlatformUI.createAndRunWorkbench(PlatformUI.java:150)
	at org.eclipse.ui.internal.ide.application.IDEApplication.start(IDEApplication.java:135)
	at org.eclipse.equinox.internal.app.EclipseAppHandle.run(EclipseAppHandle.java:196)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.runApplication(EclipseAppLauncher.java:134)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.start(EclipseAppLauncher.java:104)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:382)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:236)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.eclipse.equinox.launcher.Main.invokeFramework(Main.java:648)
	at org.eclipse.equinox.launcher.Main.basicRun(Main.java:603)
	at org.eclipse.equinox.launcher.Main.run(Main.java:1465)

!ENTRY org.eclipse.e4.ui.workbench 4 0 2025-06-13 10:38:59.789
!MESSAGE 
!STACK 0
java.lang.NullPointerException
	at org.omnetpp.scave.computed.ComputedScalarManager.dispose(ComputedScalarManager.java:63)
	at org.omnetpp.scave.editors.ScaveEditor.dispose(ScaveEditor.java:265)
	at org.eclipse.ui.internal.e4.compatibility.CompatibilityPart.createPartControl(CompatibilityPart.java:146)
	at org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor.createPartControl(CompatibilityEditor.java:99)
	at org.eclipse.ui.internal.e4.compatibility.CompatibilityPart.create(CompatibilityPart.java:321)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.eclipse.e4.core.internal.di.MethodRequestor.execute(MethodRequestor.java:55)
	at org.eclipse.e4.core.internal.di.InjectorImpl.processAnnotated(InjectorImpl.java:888)
	at org.eclipse.e4.core.internal.di.InjectorImpl.processAnnotated(InjectorImpl.java:869)
	at org.eclipse.e4.core.internal.di.InjectorImpl.inject(InjectorImpl.java:120)
	at org.eclipse.e4.core.internal.di.InjectorImpl.internalMake(InjectorImpl.java:337)
	at org.eclipse.e4.core.internal.di.InjectorImpl.make(InjectorImpl.java:258)
	at org.eclipse.e4.core.contexts.ContextInjectionFactory.make(ContextInjectionFactory.java:162)
	at org.eclipse.e4.ui.internal.workbench.ReflectionContributionFactory.createFromBundle(ReflectionContributionFactory.java:104)
	at org.eclipse.e4.ui.internal.workbench.ReflectionContributionFactory.doCreate(ReflectionContributionFactory.java:73)
	at org.eclipse.e4.ui.internal.workbench.ReflectionContributionFactory.create(ReflectionContributionFactory.java:55)
	at org.eclipse.e4.ui.workbench.renderers.swt.ContributedPartRenderer.createWidget(ContributedPartRenderer.java:127)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.createWidget(PartRenderingEngine.java:983)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.safeCreateGui(PartRenderingEngine.java:662)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.safeCreateGui(PartRenderingEngine.java:766)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.access$2(PartRenderingEngine.java:737)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine$7.run(PartRenderingEngine.java:731)
	at org.eclipse.core.runtime.SafeRunner.run(SafeRunner.java:42)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.createGui(PartRenderingEngine.java:715)
	at org.eclipse.e4.ui.workbench.renderers.swt.StackRenderer.showTab(StackRenderer.java:1251)
	at org.eclipse.e4.ui.workbench.renderers.swt.LazyStackRenderer$1.handleEvent(LazyStackRenderer.java:66)
	at org.eclipse.e4.ui.services.internal.events.UIEventHandler$1.run(UIEventHandler.java:40)
	at org.eclipse.swt.widgets.Synchronizer.syncExec(Synchronizer.java:187)
	at org.eclipse.ui.internal.UISynchronizer.syncExec(UISynchronizer.java:156)
	at org.eclipse.swt.widgets.Display.syncExec(Display.java:4734)
	at org.eclipse.e4.ui.internal.workbench.swt.E4Application$1.syncExec(E4Application.java:218)
	at org.eclipse.e4.ui.services.internal.events.UIEventHandler.handleEvent(UIEventHandler.java:36)
	at org.eclipse.equinox.internal.event.EventHandlerWrapper.handleEvent(EventHandlerWrapper.java:197)
	at org.eclipse.equinox.internal.event.EventHandlerTracker.dispatchEvent(EventHandlerTracker.java:197)
	at org.eclipse.equinox.internal.event.EventHandlerTracker.dispatchEvent(EventHandlerTracker.java:1)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:230)
	at org.eclipse.osgi.framework.eventmgr.ListenerQueue.dispatchEventSynchronous(ListenerQueue.java:148)
	at org.eclipse.equinox.internal.event.EventAdminImpl.dispatchEvent(EventAdminImpl.java:135)
	at org.eclipse.equinox.internal.event.EventAdminImpl.sendEvent(EventAdminImpl.java:78)
	at org.eclipse.equinox.internal.event.EventComponent.sendEvent(EventComponent.java:39)
	at org.eclipse.e4.ui.services.internal.events.EventBroker.send(EventBroker.java:81)
	at org.eclipse.e4.ui.internal.workbench.UIEventPublisher.notifyChanged(UIEventPublisher.java:59)
	at org.eclipse.emf.common.notify.impl.BasicNotifierImpl.eNotify(BasicNotifierImpl.java:374)
	at org.eclipse.e4.ui.model.application.ui.impl.ElementContainerImpl.setSelectedElement(ElementContainerImpl.java:171)
	at org.eclipse.e4.ui.internal.workbench.ModelServiceImpl.showElementInWindow(ModelServiceImpl.java:488)
	at org.eclipse.e4.ui.internal.workbench.ModelServiceImpl.bringToTop(ModelServiceImpl.java:454)
	at org.eclipse.e4.ui.internal.workbench.PartServiceImpl.delegateBringToTop(PartServiceImpl.java:692)
	at org.eclipse.e4.ui.internal.workbench.PartServiceImpl.bringToTop(PartServiceImpl.java:385)
	at org.eclipse.e4.ui.internal.workbench.PartServiceImpl.showPart(PartServiceImpl.java:1126)
	at org.eclipse.ui.internal.WorkbenchPage.busyOpenEditor(WorkbenchPage.java:3182)
	at org.eclipse.ui.internal.WorkbenchPage.access$23(WorkbenchPage.java:3104)
	at org.eclipse.ui.internal.WorkbenchPage$9.run(WorkbenchPage.java:3086)
	at org.eclipse.swt.custom.BusyIndicator.showWhile(BusyIndicator.java:70)
	at org.eclipse.ui.internal.WorkbenchPage.openEditor(WorkbenchPage.java:3081)
	at org.eclipse.ui.internal.WorkbenchPage.openEditor(WorkbenchPage.java:3045)
	at org.eclipse.ui.internal.WorkbenchPage.openEditor(WorkbenchPage.java:3035)
	at org.eclipse.ui.ide.IDE.openEditor(IDE.java:541)
	at org.eclipse.ui.ide.IDE.openEditor(IDE.java:500)
	at org.eclipse.ui.actions.OpenFileAction.openFile(OpenFileAction.java:99)
	at org.eclipse.ui.actions.OpenSystemEditorAction.run(OpenSystemEditorAction.java:99)
	at org.eclipse.ui.actions.RetargetAction.run(RetargetAction.java:229)
	at org.eclipse.ui.navigator.CommonNavigatorManager$3.open(CommonNavigatorManager.java:185)
	at org.eclipse.ui.OpenAndLinkWithEditorHelper$InternalListener.open(OpenAndLinkWithEditorHelper.java:48)
	at org.eclipse.jface.viewers.StructuredViewer$2.run(StructuredViewer.java:853)
	at org.eclipse.core.runtime.SafeRunner.run(SafeRunner.java:42)
	at org.eclipse.ui.internal.JFaceUtil$1.run(JFaceUtil.java:50)
	at org.eclipse.jface.util.SafeRunnable.run(SafeRunnable.java:178)
	at org.eclipse.jface.viewers.StructuredViewer.fireOpen(StructuredViewer.java:850)
	at org.eclipse.jface.viewers.StructuredViewer.handleOpen(StructuredViewer.java:1142)
	at org.eclipse.ui.navigator.CommonViewer.handleOpen(CommonViewer.java:462)
	at org.eclipse.jface.viewers.StructuredViewer$6.handleOpen(StructuredViewer.java:1249)
	at org.eclipse.jface.util.OpenStrategy.fireOpenEvent(OpenStrategy.java:278)
	at org.eclipse.jface.util.OpenStrategy.access$2(OpenStrategy.java:272)
	at org.eclipse.jface.util.OpenStrategy$1.handleEvent(OpenStrategy.java:313)
	at org.eclipse.swt.widgets.EventTable.sendEvent(EventTable.java:84)
	at org.eclipse.swt.widgets.Display.sendEvent(Display.java:4353)
	at org.eclipse.swt.widgets.Widget.sendEvent(Widget.java:1061)
	at org.eclipse.swt.widgets.Display.runDeferredEvents(Display.java:4172)
	at org.eclipse.swt.widgets.Display.readAndDispatch(Display.java:3761)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine$9.run(PartRenderingEngine.java:1151)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:332)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.run(PartRenderingEngine.java:1032)
	at org.eclipse.e4.ui.internal.workbench.E4Workbench.createAndRunUI(E4Workbench.java:148)
	at org.eclipse.ui.internal.Workbench$5.run(Workbench.java:636)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:332)
	at org.eclipse.ui.internal.Workbench.createAndRunWorkbench(Workbench.java:579)
	at org.eclipse.ui.PlatformUI.createAndRunWorkbench(PlatformUI.java:150)
	at org.eclipse.ui.internal.ide.application.IDEApplication.start(IDEApplication.java:135)
	at org.eclipse.equinox.internal.app.EclipseAppHandle.run(EclipseAppHandle.java:196)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.runApplication(EclipseAppLauncher.java:134)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.start(EclipseAppLauncher.java:104)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:382)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:236)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.eclipse.equinox.launcher.Main.invokeFramework(Main.java:648)
	at org.eclipse.equinox.launcher.Main.basicRun(Main.java:603)
	at org.eclipse.equinox.launcher.Main.run(Main.java:1465)

!ENTRY org.eclipse.core.jobs 4 2 2025-06-13 10:38:59.790
!MESSAGE An internal error occurred during: "Updating problem markers".
!STACK 0
java.lang.NullPointerException
	at org.omnetpp.common.markers.ProblemMarkerSynchronizer.addRemoveMarkers(ProblemMarkerSynchronizer.java:199)
	at org.omnetpp.common.markers.ProblemMarkerSynchronizer$1.runInWorkspace(ProblemMarkerSynchronizer.java:177)
	at org.eclipse.core.internal.resources.InternalWorkspaceJob.run(InternalWorkspaceJob.java:38)
	at org.eclipse.core.internal.jobs.Worker.run(Worker.java:54)

!ENTRY org.eclipse.e4.ui.workbench 4 2 2025-06-13 10:38:59.812
!MESSAGE Problems occurred when invoking code from plug-in: "org.eclipse.e4.ui.workbench".
!STACK 0
java.lang.NullPointerException
	at org.omnetpp.scave.model.presentation.ScaveModelActionBarContributor.setActiveEditor(ScaveModelActionBarContributor.java:253)
	at org.eclipse.ui.internal.EditorActionBars.partChanged(EditorActionBars.java:354)
	at org.eclipse.ui.internal.WorkbenchPage.updateActivations(WorkbenchPage.java:298)
	at org.eclipse.ui.internal.WorkbenchPage.access$17(WorkbenchPage.java:280)
	at org.eclipse.ui.internal.WorkbenchPage$E4PartListener.partActivated(WorkbenchPage.java:202)
	at org.eclipse.e4.ui.internal.workbench.PartServiceImpl$3.run(PartServiceImpl.java:234)
	at org.eclipse.core.runtime.SafeRunner.run(SafeRunner.java:42)
	at org.eclipse.e4.ui.internal.workbench.PartServiceImpl.firePartActivated(PartServiceImpl.java:231)
	at org.eclipse.e4.ui.internal.workbench.PartServiceImpl.activate(PartServiceImpl.java:682)
	at org.eclipse.e4.ui.internal.workbench.PartServiceImpl.activate(PartServiceImpl.java:613)
	at org.eclipse.e4.ui.internal.workbench.PartServiceImpl.activate(PartServiceImpl.java:601)
	at org.eclipse.ui.internal.WorkbenchPage.busyOpenEditor(WorkbenchPage.java:3190)
	at org.eclipse.ui.internal.WorkbenchPage.access$23(WorkbenchPage.java:3104)
	at org.eclipse.ui.internal.WorkbenchPage$9.run(WorkbenchPage.java:3086)
	at org.eclipse.swt.custom.BusyIndicator.showWhile(BusyIndicator.java:70)
	at org.eclipse.ui.internal.WorkbenchPage.openEditor(WorkbenchPage.java:3081)
	at org.eclipse.ui.internal.WorkbenchPage.openEditor(WorkbenchPage.java:3045)
	at org.eclipse.ui.internal.WorkbenchPage.openEditor(WorkbenchPage.java:3035)
	at org.eclipse.ui.ide.IDE.openEditor(IDE.java:541)
	at org.eclipse.ui.ide.IDE.openEditor(IDE.java:500)
	at org.eclipse.ui.actions.OpenFileAction.openFile(OpenFileAction.java:99)
	at org.eclipse.ui.actions.OpenSystemEditorAction.run(OpenSystemEditorAction.java:99)
	at org.eclipse.ui.actions.RetargetAction.run(RetargetAction.java:229)
	at org.eclipse.ui.navigator.CommonNavigatorManager$3.open(CommonNavigatorManager.java:185)
	at org.eclipse.ui.OpenAndLinkWithEditorHelper$InternalListener.open(OpenAndLinkWithEditorHelper.java:48)
	at org.eclipse.jface.viewers.StructuredViewer$2.run(StructuredViewer.java:853)
	at org.eclipse.core.runtime.SafeRunner.run(SafeRunner.java:42)
	at org.eclipse.ui.internal.JFaceUtil$1.run(JFaceUtil.java:50)
	at org.eclipse.jface.util.SafeRunnable.run(SafeRunnable.java:178)
	at org.eclipse.jface.viewers.StructuredViewer.fireOpen(StructuredViewer.java:850)
	at org.eclipse.jface.viewers.StructuredViewer.handleOpen(StructuredViewer.java:1142)
	at org.eclipse.ui.navigator.CommonViewer.handleOpen(CommonViewer.java:462)
	at org.eclipse.jface.viewers.StructuredViewer$6.handleOpen(StructuredViewer.java:1249)
	at org.eclipse.jface.util.OpenStrategy.fireOpenEvent(OpenStrategy.java:278)
	at org.eclipse.jface.util.OpenStrategy.access$2(OpenStrategy.java:272)
	at org.eclipse.jface.util.OpenStrategy$1.handleEvent(OpenStrategy.java:313)
	at org.eclipse.swt.widgets.EventTable.sendEvent(EventTable.java:84)
	at org.eclipse.swt.widgets.Display.sendEvent(Display.java:4353)
	at org.eclipse.swt.widgets.Widget.sendEvent(Widget.java:1061)
	at org.eclipse.swt.widgets.Display.runDeferredEvents(Display.java:4172)
	at org.eclipse.swt.widgets.Display.readAndDispatch(Display.java:3761)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine$9.run(PartRenderingEngine.java:1151)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:332)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.run(PartRenderingEngine.java:1032)
	at org.eclipse.e4.ui.internal.workbench.E4Workbench.createAndRunUI(E4Workbench.java:148)
	at org.eclipse.ui.internal.Workbench$5.run(Workbench.java:636)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:332)
	at org.eclipse.ui.internal.Workbench.createAndRunWorkbench(Workbench.java:579)
	at org.eclipse.ui.PlatformUI.createAndRunWorkbench(PlatformUI.java:150)
	at org.eclipse.ui.internal.ide.application.IDEApplication.start(IDEApplication.java:135)
	at org.eclipse.equinox.internal.app.EclipseAppHandle.run(EclipseAppHandle.java:196)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.runApplication(EclipseAppLauncher.java:134)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.start(EclipseAppLauncher.java:104)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:382)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:236)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.eclipse.equinox.launcher.Main.invokeFramework(Main.java:648)
	at org.eclipse.equinox.launcher.Main.basicRun(Main.java:603)
	at org.eclipse.equinox.launcher.Main.run(Main.java:1465)

!ENTRY org.eclipse.e4.ui.workbench 4 0 2025-06-13 10:38:59.813
!MESSAGE An exception occurred while notifying part listeners
!STACK 0
java.lang.NullPointerException
	at org.omnetpp.scave.model.presentation.ScaveModelActionBarContributor.setActiveEditor(ScaveModelActionBarContributor.java:253)
	at org.eclipse.ui.internal.EditorActionBars.partChanged(EditorActionBars.java:354)
	at org.eclipse.ui.internal.WorkbenchPage.updateActivations(WorkbenchPage.java:298)
	at org.eclipse.ui.internal.WorkbenchPage.access$17(WorkbenchPage.java:280)
	at org.eclipse.ui.internal.WorkbenchPage$E4PartListener.partActivated(WorkbenchPage.java:202)
	at org.eclipse.e4.ui.internal.workbench.PartServiceImpl$3.run(PartServiceImpl.java:234)
	at org.eclipse.core.runtime.SafeRunner.run(SafeRunner.java:42)
	at org.eclipse.e4.ui.internal.workbench.PartServiceImpl.firePartActivated(PartServiceImpl.java:231)
	at org.eclipse.e4.ui.internal.workbench.PartServiceImpl.activate(PartServiceImpl.java:682)
	at org.eclipse.e4.ui.internal.workbench.PartServiceImpl.activate(PartServiceImpl.java:613)
	at org.eclipse.e4.ui.internal.workbench.PartServiceImpl.activate(PartServiceImpl.java:601)
	at org.eclipse.ui.internal.WorkbenchPage.busyOpenEditor(WorkbenchPage.java:3190)
	at org.eclipse.ui.internal.WorkbenchPage.access$23(WorkbenchPage.java:3104)
	at org.eclipse.ui.internal.WorkbenchPage$9.run(WorkbenchPage.java:3086)
	at org.eclipse.swt.custom.BusyIndicator.showWhile(BusyIndicator.java:70)
	at org.eclipse.ui.internal.WorkbenchPage.openEditor(WorkbenchPage.java:3081)
	at org.eclipse.ui.internal.WorkbenchPage.openEditor(WorkbenchPage.java:3045)
	at org.eclipse.ui.internal.WorkbenchPage.openEditor(WorkbenchPage.java:3035)
	at org.eclipse.ui.ide.IDE.openEditor(IDE.java:541)
	at org.eclipse.ui.ide.IDE.openEditor(IDE.java:500)
	at org.eclipse.ui.actions.OpenFileAction.openFile(OpenFileAction.java:99)
	at org.eclipse.ui.actions.OpenSystemEditorAction.run(OpenSystemEditorAction.java:99)
	at org.eclipse.ui.actions.RetargetAction.run(RetargetAction.java:229)
	at org.eclipse.ui.navigator.CommonNavigatorManager$3.open(CommonNavigatorManager.java:185)
	at org.eclipse.ui.OpenAndLinkWithEditorHelper$InternalListener.open(OpenAndLinkWithEditorHelper.java:48)
	at org.eclipse.jface.viewers.StructuredViewer$2.run(StructuredViewer.java:853)
	at org.eclipse.core.runtime.SafeRunner.run(SafeRunner.java:42)
	at org.eclipse.ui.internal.JFaceUtil$1.run(JFaceUtil.java:50)
	at org.eclipse.jface.util.SafeRunnable.run(SafeRunnable.java:178)
	at org.eclipse.jface.viewers.StructuredViewer.fireOpen(StructuredViewer.java:850)
	at org.eclipse.jface.viewers.StructuredViewer.handleOpen(StructuredViewer.java:1142)
	at org.eclipse.ui.navigator.CommonViewer.handleOpen(CommonViewer.java:462)
	at org.eclipse.jface.viewers.StructuredViewer$6.handleOpen(StructuredViewer.java:1249)
	at org.eclipse.jface.util.OpenStrategy.fireOpenEvent(OpenStrategy.java:278)
	at org.eclipse.jface.util.OpenStrategy.access$2(OpenStrategy.java:272)
	at org.eclipse.jface.util.OpenStrategy$1.handleEvent(OpenStrategy.java:313)
	at org.eclipse.swt.widgets.EventTable.sendEvent(EventTable.java:84)
	at org.eclipse.swt.widgets.Display.sendEvent(Display.java:4353)
	at org.eclipse.swt.widgets.Widget.sendEvent(Widget.java:1061)
	at org.eclipse.swt.widgets.Display.runDeferredEvents(Display.java:4172)
	at org.eclipse.swt.widgets.Display.readAndDispatch(Display.java:3761)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine$9.run(PartRenderingEngine.java:1151)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:332)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.run(PartRenderingEngine.java:1032)
	at org.eclipse.e4.ui.internal.workbench.E4Workbench.createAndRunUI(E4Workbench.java:148)
	at org.eclipse.ui.internal.Workbench$5.run(Workbench.java:636)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:332)
	at org.eclipse.ui.internal.Workbench.createAndRunWorkbench(Workbench.java:579)
	at org.eclipse.ui.PlatformUI.createAndRunWorkbench(PlatformUI.java:150)
	at org.eclipse.ui.internal.ide.application.IDEApplication.start(IDEApplication.java:135)
	at org.eclipse.equinox.internal.app.EclipseAppHandle.run(EclipseAppHandle.java:196)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.runApplication(EclipseAppLauncher.java:134)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.start(EclipseAppLauncher.java:104)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:382)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:236)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.eclipse.equinox.launcher.Main.invokeFramework(Main.java:648)
	at org.eclipse.equinox.launcher.Main.basicRun(Main.java:603)
	at org.eclipse.equinox.launcher.Main.run(Main.java:1465)

!ENTRY org.eclipse.e4.ui.workbench 4 2 2025-06-13 10:41:50.456
!MESSAGE Problems occurred when invoking code from plug-in: "org.eclipse.e4.ui.workbench".
!STACK 0
java.lang.NullPointerException
	at org.omnetpp.scave.model.presentation.ScaveModelActionBarContributor.setActiveEditor(ScaveModelActionBarContributor.java:253)
	at org.eclipse.ui.internal.EditorActionBars.partChanged(EditorActionBars.java:354)
	at org.eclipse.ui.internal.WorkbenchPage.updateActivations(WorkbenchPage.java:298)
	at org.eclipse.ui.internal.WorkbenchPage.access$17(WorkbenchPage.java:280)
	at org.eclipse.ui.internal.WorkbenchPage$E4PartListener.partActivated(WorkbenchPage.java:202)
	at org.eclipse.e4.ui.internal.workbench.PartServiceImpl$3.run(PartServiceImpl.java:234)
	at org.eclipse.core.runtime.SafeRunner.run(SafeRunner.java:42)
	at org.eclipse.e4.ui.internal.workbench.PartServiceImpl.firePartActivated(PartServiceImpl.java:231)
	at org.eclipse.e4.ui.internal.workbench.PartServiceImpl.activate(PartServiceImpl.java:682)
	at org.eclipse.e4.ui.internal.workbench.PartServiceImpl.activate(PartServiceImpl.java:613)
	at org.eclipse.e4.ui.internal.workbench.PartServiceImpl.activate(PartServiceImpl.java:601)
	at org.eclipse.ui.internal.WorkbenchPage.activate(WorkbenchPage.java:972)
	at org.eclipse.ui.internal.WorkbenchPage.busyOpenEditor(WorkbenchPage.java:3143)
	at org.eclipse.ui.internal.WorkbenchPage.access$23(WorkbenchPage.java:3104)
	at org.eclipse.ui.internal.WorkbenchPage$9.run(WorkbenchPage.java:3086)
	at org.eclipse.swt.custom.BusyIndicator.showWhile(BusyIndicator.java:70)
	at org.eclipse.ui.internal.WorkbenchPage.openEditor(WorkbenchPage.java:3081)
	at org.eclipse.ui.internal.WorkbenchPage.openEditor(WorkbenchPage.java:3045)
	at org.eclipse.ui.internal.WorkbenchPage.openEditor(WorkbenchPage.java:3035)
	at org.eclipse.ui.ide.IDE.openEditor(IDE.java:541)
	at org.eclipse.ui.ide.IDE.openEditor(IDE.java:500)
	at org.eclipse.ui.actions.OpenFileAction.openFile(OpenFileAction.java:99)
	at org.eclipse.ui.actions.OpenSystemEditorAction.run(OpenSystemEditorAction.java:99)
	at org.eclipse.ui.actions.RetargetAction.run(RetargetAction.java:229)
	at org.eclipse.ui.navigator.CommonNavigatorManager$3.open(CommonNavigatorManager.java:185)
	at org.eclipse.ui.OpenAndLinkWithEditorHelper$InternalListener.open(OpenAndLinkWithEditorHelper.java:48)
	at org.eclipse.jface.viewers.StructuredViewer$2.run(StructuredViewer.java:853)
	at org.eclipse.core.runtime.SafeRunner.run(SafeRunner.java:42)
	at org.eclipse.ui.internal.JFaceUtil$1.run(JFaceUtil.java:50)
	at org.eclipse.jface.util.SafeRunnable.run(SafeRunnable.java:178)
	at org.eclipse.jface.viewers.StructuredViewer.fireOpen(StructuredViewer.java:850)
	at org.eclipse.jface.viewers.StructuredViewer.handleOpen(StructuredViewer.java:1142)
	at org.eclipse.ui.navigator.CommonViewer.handleOpen(CommonViewer.java:462)
	at org.eclipse.jface.viewers.StructuredViewer$6.handleOpen(StructuredViewer.java:1249)
	at org.eclipse.jface.util.OpenStrategy.fireOpenEvent(OpenStrategy.java:278)
	at org.eclipse.jface.util.OpenStrategy.access$2(OpenStrategy.java:272)
	at org.eclipse.jface.util.OpenStrategy$1.handleEvent(OpenStrategy.java:313)
	at org.eclipse.swt.widgets.EventTable.sendEvent(EventTable.java:84)
	at org.eclipse.swt.widgets.Display.sendEvent(Display.java:4353)
	at org.eclipse.swt.widgets.Widget.sendEvent(Widget.java:1061)
	at org.eclipse.swt.widgets.Display.runDeferredEvents(Display.java:4172)
	at org.eclipse.swt.widgets.Display.readAndDispatch(Display.java:3761)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine$9.run(PartRenderingEngine.java:1151)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:332)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.run(PartRenderingEngine.java:1032)
	at org.eclipse.e4.ui.internal.workbench.E4Workbench.createAndRunUI(E4Workbench.java:148)
	at org.eclipse.ui.internal.Workbench$5.run(Workbench.java:636)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:332)
	at org.eclipse.ui.internal.Workbench.createAndRunWorkbench(Workbench.java:579)
	at org.eclipse.ui.PlatformUI.createAndRunWorkbench(PlatformUI.java:150)
	at org.eclipse.ui.internal.ide.application.IDEApplication.start(IDEApplication.java:135)
	at org.eclipse.equinox.internal.app.EclipseAppHandle.run(EclipseAppHandle.java:196)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.runApplication(EclipseAppLauncher.java:134)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.start(EclipseAppLauncher.java:104)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:382)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:236)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.eclipse.equinox.launcher.Main.invokeFramework(Main.java:648)
	at org.eclipse.equinox.launcher.Main.basicRun(Main.java:603)
	at org.eclipse.equinox.launcher.Main.run(Main.java:1465)

!ENTRY org.eclipse.e4.ui.workbench 4 0 2025-06-13 10:41:50.457
!MESSAGE An exception occurred while notifying part listeners
!STACK 0
java.lang.NullPointerException
	at org.omnetpp.scave.model.presentation.ScaveModelActionBarContributor.setActiveEditor(ScaveModelActionBarContributor.java:253)
	at org.eclipse.ui.internal.EditorActionBars.partChanged(EditorActionBars.java:354)
	at org.eclipse.ui.internal.WorkbenchPage.updateActivations(WorkbenchPage.java:298)
	at org.eclipse.ui.internal.WorkbenchPage.access$17(WorkbenchPage.java:280)
	at org.eclipse.ui.internal.WorkbenchPage$E4PartListener.partActivated(WorkbenchPage.java:202)
	at org.eclipse.e4.ui.internal.workbench.PartServiceImpl$3.run(PartServiceImpl.java:234)
	at org.eclipse.core.runtime.SafeRunner.run(SafeRunner.java:42)
	at org.eclipse.e4.ui.internal.workbench.PartServiceImpl.firePartActivated(PartServiceImpl.java:231)
	at org.eclipse.e4.ui.internal.workbench.PartServiceImpl.activate(PartServiceImpl.java:682)
	at org.eclipse.e4.ui.internal.workbench.PartServiceImpl.activate(PartServiceImpl.java:613)
	at org.eclipse.e4.ui.internal.workbench.PartServiceImpl.activate(PartServiceImpl.java:601)
	at org.eclipse.ui.internal.WorkbenchPage.activate(WorkbenchPage.java:972)
	at org.eclipse.ui.internal.WorkbenchPage.busyOpenEditor(WorkbenchPage.java:3143)
	at org.eclipse.ui.internal.WorkbenchPage.access$23(WorkbenchPage.java:3104)
	at org.eclipse.ui.internal.WorkbenchPage$9.run(WorkbenchPage.java:3086)
	at org.eclipse.swt.custom.BusyIndicator.showWhile(BusyIndicator.java:70)
	at org.eclipse.ui.internal.WorkbenchPage.openEditor(WorkbenchPage.java:3081)
	at org.eclipse.ui.internal.WorkbenchPage.openEditor(WorkbenchPage.java:3045)
	at org.eclipse.ui.internal.WorkbenchPage.openEditor(WorkbenchPage.java:3035)
	at org.eclipse.ui.ide.IDE.openEditor(IDE.java:541)
	at org.eclipse.ui.ide.IDE.openEditor(IDE.java:500)
	at org.eclipse.ui.actions.OpenFileAction.openFile(OpenFileAction.java:99)
	at org.eclipse.ui.actions.OpenSystemEditorAction.run(OpenSystemEditorAction.java:99)
	at org.eclipse.ui.actions.RetargetAction.run(RetargetAction.java:229)
	at org.eclipse.ui.navigator.CommonNavigatorManager$3.open(CommonNavigatorManager.java:185)
	at org.eclipse.ui.OpenAndLinkWithEditorHelper$InternalListener.open(OpenAndLinkWithEditorHelper.java:48)
	at org.eclipse.jface.viewers.StructuredViewer$2.run(StructuredViewer.java:853)
	at org.eclipse.core.runtime.SafeRunner.run(SafeRunner.java:42)
	at org.eclipse.ui.internal.JFaceUtil$1.run(JFaceUtil.java:50)
	at org.eclipse.jface.util.SafeRunnable.run(SafeRunnable.java:178)
	at org.eclipse.jface.viewers.StructuredViewer.fireOpen(StructuredViewer.java:850)
	at org.eclipse.jface.viewers.StructuredViewer.handleOpen(StructuredViewer.java:1142)
	at org.eclipse.ui.navigator.CommonViewer.handleOpen(CommonViewer.java:462)
	at org.eclipse.jface.viewers.StructuredViewer$6.handleOpen(StructuredViewer.java:1249)
	at org.eclipse.jface.util.OpenStrategy.fireOpenEvent(OpenStrategy.java:278)
	at org.eclipse.jface.util.OpenStrategy.access$2(OpenStrategy.java:272)
	at org.eclipse.jface.util.OpenStrategy$1.handleEvent(OpenStrategy.java:313)
	at org.eclipse.swt.widgets.EventTable.sendEvent(EventTable.java:84)
	at org.eclipse.swt.widgets.Display.sendEvent(Display.java:4353)
	at org.eclipse.swt.widgets.Widget.sendEvent(Widget.java:1061)
	at org.eclipse.swt.widgets.Display.runDeferredEvents(Display.java:4172)
	at org.eclipse.swt.widgets.Display.readAndDispatch(Display.java:3761)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine$9.run(PartRenderingEngine.java:1151)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:332)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.run(PartRenderingEngine.java:1032)
	at org.eclipse.e4.ui.internal.workbench.E4Workbench.createAndRunUI(E4Workbench.java:148)
	at org.eclipse.ui.internal.Workbench$5.run(Workbench.java:636)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:332)
	at org.eclipse.ui.internal.Workbench.createAndRunWorkbench(Workbench.java:579)
	at org.eclipse.ui.PlatformUI.createAndRunWorkbench(PlatformUI.java:150)
	at org.eclipse.ui.internal.ide.application.IDEApplication.start(IDEApplication.java:135)
	at org.eclipse.equinox.internal.app.EclipseAppHandle.run(EclipseAppHandle.java:196)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.runApplication(EclipseAppLauncher.java:134)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.start(EclipseAppLauncher.java:104)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:382)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:236)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.eclipse.equinox.launcher.Main.invokeFramework(Main.java:648)
	at org.eclipse.equinox.launcher.Main.basicRun(Main.java:603)
	at org.eclipse.equinox.launcher.Main.run(Main.java:1465)

!ENTRY org.eclipse.e4.ui.workbench 4 0 2025-06-13 10:44:38.377
!MESSAGE 
!STACK 0
org.eclipse.emf.common.util.BasicEList$BasicIndexOutOfBoundsException: index=0, size=0
	at org.eclipse.emf.common.util.BasicEList.get(BasicEList.java:346)
	at org.omnetpp.scave.editors.ScaveEditor.getAnalysis(ScaveEditor.java:530)
	at org.omnetpp.scave.editors.ScaveEditor.createModel(ScaveEditor.java:365)
	at org.omnetpp.scave.editors.AbstractEMFModelEditor.createPages(AbstractEMFModelEditor.java:725)
	at org.eclipse.ui.part.MultiPageEditorPart.createPartControl(MultiPageEditorPart.java:362)
	at org.eclipse.ui.internal.e4.compatibility.CompatibilityPart.createPartControl(CompatibilityPart.java:140)
	at org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor.createPartControl(CompatibilityEditor.java:99)
	at org.eclipse.ui.internal.e4.compatibility.CompatibilityPart.create(CompatibilityPart.java:321)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.eclipse.e4.core.internal.di.MethodRequestor.execute(MethodRequestor.java:55)
	at org.eclipse.e4.core.internal.di.InjectorImpl.processAnnotated(InjectorImpl.java:888)
	at org.eclipse.e4.core.internal.di.InjectorImpl.processAnnotated(InjectorImpl.java:869)
	at org.eclipse.e4.core.internal.di.InjectorImpl.inject(InjectorImpl.java:120)
	at org.eclipse.e4.core.internal.di.InjectorImpl.internalMake(InjectorImpl.java:337)
	at org.eclipse.e4.core.internal.di.InjectorImpl.make(InjectorImpl.java:258)
	at org.eclipse.e4.core.contexts.ContextInjectionFactory.make(ContextInjectionFactory.java:162)
	at org.eclipse.e4.ui.internal.workbench.ReflectionContributionFactory.createFromBundle(ReflectionContributionFactory.java:104)
	at org.eclipse.e4.ui.internal.workbench.ReflectionContributionFactory.doCreate(ReflectionContributionFactory.java:73)
	at org.eclipse.e4.ui.internal.workbench.ReflectionContributionFactory.create(ReflectionContributionFactory.java:55)
	at org.eclipse.e4.ui.workbench.renderers.swt.ContributedPartRenderer.createWidget(ContributedPartRenderer.java:127)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.createWidget(PartRenderingEngine.java:983)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.safeCreateGui(PartRenderingEngine.java:662)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.safeCreateGui(PartRenderingEngine.java:766)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.access$2(PartRenderingEngine.java:737)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine$7.run(PartRenderingEngine.java:731)
	at org.eclipse.core.runtime.SafeRunner.run(SafeRunner.java:42)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.createGui(PartRenderingEngine.java:715)
	at org.eclipse.e4.ui.workbench.renderers.swt.StackRenderer.showTab(StackRenderer.java:1251)
	at org.eclipse.e4.ui.workbench.renderers.swt.LazyStackRenderer$1.handleEvent(LazyStackRenderer.java:66)
	at org.eclipse.e4.ui.services.internal.events.UIEventHandler$1.run(UIEventHandler.java:40)
	at org.eclipse.swt.widgets.Synchronizer.syncExec(Synchronizer.java:187)
	at org.eclipse.ui.internal.UISynchronizer.syncExec(UISynchronizer.java:156)
	at org.eclipse.swt.widgets.Display.syncExec(Display.java:4734)
	at org.eclipse.e4.ui.internal.workbench.swt.E4Application$1.syncExec(E4Application.java:218)
	at org.eclipse.e4.ui.services.internal.events.UIEventHandler.handleEvent(UIEventHandler.java:36)
	at org.eclipse.equinox.internal.event.EventHandlerWrapper.handleEvent(EventHandlerWrapper.java:197)
	at org.eclipse.equinox.internal.event.EventHandlerTracker.dispatchEvent(EventHandlerTracker.java:197)
	at org.eclipse.equinox.internal.event.EventHandlerTracker.dispatchEvent(EventHandlerTracker.java:1)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:230)
	at org.eclipse.osgi.framework.eventmgr.ListenerQueue.dispatchEventSynchronous(ListenerQueue.java:148)
	at org.eclipse.equinox.internal.event.EventAdminImpl.dispatchEvent(EventAdminImpl.java:135)
	at org.eclipse.equinox.internal.event.EventAdminImpl.sendEvent(EventAdminImpl.java:78)
	at org.eclipse.equinox.internal.event.EventComponent.sendEvent(EventComponent.java:39)
	at org.eclipse.e4.ui.services.internal.events.EventBroker.send(EventBroker.java:81)
	at org.eclipse.e4.ui.internal.workbench.UIEventPublisher.notifyChanged(UIEventPublisher.java:59)
	at org.eclipse.emf.common.notify.impl.BasicNotifierImpl.eNotify(BasicNotifierImpl.java:374)
	at org.eclipse.e4.ui.model.application.ui.impl.ElementContainerImpl.setSelectedElement(ElementContainerImpl.java:171)
	at org.eclipse.e4.ui.internal.workbench.ModelServiceImpl.showElementInWindow(ModelServiceImpl.java:488)
	at org.eclipse.e4.ui.internal.workbench.ModelServiceImpl.bringToTop(ModelServiceImpl.java:454)
	at org.eclipse.e4.ui.internal.workbench.PartServiceImpl.delegateBringToTop(PartServiceImpl.java:692)
	at org.eclipse.e4.ui.internal.workbench.PartServiceImpl.bringToTop(PartServiceImpl.java:385)
	at org.eclipse.e4.ui.internal.workbench.PartServiceImpl.showPart(PartServiceImpl.java:1126)
	at org.eclipse.ui.internal.WorkbenchPage.busyOpenEditor(WorkbenchPage.java:3182)
	at org.eclipse.ui.internal.WorkbenchPage.access$23(WorkbenchPage.java:3104)
	at org.eclipse.ui.internal.WorkbenchPage$9.run(WorkbenchPage.java:3086)
	at org.eclipse.swt.custom.BusyIndicator.showWhile(BusyIndicator.java:70)
	at org.eclipse.ui.internal.WorkbenchPage.openEditor(WorkbenchPage.java:3081)
	at org.eclipse.ui.internal.WorkbenchPage.openEditor(WorkbenchPage.java:3045)
	at org.eclipse.ui.internal.WorkbenchPage.openEditor(WorkbenchPage.java:3035)
	at org.eclipse.ui.ide.IDE.openEditor(IDE.java:541)
	at org.eclipse.ui.ide.IDE.openEditor(IDE.java:500)
	at org.eclipse.ui.actions.OpenFileAction.openFile(OpenFileAction.java:99)
	at org.eclipse.ui.actions.OpenSystemEditorAction.run(OpenSystemEditorAction.java:99)
	at org.eclipse.ui.actions.RetargetAction.run(RetargetAction.java:229)
	at org.eclipse.ui.navigator.CommonNavigatorManager$3.open(CommonNavigatorManager.java:185)
	at org.eclipse.ui.OpenAndLinkWithEditorHelper$InternalListener.open(OpenAndLinkWithEditorHelper.java:48)
	at org.eclipse.jface.viewers.StructuredViewer$2.run(StructuredViewer.java:853)
	at org.eclipse.core.runtime.SafeRunner.run(SafeRunner.java:42)
	at org.eclipse.ui.internal.JFaceUtil$1.run(JFaceUtil.java:50)
	at org.eclipse.jface.util.SafeRunnable.run(SafeRunnable.java:178)
	at org.eclipse.jface.viewers.StructuredViewer.fireOpen(StructuredViewer.java:850)
	at org.eclipse.jface.viewers.StructuredViewer.handleOpen(StructuredViewer.java:1142)
	at org.eclipse.ui.navigator.CommonViewer.handleOpen(CommonViewer.java:462)
	at org.eclipse.jface.viewers.StructuredViewer$6.handleOpen(StructuredViewer.java:1249)
	at org.eclipse.jface.util.OpenStrategy.fireOpenEvent(OpenStrategy.java:278)
	at org.eclipse.jface.util.OpenStrategy.access$2(OpenStrategy.java:272)
	at org.eclipse.jface.util.OpenStrategy$1.handleEvent(OpenStrategy.java:313)
	at org.eclipse.swt.widgets.EventTable.sendEvent(EventTable.java:84)
	at org.eclipse.swt.widgets.Display.sendEvent(Display.java:4353)
	at org.eclipse.swt.widgets.Widget.sendEvent(Widget.java:1061)
	at org.eclipse.swt.widgets.Display.runDeferredEvents(Display.java:4172)
	at org.eclipse.swt.widgets.Display.readAndDispatch(Display.java:3761)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine$9.run(PartRenderingEngine.java:1151)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:332)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.run(PartRenderingEngine.java:1032)
	at org.eclipse.e4.ui.internal.workbench.E4Workbench.createAndRunUI(E4Workbench.java:148)
	at org.eclipse.ui.internal.Workbench$5.run(Workbench.java:636)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:332)
	at org.eclipse.ui.internal.Workbench.createAndRunWorkbench(Workbench.java:579)
	at org.eclipse.ui.PlatformUI.createAndRunWorkbench(PlatformUI.java:150)
	at org.eclipse.ui.internal.ide.application.IDEApplication.start(IDEApplication.java:135)
	at org.eclipse.equinox.internal.app.EclipseAppHandle.run(EclipseAppHandle.java:196)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.runApplication(EclipseAppLauncher.java:134)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.start(EclipseAppLauncher.java:104)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:382)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:236)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.eclipse.equinox.launcher.Main.invokeFramework(Main.java:648)
	at org.eclipse.equinox.launcher.Main.basicRun(Main.java:603)
	at org.eclipse.equinox.launcher.Main.run(Main.java:1465)

!ENTRY org.eclipse.e4.ui.workbench 4 0 2025-06-13 10:44:38.378
!MESSAGE 
!STACK 0
java.lang.NullPointerException
	at org.omnetpp.scave.computed.ComputedScalarManager.dispose(ComputedScalarManager.java:63)
	at org.omnetpp.scave.editors.ScaveEditor.dispose(ScaveEditor.java:265)
	at org.eclipse.ui.internal.e4.compatibility.CompatibilityPart.createPartControl(CompatibilityPart.java:146)
	at org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor.createPartControl(CompatibilityEditor.java:99)
	at org.eclipse.ui.internal.e4.compatibility.CompatibilityPart.create(CompatibilityPart.java:321)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.eclipse.e4.core.internal.di.MethodRequestor.execute(MethodRequestor.java:55)
	at org.eclipse.e4.core.internal.di.InjectorImpl.processAnnotated(InjectorImpl.java:888)
	at org.eclipse.e4.core.internal.di.InjectorImpl.processAnnotated(InjectorImpl.java:869)
	at org.eclipse.e4.core.internal.di.InjectorImpl.inject(InjectorImpl.java:120)
	at org.eclipse.e4.core.internal.di.InjectorImpl.internalMake(InjectorImpl.java:337)
	at org.eclipse.e4.core.internal.di.InjectorImpl.make(InjectorImpl.java:258)
	at org.eclipse.e4.core.contexts.ContextInjectionFactory.make(ContextInjectionFactory.java:162)
	at org.eclipse.e4.ui.internal.workbench.ReflectionContributionFactory.createFromBundle(ReflectionContributionFactory.java:104)
	at org.eclipse.e4.ui.internal.workbench.ReflectionContributionFactory.doCreate(ReflectionContributionFactory.java:73)
	at org.eclipse.e4.ui.internal.workbench.ReflectionContributionFactory.create(ReflectionContributionFactory.java:55)
	at org.eclipse.e4.ui.workbench.renderers.swt.ContributedPartRenderer.createWidget(ContributedPartRenderer.java:127)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.createWidget(PartRenderingEngine.java:983)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.safeCreateGui(PartRenderingEngine.java:662)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.safeCreateGui(PartRenderingEngine.java:766)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.access$2(PartRenderingEngine.java:737)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine$7.run(PartRenderingEngine.java:731)
	at org.eclipse.core.runtime.SafeRunner.run(SafeRunner.java:42)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.createGui(PartRenderingEngine.java:715)
	at org.eclipse.e4.ui.workbench.renderers.swt.StackRenderer.showTab(StackRenderer.java:1251)
	at org.eclipse.e4.ui.workbench.renderers.swt.LazyStackRenderer$1.handleEvent(LazyStackRenderer.java:66)
	at org.eclipse.e4.ui.services.internal.events.UIEventHandler$1.run(UIEventHandler.java:40)
	at org.eclipse.swt.widgets.Synchronizer.syncExec(Synchronizer.java:187)
	at org.eclipse.ui.internal.UISynchronizer.syncExec(UISynchronizer.java:156)
	at org.eclipse.swt.widgets.Display.syncExec(Display.java:4734)
	at org.eclipse.e4.ui.internal.workbench.swt.E4Application$1.syncExec(E4Application.java:218)
	at org.eclipse.e4.ui.services.internal.events.UIEventHandler.handleEvent(UIEventHandler.java:36)
	at org.eclipse.equinox.internal.event.EventHandlerWrapper.handleEvent(EventHandlerWrapper.java:197)
	at org.eclipse.equinox.internal.event.EventHandlerTracker.dispatchEvent(EventHandlerTracker.java:197)
	at org.eclipse.equinox.internal.event.EventHandlerTracker.dispatchEvent(EventHandlerTracker.java:1)
	at org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(EventManager.java:230)
	at org.eclipse.osgi.framework.eventmgr.ListenerQueue.dispatchEventSynchronous(ListenerQueue.java:148)
	at org.eclipse.equinox.internal.event.EventAdminImpl.dispatchEvent(EventAdminImpl.java:135)
	at org.eclipse.equinox.internal.event.EventAdminImpl.sendEvent(EventAdminImpl.java:78)
	at org.eclipse.equinox.internal.event.EventComponent.sendEvent(EventComponent.java:39)
	at org.eclipse.e4.ui.services.internal.events.EventBroker.send(EventBroker.java:81)
	at org.eclipse.e4.ui.internal.workbench.UIEventPublisher.notifyChanged(UIEventPublisher.java:59)
	at org.eclipse.emf.common.notify.impl.BasicNotifierImpl.eNotify(BasicNotifierImpl.java:374)
	at org.eclipse.e4.ui.model.application.ui.impl.ElementContainerImpl.setSelectedElement(ElementContainerImpl.java:171)
	at org.eclipse.e4.ui.internal.workbench.ModelServiceImpl.showElementInWindow(ModelServiceImpl.java:488)
	at org.eclipse.e4.ui.internal.workbench.ModelServiceImpl.bringToTop(ModelServiceImpl.java:454)
	at org.eclipse.e4.ui.internal.workbench.PartServiceImpl.delegateBringToTop(PartServiceImpl.java:692)
	at org.eclipse.e4.ui.internal.workbench.PartServiceImpl.bringToTop(PartServiceImpl.java:385)
	at org.eclipse.e4.ui.internal.workbench.PartServiceImpl.showPart(PartServiceImpl.java:1126)
	at org.eclipse.ui.internal.WorkbenchPage.busyOpenEditor(WorkbenchPage.java:3182)
	at org.eclipse.ui.internal.WorkbenchPage.access$23(WorkbenchPage.java:3104)
	at org.eclipse.ui.internal.WorkbenchPage$9.run(WorkbenchPage.java:3086)
	at org.eclipse.swt.custom.BusyIndicator.showWhile(BusyIndicator.java:70)
	at org.eclipse.ui.internal.WorkbenchPage.openEditor(WorkbenchPage.java:3081)
	at org.eclipse.ui.internal.WorkbenchPage.openEditor(WorkbenchPage.java:3045)
	at org.eclipse.ui.internal.WorkbenchPage.openEditor(WorkbenchPage.java:3035)
	at org.eclipse.ui.ide.IDE.openEditor(IDE.java:541)
	at org.eclipse.ui.ide.IDE.openEditor(IDE.java:500)
	at org.eclipse.ui.actions.OpenFileAction.openFile(OpenFileAction.java:99)
	at org.eclipse.ui.actions.OpenSystemEditorAction.run(OpenSystemEditorAction.java:99)
	at org.eclipse.ui.actions.RetargetAction.run(RetargetAction.java:229)
	at org.eclipse.ui.navigator.CommonNavigatorManager$3.open(CommonNavigatorManager.java:185)
	at org.eclipse.ui.OpenAndLinkWithEditorHelper$InternalListener.open(OpenAndLinkWithEditorHelper.java:48)
	at org.eclipse.jface.viewers.StructuredViewer$2.run(StructuredViewer.java:853)
	at org.eclipse.core.runtime.SafeRunner.run(SafeRunner.java:42)
	at org.eclipse.ui.internal.JFaceUtil$1.run(JFaceUtil.java:50)
	at org.eclipse.jface.util.SafeRunnable.run(SafeRunnable.java:178)
	at org.eclipse.jface.viewers.StructuredViewer.fireOpen(StructuredViewer.java:850)
	at org.eclipse.jface.viewers.StructuredViewer.handleOpen(StructuredViewer.java:1142)
	at org.eclipse.ui.navigator.CommonViewer.handleOpen(CommonViewer.java:462)
	at org.eclipse.jface.viewers.StructuredViewer$6.handleOpen(StructuredViewer.java:1249)
	at org.eclipse.jface.util.OpenStrategy.fireOpenEvent(OpenStrategy.java:278)
	at org.eclipse.jface.util.OpenStrategy.access$2(OpenStrategy.java:272)
	at org.eclipse.jface.util.OpenStrategy$1.handleEvent(OpenStrategy.java:313)
	at org.eclipse.swt.widgets.EventTable.sendEvent(EventTable.java:84)
	at org.eclipse.swt.widgets.Display.sendEvent(Display.java:4353)
	at org.eclipse.swt.widgets.Widget.sendEvent(Widget.java:1061)
	at org.eclipse.swt.widgets.Display.runDeferredEvents(Display.java:4172)
	at org.eclipse.swt.widgets.Display.readAndDispatch(Display.java:3761)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine$9.run(PartRenderingEngine.java:1151)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:332)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.run(PartRenderingEngine.java:1032)
	at org.eclipse.e4.ui.internal.workbench.E4Workbench.createAndRunUI(E4Workbench.java:148)
	at org.eclipse.ui.internal.Workbench$5.run(Workbench.java:636)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:332)
	at org.eclipse.ui.internal.Workbench.createAndRunWorkbench(Workbench.java:579)
	at org.eclipse.ui.PlatformUI.createAndRunWorkbench(PlatformUI.java:150)
	at org.eclipse.ui.internal.ide.application.IDEApplication.start(IDEApplication.java:135)
	at org.eclipse.equinox.internal.app.EclipseAppHandle.run(EclipseAppHandle.java:196)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.runApplication(EclipseAppLauncher.java:134)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.start(EclipseAppLauncher.java:104)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:382)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:236)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.eclipse.equinox.launcher.Main.invokeFramework(Main.java:648)
	at org.eclipse.equinox.launcher.Main.basicRun(Main.java:603)
	at org.eclipse.equinox.launcher.Main.run(Main.java:1465)

!ENTRY org.eclipse.core.jobs 4 2 2025-06-13 10:44:38.379
!MESSAGE An internal error occurred during: "Updating problem markers".
!STACK 0
java.lang.NullPointerException
	at org.omnetpp.common.markers.ProblemMarkerSynchronizer.addRemoveMarkers(ProblemMarkerSynchronizer.java:199)
	at org.omnetpp.common.markers.ProblemMarkerSynchronizer$1.runInWorkspace(ProblemMarkerSynchronizer.java:177)
	at org.eclipse.core.internal.resources.InternalWorkspaceJob.run(InternalWorkspaceJob.java:38)
	at org.eclipse.core.internal.jobs.Worker.run(Worker.java:54)

!ENTRY org.eclipse.e4.ui.workbench 4 2 2025-06-13 10:44:38.404
!MESSAGE Problems occurred when invoking code from plug-in: "org.eclipse.e4.ui.workbench".
!STACK 0
java.lang.NullPointerException
	at org.omnetpp.scave.model.presentation.ScaveModelActionBarContributor.setActiveEditor(ScaveModelActionBarContributor.java:253)
	at org.eclipse.ui.internal.EditorActionBars.partChanged(EditorActionBars.java:354)
	at org.eclipse.ui.internal.WorkbenchPage.updateActivations(WorkbenchPage.java:298)
	at org.eclipse.ui.internal.WorkbenchPage.access$17(WorkbenchPage.java:280)
	at org.eclipse.ui.internal.WorkbenchPage$E4PartListener.partActivated(WorkbenchPage.java:202)
	at org.eclipse.e4.ui.internal.workbench.PartServiceImpl$3.run(PartServiceImpl.java:234)
	at org.eclipse.core.runtime.SafeRunner.run(SafeRunner.java:42)
	at org.eclipse.e4.ui.internal.workbench.PartServiceImpl.firePartActivated(PartServiceImpl.java:231)
	at org.eclipse.e4.ui.internal.workbench.PartServiceImpl.activate(PartServiceImpl.java:682)
	at org.eclipse.e4.ui.internal.workbench.PartServiceImpl.activate(PartServiceImpl.java:613)
	at org.eclipse.e4.ui.internal.workbench.PartServiceImpl.activate(PartServiceImpl.java:601)
	at org.eclipse.ui.internal.WorkbenchPage.busyOpenEditor(WorkbenchPage.java:3190)
	at org.eclipse.ui.internal.WorkbenchPage.access$23(WorkbenchPage.java:3104)
	at org.eclipse.ui.internal.WorkbenchPage$9.run(WorkbenchPage.java:3086)
	at org.eclipse.swt.custom.BusyIndicator.showWhile(BusyIndicator.java:70)
	at org.eclipse.ui.internal.WorkbenchPage.openEditor(WorkbenchPage.java:3081)
	at org.eclipse.ui.internal.WorkbenchPage.openEditor(WorkbenchPage.java:3045)
	at org.eclipse.ui.internal.WorkbenchPage.openEditor(WorkbenchPage.java:3035)
	at org.eclipse.ui.ide.IDE.openEditor(IDE.java:541)
	at org.eclipse.ui.ide.IDE.openEditor(IDE.java:500)
	at org.eclipse.ui.actions.OpenFileAction.openFile(OpenFileAction.java:99)
	at org.eclipse.ui.actions.OpenSystemEditorAction.run(OpenSystemEditorAction.java:99)
	at org.eclipse.ui.actions.RetargetAction.run(RetargetAction.java:229)
	at org.eclipse.ui.navigator.CommonNavigatorManager$3.open(CommonNavigatorManager.java:185)
	at org.eclipse.ui.OpenAndLinkWithEditorHelper$InternalListener.open(OpenAndLinkWithEditorHelper.java:48)
	at org.eclipse.jface.viewers.StructuredViewer$2.run(StructuredViewer.java:853)
	at org.eclipse.core.runtime.SafeRunner.run(SafeRunner.java:42)
	at org.eclipse.ui.internal.JFaceUtil$1.run(JFaceUtil.java:50)
	at org.eclipse.jface.util.SafeRunnable.run(SafeRunnable.java:178)
	at org.eclipse.jface.viewers.StructuredViewer.fireOpen(StructuredViewer.java:850)
	at org.eclipse.jface.viewers.StructuredViewer.handleOpen(StructuredViewer.java:1142)
	at org.eclipse.ui.navigator.CommonViewer.handleOpen(CommonViewer.java:462)
	at org.eclipse.jface.viewers.StructuredViewer$6.handleOpen(StructuredViewer.java:1249)
	at org.eclipse.jface.util.OpenStrategy.fireOpenEvent(OpenStrategy.java:278)
	at org.eclipse.jface.util.OpenStrategy.access$2(OpenStrategy.java:272)
	at org.eclipse.jface.util.OpenStrategy$1.handleEvent(OpenStrategy.java:313)
	at org.eclipse.swt.widgets.EventTable.sendEvent(EventTable.java:84)
	at org.eclipse.swt.widgets.Display.sendEvent(Display.java:4353)
	at org.eclipse.swt.widgets.Widget.sendEvent(Widget.java:1061)
	at org.eclipse.swt.widgets.Display.runDeferredEvents(Display.java:4172)
	at org.eclipse.swt.widgets.Display.readAndDispatch(Display.java:3761)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine$9.run(PartRenderingEngine.java:1151)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:332)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.run(PartRenderingEngine.java:1032)
	at org.eclipse.e4.ui.internal.workbench.E4Workbench.createAndRunUI(E4Workbench.java:148)
	at org.eclipse.ui.internal.Workbench$5.run(Workbench.java:636)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:332)
	at org.eclipse.ui.internal.Workbench.createAndRunWorkbench(Workbench.java:579)
	at org.eclipse.ui.PlatformUI.createAndRunWorkbench(PlatformUI.java:150)
	at org.eclipse.ui.internal.ide.application.IDEApplication.start(IDEApplication.java:135)
	at org.eclipse.equinox.internal.app.EclipseAppHandle.run(EclipseAppHandle.java:196)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.runApplication(EclipseAppLauncher.java:134)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.start(EclipseAppLauncher.java:104)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:382)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:236)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.eclipse.equinox.launcher.Main.invokeFramework(Main.java:648)
	at org.eclipse.equinox.launcher.Main.basicRun(Main.java:603)
	at org.eclipse.equinox.launcher.Main.run(Main.java:1465)

!ENTRY org.eclipse.e4.ui.workbench 4 0 2025-06-13 10:44:38.404
!MESSAGE An exception occurred while notifying part listeners
!STACK 0
java.lang.NullPointerException
	at org.omnetpp.scave.model.presentation.ScaveModelActionBarContributor.setActiveEditor(ScaveModelActionBarContributor.java:253)
	at org.eclipse.ui.internal.EditorActionBars.partChanged(EditorActionBars.java:354)
	at org.eclipse.ui.internal.WorkbenchPage.updateActivations(WorkbenchPage.java:298)
	at org.eclipse.ui.internal.WorkbenchPage.access$17(WorkbenchPage.java:280)
	at org.eclipse.ui.internal.WorkbenchPage$E4PartListener.partActivated(WorkbenchPage.java:202)
	at org.eclipse.e4.ui.internal.workbench.PartServiceImpl$3.run(PartServiceImpl.java:234)
	at org.eclipse.core.runtime.SafeRunner.run(SafeRunner.java:42)
	at org.eclipse.e4.ui.internal.workbench.PartServiceImpl.firePartActivated(PartServiceImpl.java:231)
	at org.eclipse.e4.ui.internal.workbench.PartServiceImpl.activate(PartServiceImpl.java:682)
	at org.eclipse.e4.ui.internal.workbench.PartServiceImpl.activate(PartServiceImpl.java:613)
	at org.eclipse.e4.ui.internal.workbench.PartServiceImpl.activate(PartServiceImpl.java:601)
	at org.eclipse.ui.internal.WorkbenchPage.busyOpenEditor(WorkbenchPage.java:3190)
	at org.eclipse.ui.internal.WorkbenchPage.access$23(WorkbenchPage.java:3104)
	at org.eclipse.ui.internal.WorkbenchPage$9.run(WorkbenchPage.java:3086)
	at org.eclipse.swt.custom.BusyIndicator.showWhile(BusyIndicator.java:70)
	at org.eclipse.ui.internal.WorkbenchPage.openEditor(WorkbenchPage.java:3081)
	at org.eclipse.ui.internal.WorkbenchPage.openEditor(WorkbenchPage.java:3045)
	at org.eclipse.ui.internal.WorkbenchPage.openEditor(WorkbenchPage.java:3035)
	at org.eclipse.ui.ide.IDE.openEditor(IDE.java:541)
	at org.eclipse.ui.ide.IDE.openEditor(IDE.java:500)
	at org.eclipse.ui.actions.OpenFileAction.openFile(OpenFileAction.java:99)
	at org.eclipse.ui.actions.OpenSystemEditorAction.run(OpenSystemEditorAction.java:99)
	at org.eclipse.ui.actions.RetargetAction.run(RetargetAction.java:229)
	at org.eclipse.ui.navigator.CommonNavigatorManager$3.open(CommonNavigatorManager.java:185)
	at org.eclipse.ui.OpenAndLinkWithEditorHelper$InternalListener.open(OpenAndLinkWithEditorHelper.java:48)
	at org.eclipse.jface.viewers.StructuredViewer$2.run(StructuredViewer.java:853)
	at org.eclipse.core.runtime.SafeRunner.run(SafeRunner.java:42)
	at org.eclipse.ui.internal.JFaceUtil$1.run(JFaceUtil.java:50)
	at org.eclipse.jface.util.SafeRunnable.run(SafeRunnable.java:178)
	at org.eclipse.jface.viewers.StructuredViewer.fireOpen(StructuredViewer.java:850)
	at org.eclipse.jface.viewers.StructuredViewer.handleOpen(StructuredViewer.java:1142)
	at org.eclipse.ui.navigator.CommonViewer.handleOpen(CommonViewer.java:462)
	at org.eclipse.jface.viewers.StructuredViewer$6.handleOpen(StructuredViewer.java:1249)
	at org.eclipse.jface.util.OpenStrategy.fireOpenEvent(OpenStrategy.java:278)
	at org.eclipse.jface.util.OpenStrategy.access$2(OpenStrategy.java:272)
	at org.eclipse.jface.util.OpenStrategy$1.handleEvent(OpenStrategy.java:313)
	at org.eclipse.swt.widgets.EventTable.sendEvent(EventTable.java:84)
	at org.eclipse.swt.widgets.Display.sendEvent(Display.java:4353)
	at org.eclipse.swt.widgets.Widget.sendEvent(Widget.java:1061)
	at org.eclipse.swt.widgets.Display.runDeferredEvents(Display.java:4172)
	at org.eclipse.swt.widgets.Display.readAndDispatch(Display.java:3761)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine$9.run(PartRenderingEngine.java:1151)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:332)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.run(PartRenderingEngine.java:1032)
	at org.eclipse.e4.ui.internal.workbench.E4Workbench.createAndRunUI(E4Workbench.java:148)
	at org.eclipse.ui.internal.Workbench$5.run(Workbench.java:636)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:332)
	at org.eclipse.ui.internal.Workbench.createAndRunWorkbench(Workbench.java:579)
	at org.eclipse.ui.PlatformUI.createAndRunWorkbench(PlatformUI.java:150)
	at org.eclipse.ui.internal.ide.application.IDEApplication.start(IDEApplication.java:135)
	at org.eclipse.equinox.internal.app.EclipseAppHandle.run(EclipseAppHandle.java:196)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.runApplication(EclipseAppLauncher.java:134)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.start(EclipseAppLauncher.java:104)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:382)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:236)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.eclipse.equinox.launcher.Main.invokeFramework(Main.java:648)
	at org.eclipse.equinox.launcher.Main.basicRun(Main.java:603)
	at org.eclipse.equinox.launcher.Main.run(Main.java:1465)

!ENTRY org.eclipse.e4.ui.workbench 4 2 2025-06-13 10:44:45.356
!MESSAGE Problems occurred when invoking code from plug-in: "org.eclipse.e4.ui.workbench".
!STACK 0
java.lang.NullPointerException
	at org.omnetpp.scave.model.presentation.ScaveModelActionBarContributor.setActiveEditor(ScaveModelActionBarContributor.java:253)
	at org.eclipse.ui.internal.EditorActionBars.partChanged(EditorActionBars.java:354)
	at org.eclipse.ui.internal.WorkbenchPage.updateActivations(WorkbenchPage.java:298)
	at org.eclipse.ui.internal.WorkbenchPage.access$17(WorkbenchPage.java:280)
	at org.eclipse.ui.internal.WorkbenchPage$E4PartListener.partActivated(WorkbenchPage.java:202)
	at org.eclipse.e4.ui.internal.workbench.PartServiceImpl$3.run(PartServiceImpl.java:234)
	at org.eclipse.core.runtime.SafeRunner.run(SafeRunner.java:42)
	at org.eclipse.e4.ui.internal.workbench.PartServiceImpl.firePartActivated(PartServiceImpl.java:231)
	at org.eclipse.e4.ui.internal.workbench.PartServiceImpl.activate(PartServiceImpl.java:682)
	at org.eclipse.e4.ui.internal.workbench.PartServiceImpl.activate(PartServiceImpl.java:613)
	at org.eclipse.e4.ui.internal.workbench.swt.AbstractPartRenderer.activate(AbstractPartRenderer.java:106)
	at org.eclipse.e4.ui.workbench.renderers.swt.ContributedPartRenderer$1.handleEvent(ContributedPartRenderer.java:61)
	at org.eclipse.swt.widgets.EventTable.sendEvent(EventTable.java:84)
	at org.eclipse.swt.widgets.Display.sendEvent(Display.java:4353)
	at org.eclipse.swt.widgets.Widget.sendEvent(Widget.java:1061)
	at org.eclipse.swt.widgets.Widget.sendEvent(Widget.java:1085)
	at org.eclipse.swt.widgets.Widget.sendEvent(Widget.java:1070)
	at org.eclipse.swt.widgets.Shell.setActiveControl(Shell.java:1453)
	at org.eclipse.swt.widgets.Shell.WM_MOUSEACTIVATE(Shell.java:2334)
	at org.eclipse.swt.widgets.Control.windowProc(Control.java:4654)
	at org.eclipse.swt.widgets.Canvas.windowProc(Canvas.java:339)
	at org.eclipse.swt.widgets.Decorations.windowProc(Decorations.java:1626)
	at org.eclipse.swt.widgets.Shell.windowProc(Shell.java:2075)
	at org.eclipse.swt.widgets.Display.windowProc(Display.java:5036)
	at org.eclipse.swt.internal.win32.OS.DefWindowProcW(Native Method)
	at org.eclipse.swt.internal.win32.OS.DefWindowProc(OS.java:2544)
	at org.eclipse.swt.widgets.Scrollable.callWindowProc(Scrollable.java:79)
	at org.eclipse.swt.widgets.Control.windowProc(Control.java:4705)
	at org.eclipse.swt.widgets.Display.windowProc(Display.java:5036)
	at org.eclipse.swt.internal.win32.OS.DefWindowProcW(Native Method)
	at org.eclipse.swt.internal.win32.OS.DefWindowProc(OS.java:2544)
	at org.eclipse.swt.widgets.Scrollable.callWindowProc(Scrollable.java:79)
	at org.eclipse.swt.widgets.Control.windowProc(Control.java:4705)
	at org.eclipse.swt.widgets.Display.windowProc(Display.java:5036)
	at org.eclipse.swt.internal.win32.OS.DefWindowProcW(Native Method)
	at org.eclipse.swt.internal.win32.OS.DefWindowProc(OS.java:2544)
	at org.eclipse.swt.widgets.Scrollable.callWindowProc(Scrollable.java:79)
	at org.eclipse.swt.widgets.Control.windowProc(Control.java:4705)
	at org.eclipse.swt.widgets.Display.windowProc(Display.java:5036)
	at org.eclipse.swt.internal.win32.OS.DefWindowProcW(Native Method)
	at org.eclipse.swt.internal.win32.OS.DefWindowProc(OS.java:2544)
	at org.eclipse.swt.widgets.Scrollable.callWindowProc(Scrollable.java:79)
	at org.eclipse.swt.widgets.Control.windowProc(Control.java:4705)
	at org.eclipse.swt.widgets.Display.windowProc(Display.java:5036)
	at org.eclipse.swt.internal.win32.OS.DefWindowProcW(Native Method)
	at org.eclipse.swt.internal.win32.OS.DefWindowProc(OS.java:2544)
	at org.eclipse.swt.widgets.Scrollable.callWindowProc(Scrollable.java:79)
	at org.eclipse.swt.widgets.Control.windowProc(Control.java:4705)
	at org.eclipse.swt.widgets.Display.windowProc(Display.java:5036)
	at org.eclipse.swt.internal.win32.OS.DefWindowProcW(Native Method)
	at org.eclipse.swt.internal.win32.OS.DefWindowProc(OS.java:2544)
	at org.eclipse.swt.widgets.Scrollable.callWindowProc(Scrollable.java:79)
	at org.eclipse.swt.widgets.Control.windowProc(Control.java:4705)
	at org.eclipse.swt.widgets.Display.windowProc(Display.java:5036)
	at org.eclipse.swt.internal.win32.OS.DefWindowProcW(Native Method)
	at org.eclipse.swt.internal.win32.OS.DefWindowProc(OS.java:2544)
	at org.eclipse.swt.widgets.Scrollable.callWindowProc(Scrollable.java:79)
	at org.eclipse.swt.widgets.Control.windowProc(Control.java:4705)
	at org.eclipse.swt.widgets.Display.windowProc(Display.java:5036)
	at org.eclipse.swt.internal.win32.OS.DefWindowProcW(Native Method)
	at org.eclipse.swt.internal.win32.OS.DefWindowProc(OS.java:2544)
	at org.eclipse.swt.widgets.Scrollable.callWindowProc(Scrollable.java:79)
	at org.eclipse.swt.widgets.Control.windowProc(Control.java:4705)
	at org.eclipse.swt.widgets.Display.windowProc(Display.java:5036)
	at org.eclipse.swt.internal.win32.OS.DefWindowProcW(Native Method)
	at org.eclipse.swt.internal.win32.OS.DefWindowProc(OS.java:2544)
	at org.eclipse.swt.widgets.Scrollable.callWindowProc(Scrollable.java:79)
	at org.eclipse.swt.widgets.Control.windowProc(Control.java:4705)
	at org.eclipse.swt.widgets.Display.windowProc(Display.java:5036)
	at org.eclipse.swt.internal.win32.OS.DefWindowProcW(Native Method)
	at org.eclipse.swt.internal.win32.OS.DefWindowProc(OS.java:2544)
	at org.eclipse.swt.widgets.Scrollable.callWindowProc(Scrollable.java:79)
	at org.eclipse.swt.widgets.Control.windowProc(Control.java:4705)
	at org.eclipse.swt.widgets.Display.windowProc(Display.java:5036)
	at org.eclipse.swt.internal.win32.OS.DefWindowProcW(Native Method)
	at org.eclipse.swt.internal.win32.OS.DefWindowProc(OS.java:2544)
	at org.eclipse.swt.widgets.Scrollable.callWindowProc(Scrollable.java:79)
	at org.eclipse.swt.widgets.Control.windowProc(Control.java:4705)
	at org.eclipse.swt.widgets.Display.windowProc(Display.java:5036)
	at org.eclipse.swt.internal.win32.OS.CallWindowProcW(Native Method)
	at org.eclipse.swt.internal.win32.OS.CallWindowProc(OS.java:2443)
	at org.eclipse.swt.widgets.Button.callWindowProc(Button.java:342)
	at org.eclipse.swt.widgets.Control.windowProc(Control.java:4705)
	at org.eclipse.swt.widgets.Display.windowProc(Display.java:5036)
	at org.eclipse.swt.internal.win32.OS.PeekMessageW(Native Method)
	at org.eclipse.swt.internal.win32.OS.PeekMessage(OS.java:3141)
	at org.eclipse.swt.widgets.Display.readAndDispatch(Display.java:3756)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine$9.run(PartRenderingEngine.java:1151)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:332)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.run(PartRenderingEngine.java:1032)
	at org.eclipse.e4.ui.internal.workbench.E4Workbench.createAndRunUI(E4Workbench.java:148)
	at org.eclipse.ui.internal.Workbench$5.run(Workbench.java:636)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:332)
	at org.eclipse.ui.internal.Workbench.createAndRunWorkbench(Workbench.java:579)
	at org.eclipse.ui.PlatformUI.createAndRunWorkbench(PlatformUI.java:150)
	at org.eclipse.ui.internal.ide.application.IDEApplication.start(IDEApplication.java:135)
	at org.eclipse.equinox.internal.app.EclipseAppHandle.run(EclipseAppHandle.java:196)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.runApplication(EclipseAppLauncher.java:134)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.start(EclipseAppLauncher.java:104)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:382)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:236)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.eclipse.equinox.launcher.Main.invokeFramework(Main.java:648)
	at org.eclipse.equinox.launcher.Main.basicRun(Main.java:603)
	at org.eclipse.equinox.launcher.Main.run(Main.java:1465)

!ENTRY org.eclipse.e4.ui.workbench 4 0 2025-06-13 10:44:45.357
!MESSAGE An exception occurred while notifying part listeners
!STACK 0
java.lang.NullPointerException
	at org.omnetpp.scave.model.presentation.ScaveModelActionBarContributor.setActiveEditor(ScaveModelActionBarContributor.java:253)
	at org.eclipse.ui.internal.EditorActionBars.partChanged(EditorActionBars.java:354)
	at org.eclipse.ui.internal.WorkbenchPage.updateActivations(WorkbenchPage.java:298)
	at org.eclipse.ui.internal.WorkbenchPage.access$17(WorkbenchPage.java:280)
	at org.eclipse.ui.internal.WorkbenchPage$E4PartListener.partActivated(WorkbenchPage.java:202)
	at org.eclipse.e4.ui.internal.workbench.PartServiceImpl$3.run(PartServiceImpl.java:234)
	at org.eclipse.core.runtime.SafeRunner.run(SafeRunner.java:42)
	at org.eclipse.e4.ui.internal.workbench.PartServiceImpl.firePartActivated(PartServiceImpl.java:231)
	at org.eclipse.e4.ui.internal.workbench.PartServiceImpl.activate(PartServiceImpl.java:682)
	at org.eclipse.e4.ui.internal.workbench.PartServiceImpl.activate(PartServiceImpl.java:613)
	at org.eclipse.e4.ui.internal.workbench.swt.AbstractPartRenderer.activate(AbstractPartRenderer.java:106)
	at org.eclipse.e4.ui.workbench.renderers.swt.ContributedPartRenderer$1.handleEvent(ContributedPartRenderer.java:61)
	at org.eclipse.swt.widgets.EventTable.sendEvent(EventTable.java:84)
	at org.eclipse.swt.widgets.Display.sendEvent(Display.java:4353)
	at org.eclipse.swt.widgets.Widget.sendEvent(Widget.java:1061)
	at org.eclipse.swt.widgets.Widget.sendEvent(Widget.java:1085)
	at org.eclipse.swt.widgets.Widget.sendEvent(Widget.java:1070)
	at org.eclipse.swt.widgets.Shell.setActiveControl(Shell.java:1453)
	at org.eclipse.swt.widgets.Shell.WM_MOUSEACTIVATE(Shell.java:2334)
	at org.eclipse.swt.widgets.Control.windowProc(Control.java:4654)
	at org.eclipse.swt.widgets.Canvas.windowProc(Canvas.java:339)
	at org.eclipse.swt.widgets.Decorations.windowProc(Decorations.java:1626)
	at org.eclipse.swt.widgets.Shell.windowProc(Shell.java:2075)
	at org.eclipse.swt.widgets.Display.windowProc(Display.java:5036)
	at org.eclipse.swt.internal.win32.OS.DefWindowProcW(Native Method)
	at org.eclipse.swt.internal.win32.OS.DefWindowProc(OS.java:2544)
	at org.eclipse.swt.widgets.Scrollable.callWindowProc(Scrollable.java:79)
	at org.eclipse.swt.widgets.Control.windowProc(Control.java:4705)
	at org.eclipse.swt.widgets.Display.windowProc(Display.java:5036)
	at org.eclipse.swt.internal.win32.OS.DefWindowProcW(Native Method)
	at org.eclipse.swt.internal.win32.OS.DefWindowProc(OS.java:2544)
	at org.eclipse.swt.widgets.Scrollable.callWindowProc(Scrollable.java:79)
	at org.eclipse.swt.widgets.Control.windowProc(Control.java:4705)
	at org.eclipse.swt.widgets.Display.windowProc(Display.java:5036)
	at org.eclipse.swt.internal.win32.OS.DefWindowProcW(Native Method)
	at org.eclipse.swt.internal.win32.OS.DefWindowProc(OS.java:2544)
	at org.eclipse.swt.widgets.Scrollable.callWindowProc(Scrollable.java:79)
	at org.eclipse.swt.widgets.Control.windowProc(Control.java:4705)
	at org.eclipse.swt.widgets.Display.windowProc(Display.java:5036)
	at org.eclipse.swt.internal.win32.OS.DefWindowProcW(Native Method)
	at org.eclipse.swt.internal.win32.OS.DefWindowProc(OS.java:2544)
	at org.eclipse.swt.widgets.Scrollable.callWindowProc(Scrollable.java:79)
	at org.eclipse.swt.widgets.Control.windowProc(Control.java:4705)
	at org.eclipse.swt.widgets.Display.windowProc(Display.java:5036)
	at org.eclipse.swt.internal.win32.OS.DefWindowProcW(Native Method)
	at org.eclipse.swt.internal.win32.OS.DefWindowProc(OS.java:2544)
	at org.eclipse.swt.widgets.Scrollable.callWindowProc(Scrollable.java:79)
	at org.eclipse.swt.widgets.Control.windowProc(Control.java:4705)
	at org.eclipse.swt.widgets.Display.windowProc(Display.java:5036)
	at org.eclipse.swt.internal.win32.OS.DefWindowProcW(Native Method)
	at org.eclipse.swt.internal.win32.OS.DefWindowProc(OS.java:2544)
	at org.eclipse.swt.widgets.Scrollable.callWindowProc(Scrollable.java:79)
	at org.eclipse.swt.widgets.Control.windowProc(Control.java:4705)
	at org.eclipse.swt.widgets.Display.windowProc(Display.java:5036)
	at org.eclipse.swt.internal.win32.OS.DefWindowProcW(Native Method)
	at org.eclipse.swt.internal.win32.OS.DefWindowProc(OS.java:2544)
	at org.eclipse.swt.widgets.Scrollable.callWindowProc(Scrollable.java:79)
	at org.eclipse.swt.widgets.Control.windowProc(Control.java:4705)
	at org.eclipse.swt.widgets.Display.windowProc(Display.java:5036)
	at org.eclipse.swt.internal.win32.OS.DefWindowProcW(Native Method)
	at org.eclipse.swt.internal.win32.OS.DefWindowProc(OS.java:2544)
	at org.eclipse.swt.widgets.Scrollable.callWindowProc(Scrollable.java:79)
	at org.eclipse.swt.widgets.Control.windowProc(Control.java:4705)
	at org.eclipse.swt.widgets.Display.windowProc(Display.java:5036)
	at org.eclipse.swt.internal.win32.OS.DefWindowProcW(Native Method)
	at org.eclipse.swt.internal.win32.OS.DefWindowProc(OS.java:2544)
	at org.eclipse.swt.widgets.Scrollable.callWindowProc(Scrollable.java:79)
	at org.eclipse.swt.widgets.Control.windowProc(Control.java:4705)
	at org.eclipse.swt.widgets.Display.windowProc(Display.java:5036)
	at org.eclipse.swt.internal.win32.OS.DefWindowProcW(Native Method)
	at org.eclipse.swt.internal.win32.OS.DefWindowProc(OS.java:2544)
	at org.eclipse.swt.widgets.Scrollable.callWindowProc(Scrollable.java:79)
	at org.eclipse.swt.widgets.Control.windowProc(Control.java:4705)
	at org.eclipse.swt.widgets.Display.windowProc(Display.java:5036)
	at org.eclipse.swt.internal.win32.OS.DefWindowProcW(Native Method)
	at org.eclipse.swt.internal.win32.OS.DefWindowProc(OS.java:2544)
	at org.eclipse.swt.widgets.Scrollable.callWindowProc(Scrollable.java:79)
	at org.eclipse.swt.widgets.Control.windowProc(Control.java:4705)
	at org.eclipse.swt.widgets.Display.windowProc(Display.java:5036)
	at org.eclipse.swt.internal.win32.OS.CallWindowProcW(Native Method)
	at org.eclipse.swt.internal.win32.OS.CallWindowProc(OS.java:2443)
	at org.eclipse.swt.widgets.Button.callWindowProc(Button.java:342)
	at org.eclipse.swt.widgets.Control.windowProc(Control.java:4705)
	at org.eclipse.swt.widgets.Display.windowProc(Display.java:5036)
	at org.eclipse.swt.internal.win32.OS.PeekMessageW(Native Method)
	at org.eclipse.swt.internal.win32.OS.PeekMessage(OS.java:3141)
	at org.eclipse.swt.widgets.Display.readAndDispatch(Display.java:3756)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine$9.run(PartRenderingEngine.java:1151)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:332)
	at org.eclipse.e4.ui.internal.workbench.swt.PartRenderingEngine.run(PartRenderingEngine.java:1032)
	at org.eclipse.e4.ui.internal.workbench.E4Workbench.createAndRunUI(E4Workbench.java:148)
	at org.eclipse.ui.internal.Workbench$5.run(Workbench.java:636)
	at org.eclipse.core.databinding.observable.Realm.runWithDefault(Realm.java:332)
	at org.eclipse.ui.internal.Workbench.createAndRunWorkbench(Workbench.java:579)
	at org.eclipse.ui.PlatformUI.createAndRunWorkbench(PlatformUI.java:150)
	at org.eclipse.ui.internal.ide.application.IDEApplication.start(IDEApplication.java:135)
	at org.eclipse.equinox.internal.app.EclipseAppHandle.run(EclipseAppHandle.java:196)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.runApplication(EclipseAppLauncher.java:134)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.start(EclipseAppLauncher.java:104)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:382)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:236)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.eclipse.equinox.launcher.Main.invokeFramework(Main.java:648)
	at org.eclipse.equinox.launcher.Main.basicRun(Main.java:603)
	at org.eclipse.equinox.launcher.Main.run(Main.java:1465)
