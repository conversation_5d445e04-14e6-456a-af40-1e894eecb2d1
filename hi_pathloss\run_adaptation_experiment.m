% 运行适应性验证实验的启动脚本
% 这是实验的入口点

% 主执行部分
main_adaptation_experiment();

function main_adaptation_experiment()
    % 运行适应性验证实验
    
    fprintf('=== 分层强化学习算法适应性验证实验 ===\n');
    fprintf('开始时间: %s\n', datestr(now));
    fprintf('实验目标: 验证算法在不同运动场景下的适应性\n\n');
    
    % 清理工作空间
    close all;
    clear all;
    clc;
    
    % 添加必要的路径
    addpath(pwd);
    
    % 检查必要文件
    if ~check_required_files()
        fprintf('错误: 缺少必要的实验文件\n');
        return;
    end
    
    % 设置随机种子以确保结果可重现
    rng(42);
    
    try
        % 运行主实验
        fprintf('启动适应性验证实验...\n');

        % 检查主实验文件是否存在
        if exist('adaptation_experiment_main.m', 'file')
            adaptation_experiment_main();
        else
            fprintf('错误: 找不到主实验文件 adaptation_experiment_main.m\n');
            fprintf('请确保所有实验文件都在当前目录中\n');
            return;
        end
        
        fprintf('\n=== 实验成功完成 ===\n');
        fprintf('结束时间: %s\n', datestr(now));
        fprintf('结果文件保存在: adaptation_experiment_results/\n');
        
        % 显示结果摘要
        display_experiment_summary();
        
    catch ME
        fprintf('\n=== 实验执行出错 ===\n');
        fprintf('错误信息: %s\n', ME.message);
        fprintf('错误位置: %s (第%d行)\n', ME.stack(1).file, ME.stack(1).line);
        
        % 尝试生成错误报告
        generate_error_report(ME);
    end
end

function success = check_required_files()
    % 检查必要的实验文件
    
    required_files = {
        'adaptation_experiment_main.m',
        'adaptation_training_module.m',
        'adaptation_evaluation_module.m',
        'adaptation_baseline_module.m',
        'adaptation_analysis_module.m',
        'adaptation_visualization_module.m',
        'adaptation_environment_interface.m'
    };
    
    success = true;
    missing_files = {};
    
    for i = 1:length(required_files)
        if ~exist(required_files{i}, 'file')
            success = false;
            missing_files{end+1} = required_files{i};
        end
    end
    
    if ~success
        fprintf('缺少以下必要文件:\n');
        for i = 1:length(missing_files)
            fprintf('  - %s\n', missing_files{i});
        end
    else
        fprintf('所有必要文件检查通过\n');
    end
end

function display_experiment_summary()
    % 显示实验结果摘要
    
    fprintf('\n=== 实验结果摘要 ===\n');
    
    % 检查结果目录
    results_dir = 'adaptation_experiment_results';
    if ~exist(results_dir, 'dir')
        fprintf('未找到结果目录\n');
        return;
    end
    
    % 列出生成的文件
    fprintf('生成的结果文件:\n');
    
    % 检查场景结果目录
    scenario_dirs = dir(fullfile(results_dir, 'scenario_*'));
    if ~isempty(scenario_dirs)
        fprintf('场景实验结果:\n');
        for i = 1:length(scenario_dirs)
            if scenario_dirs(i).isdir
                fprintf('  - %s/\n', scenario_dirs(i).name);
            end
        end
    end
    
    % 检查图片文件
    image_files = dir(fullfile(results_dir, '*.png'));
    if ~isempty(image_files)
        fprintf('生成的图片文件:\n');
        for i = 1:length(image_files)
            fprintf('  - %s\n', image_files(i).name);
        end
    end
    
    % 检查报告文件
    report_file = fullfile(results_dir, 'adaptation_experiment_report.txt');
    if exist(report_file, 'file')
        fprintf('实验报告: adaptation_experiment_report.txt\n');
        
        % 显示报告摘要
        try
            fid = fopen(report_file, 'r');
            if fid ~= -1
                fprintf('\n报告摘要:\n');
                line_count = 0;
                while ~feof(fid) && line_count < 10
                    line = fgetl(fid);
                    if ischar(line)
                        fprintf('%s\n', line);
                        line_count = line_count + 1;
                    end
                end
                if ~feof(fid)
                    fprintf('...(更多内容请查看完整报告)\n');
                end
                fclose(fid);
            end
        catch
            fprintf('无法读取报告文件\n');
        end
    end
    
    fprintf('\n建议后续步骤:\n');
    fprintf('1. 查看生成的图片文件了解实验结果\n');
    fprintf('2. 阅读完整的实验报告\n');
    fprintf('3. 分析不同场景下的算法性能差异\n');
    fprintf('4. 根据结果优化算法参数或策略\n');
end

function generate_error_report(ME)
    % 生成错误报告
    
    try
        % 创建错误报告目录
        error_dir = 'adaptation_experiment_results';
        if ~exist(error_dir, 'dir')
            mkdir(error_dir);
        end
        
        % 生成错误报告文件
        error_file = fullfile(error_dir, 'error_report.txt');
        fid = fopen(error_file, 'w');
        
        if fid ~= -1
            fprintf(fid, '=== 适应性验证实验错误报告 ===\n');
            fprintf(fid, '时间: %s\n\n', datestr(now));
            
            fprintf(fid, '错误信息: %s\n\n', ME.message);
            
            fprintf(fid, '错误堆栈:\n');
            for i = 1:length(ME.stack)
                fprintf(fid, '  文件: %s\n', ME.stack(i).file);
                fprintf(fid, '  函数: %s\n', ME.stack(i).name);
                fprintf(fid, '  行号: %d\n\n', ME.stack(i).line);
            end
            
            fprintf(fid, '可能的解决方案:\n');
            fprintf(fid, '1. 检查所有必要文件是否存在\n');
            fprintf(fid, '2. 确保MATLAB路径设置正确\n');
            fprintf(fid, '3. 检查BVH文件是否可读\n');
            fprintf(fid, '4. 验证内存是否充足\n');
            fprintf(fid, '5. 重新运行实验\n');
            
            fclose(fid);
            fprintf('错误报告已保存: %s\n', error_file);
        end
        
    catch
        fprintf('无法生成错误报告\n');
    end
end

% 脚本结束
