% 快速演示强化学习WBAN功率控制系统
% 自动运行，无需用户交互
close all;
clear;
clc;

fprintf('=== 强化学习WBAN功率控制快速演示 ===\n');
fprintf('本演示将展示分层强化学习在WBAN功率控制中的应用\n\n');

try
    % 步骤1: 初始化系统
    fprintf('步骤1: 初始化系统...\n');
    env = rl_environment();
    agent = hierarchical_agent();
    fprintf('✓ 系统初始化完成\n\n');
    
    % 步骤2: 快速训练
    fprintf('步骤2: 快速训练 (15轮)...\n');
    num_episodes = 15;
    episode_rewards = zeros(num_episodes, 1);
    episode_energy = zeros(num_episodes, 1);
    episode_pdr = zeros(num_episodes, 1);
    
    for episode = 1:num_episodes
        state = env.reset();
        episode_reward = 0;
        
        for step = 1:40  % 每轮40步
            meta_action = agent.select_meta_action(state);
            action = agent.select_local_action(state, meta_action);
            
            [next_state, reward, done, info] = env.step(action);
            
            agent.store_meta_experience(state, meta_action, reward, next_state, done);
            agent.store_local_experience(state, meta_action, action, reward, next_state, done);
            
            state = next_state;
            episode_reward = episode_reward + reward;
            
            if mod(step, 8) == 0
                agent.train_meta_agent();
                agent.train_local_agent();
            end
            
            if done
                break;
            end
        end
        
        env_info = env.get_environment_info();
        episode_rewards(episode) = episode_reward;
        episode_energy(episode) = env_info.total_energy;
        episode_pdr(episode) = env_info.average_pdr;
        
        agent.decay_epsilon();
        
        if mod(episode, 5) == 0
            fprintf('  Episode %d: 奖励=%.2f, 能耗=%.1f mJ, PDR=%.3f\n', ...
                   episode, episode_reward, env_info.total_energy, env_info.average_pdr);
        end
    end
    fprintf('✓ 训练完成\n\n');
    
    % 步骤3: 性能评估
    fprintf('步骤3: 性能评估...\n');
    
    % 测试训练后的RL算法
    rl_performance = evaluate_rl_performance(env, agent);
    
    % 测试基准算法
    fixed_performance = evaluate_fixed_power(env);
    emg_performance = evaluate_emg_tpc(env);
    hr_performance = evaluate_hr_tpc(env);
    
    fprintf('✓ 性能评估完成\n\n');
    
    % 步骤4: 结果分析
    fprintf('步骤4: 结果分析\n');
    fprintf('=== 性能对比结果 ===\n');
    
    algorithms = {'固定功率', 'EMG-TPC', 'HR-TPC', '分层RL'};
    energy_values = [fixed_performance.energy, emg_performance.energy, ...
                    hr_performance.energy, rl_performance.energy];
    pdr_values = [fixed_performance.pdr, emg_performance.pdr, ...
                 hr_performance.pdr, rl_performance.pdr];
    delay_values = [fixed_performance.delay, emg_performance.delay, ...
                   hr_performance.delay, rl_performance.delay];
    
    for i = 1:length(algorithms)
        fprintf('%s:\n', algorithms{i});
        fprintf('  能耗: %.1f mJ\n', energy_values(i));
        fprintf('  PDR: %.3f\n', pdr_values(i));
        fprintf('  延迟: %.1f ms\n\n', delay_values(i));
    end
    
    % 计算改进效果
    energy_improvement = (fixed_performance.energy - rl_performance.energy) / fixed_performance.energy * 100;
    pdr_improvement = (rl_performance.pdr - fixed_performance.pdr) / fixed_performance.pdr * 100;
    delay_improvement = (fixed_performance.delay - rl_performance.delay) / fixed_performance.delay * 100;
    
    fprintf('=== 分层RL相对固定功率的改进 ===\n');
    fprintf('能耗降低: %.1f%%\n', energy_improvement);
    fprintf('PDR提升: %.1f%%\n', pdr_improvement);
    fprintf('延迟降低: %.1f%%\n\n', delay_improvement);
    
    % 步骤5: 可视化结果
    fprintf('步骤5: 生成可视化结果...\n');
    create_demo_plots(episode_rewards, episode_energy, episode_pdr, ...
                     algorithms, energy_values, pdr_values, delay_values);
    fprintf('✓ 可视化完成\n\n');
    
    % 保存演示结果
    demo_results = struct();
    demo_results.training_rewards = episode_rewards;
    demo_results.training_energy = episode_energy;
    demo_results.training_pdr = episode_pdr;
    demo_results.algorithms = algorithms;
    demo_results.energy_comparison = energy_values;
    demo_results.pdr_comparison = pdr_values;
    demo_results.delay_comparison = delay_values;
    demo_results.improvements = struct('energy', energy_improvement, ...
                                      'pdr', pdr_improvement, 'delay', delay_improvement);
    
    save('quick_demo_results.mat', 'demo_results');
    
    fprintf('=== 演示完成！ ===\n');
    fprintf('结果已保存到 quick_demo_results.mat\n');
    fprintf('分层强化学习算法在WBAN功率控制中表现出色！\n');
    
catch ME
    fprintf('❌ 演示过程中出现错误: %s\n', ME.message);
    if ~isempty(ME.stack)
        fprintf('错误位置: %s (第%d行)\n', ME.stack(1).name, ME.stack(1).line);
    end
end

function performance = evaluate_rl_performance(env, agent)
    % 评估强化学习算法性能
    agent.meta_epsilon = 0;
    agent.local_epsilon = 0;
    
    state = env.reset();
    total_reward = 0;
    
    for step = 1:60
        meta_action = agent.select_meta_action(state);
        action = agent.select_local_action(state, meta_action);
        
        [next_state, reward, done, info] = env.step(action);
        total_reward = total_reward + reward;
        state = next_state;
        
        if done
            break;
        end
    end
    
    env_info = env.get_environment_info();
    performance = struct('reward', total_reward, 'energy', env_info.total_energy, ...
                        'pdr', env_info.average_pdr, 'delay', env_info.average_delay);
end

function performance = evaluate_fixed_power(env)
    % 评估固定功率算法
    state = env.reset();
    total_reward = 0;
    
    for step = 1:60
        action = 4; % 固定中等功率
        [next_state, reward, done, info] = env.step(action);
        total_reward = total_reward + reward;
        state = next_state;
        
        if done
            break;
        end
    end
    
    env_info = env.get_environment_info();
    performance = struct('reward', total_reward, 'energy', env_info.total_energy, ...
                        'pdr', env_info.average_pdr, 'delay', env_info.average_delay);
end

function performance = evaluate_emg_tpc(env)
    % 评估路径损耗TPC算法 (替代EMG-TPC)
    state = env.reset();
    total_reward = 0;

    for step = 1:60
        % 使用路径损耗数据替代EMG信号
        if step <= length(env.pathloss_data)
            pl_signal = env.pathloss_data(step);
        else
            pl_signal = env.pathloss_data(end);
        end

        if pl_signal > -70
            action = 6; % 高功率
        elseif pl_signal > -80
            action = 4; % 中功率
        else
            action = 2; % 低功率
        end

        [next_state, reward, done, info] = env.step(action);
        total_reward = total_reward + reward;
        state = next_state;

        if done
            break;
        end
    end

    env_info = env.get_environment_info();
    performance = struct('reward', total_reward, 'energy', env_info.total_energy, ...
                        'pdr', env_info.average_pdr, 'delay', env_info.average_delay);
end

function performance = evaluate_hr_tpc(env)
    % 评估RSSI-TPC算法 (替代HR-TPC)
    state = env.reset();
    total_reward = 0;

    for step = 1:60
        % 使用RSSI数据替代心率信号
        if step <= length(env.rssi_data)
            rssi_signal = env.rssi_data(step);
        else
            rssi_signal = env.rssi_data(end);
        end

        if rssi_signal < -85
            action = 6; % 高功率
        elseif rssi_signal < -75
            action = 4; % 中功率
        else
            action = 3; % 低功率
        end

        [next_state, reward, done, info] = env.step(action);
        total_reward = total_reward + reward;
        state = next_state;

        if done
            break;
        end
    end

    env_info = env.get_environment_info();
    performance = struct('reward', total_reward, 'energy', env_info.total_energy, ...
                        'pdr', env_info.average_pdr, 'delay', env_info.average_delay);
end

function create_demo_plots(episode_rewards, episode_energy, episode_pdr, ...
                          algorithms, energy_values, pdr_values, delay_values)
    % 创建演示图表
    
    % 图1: 训练过程
    figure('Position', [100, 100, 1200, 800]);
    
    subplot(2,3,1);
    plot(1:length(episode_rewards), episode_rewards, 'b-o', 'LineWidth', 2, 'MarkerSize', 4);
    title('(a) 训练奖励收敛');
    xlabel('训练轮次');
    ylabel('累积奖励');
    grid on;
    
    subplot(2,3,2);
    plot(1:length(episode_energy), episode_energy, 'r-s', 'LineWidth', 2, 'MarkerSize', 4);
    title('(b) 训练能耗变化');
    xlabel('训练轮次');
    ylabel('能耗 (mJ)');
    grid on;
    
    subplot(2,3,3);
    plot(1:length(episode_pdr), episode_pdr, 'g-^', 'LineWidth', 2, 'MarkerSize', 4);
    title('(c) 训练PDR变化');
    xlabel('训练轮次');
    ylabel('包递交率');
    ylim([0, 1]);
    grid on;
    
    % 性能对比
    subplot(2,3,4);
    bar(energy_values, 'FaceColor', [0.2, 0.6, 0.8], 'EdgeColor', 'black');
    set(gca, 'XTickLabel', algorithms, 'XTickLabelRotation', 45);
    title('(d) 能耗对比');
    ylabel('能耗 (mJ)');
    grid on;
    
    subplot(2,3,5);
    bar(pdr_values, 'FaceColor', [0.8, 0.4, 0.2], 'EdgeColor', 'black');
    set(gca, 'XTickLabel', algorithms, 'XTickLabelRotation', 45);
    title('(e) PDR对比');
    ylabel('包递交率');
    ylim([0, 1]);
    grid on;
    
    subplot(2,3,6);
    bar(delay_values, 'FaceColor', [0.4, 0.8, 0.4], 'EdgeColor', 'black');
    set(gca, 'XTickLabel', algorithms, 'XTickLabelRotation', 45);
    title('(f) 延迟对比');
    ylabel('延迟 (ms)');
    grid on;
    
    sgtitle('分层强化学习WBAN功率控制演示结果', 'FontSize', 16, 'FontWeight', 'bold');
    
    % 图2: 综合性能雷达图
    figure('Position', [200, 200, 600, 600]);
    
    % 归一化数据
    energy_norm = (max(energy_values) - energy_values) / (max(energy_values) - min(energy_values));
    pdr_norm = pdr_values / max(pdr_values);
    delay_norm = (max(delay_values) - delay_values) / (max(delay_values) - min(delay_values));
    
    % 雷达图数据
    theta = linspace(0, 2*pi, 4);
    colors = {'r', 'g', 'b', 'k'};
    line_styles = {'-', '--', '-.', ':'};
    
    for i = 1:length(algorithms)
        values = [energy_norm(i), pdr_norm(i), delay_norm(i), energy_norm(i)]; % 闭合
        polarplot(theta, values, [colors{i} line_styles{i}], 'LineWidth', 2, 'MarkerSize', 8);
        hold on;
    end
    
    thetaticks([0, 90, 180, 270]);
    thetaticklabels({'能效', 'PDR', '延迟', ''});
    title('算法综合性能雷达图', 'FontSize', 14);
    legend(algorithms, 'Location', 'best');
    
    % 保存图片
    fig2 = gcf;
    saveas(fig2, 'rl_demo_performance_radar.png');

    % 保存第一个图
    figure(fig2.Number - 1);
    saveas(gcf, 'rl_demo_training_results.png');
end

fprintf('运行快速演示...\n');
