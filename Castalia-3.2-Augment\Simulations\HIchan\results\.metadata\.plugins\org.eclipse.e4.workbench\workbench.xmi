<?xml version="1.0" encoding="ASCII"?>
<application:Application xmi:version="2.0" xmlns:xmi="http://www.omg.org/XMI" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:advanced="http://www.eclipse.org/ui/2010/UIModel/application/ui/advanced" xmlns:application="http://www.eclipse.org/ui/2010/UIModel/application" xmlns:basic="http://www.eclipse.org/ui/2010/UIModel/application/ui/basic" xmlns:menu="http://www.eclipse.org/ui/2010/UIModel/application/ui/menu" xmi:id="_2vUVAUQHEfC4A_fUchu1mw" elementId="org.eclipse.e4.legacy.ide.application" contributorURI="platform:/plugin/org.eclipse.ui.workbench" selectedElement="_2vUVAkQHEfC4A_fUchu1mw" bindingContexts="_2vUVCEQHEfC4A_fUchu1mw">
  <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;workbench>&#xD;&#xA;&lt;mruList>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.omnetpp.scave.editors.ScaveEditor&quot; name=&quot;TMAC-0.vec&quot; tooltip=&quot;Castalia1304/TMAC-0.vec&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Castalia1304/TMAC-0.vec&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.omnetpp.scave.editors.ResultFileOpener.sca&quot; name=&quot;TMAC-0.sca&quot; tooltip=&quot;Castalia1304/TMAC-0.sca&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Castalia1304/TMAC-0.sca&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.omnetpp.scave.editors.ScaveEditor&quot; name=&quot;TMAC1304.anf&quot; tooltip=&quot;Castalia1304/TMAC1304.anf&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Castalia1304/TMAC1304.anf&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.omnetpp.scave.editors.ScaveEditor&quot; name=&quot;Tictoc16.anf&quot; tooltip=&quot;tictoc/Tictoc16.anf&quot;>&#xD;&#xA;&lt;persistable path=&quot;/tictoc/Tictoc16.anf&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.ide.FileStoreEditorInputFactory&quot; id=&quot;org.omnetpp.scave.editors.ResultFileOpener.sca&quot; name=&quot;TMAC-0.sca&quot; tooltip=&quot;C:\Castalia-3.2\Simulations\HIchan\results\TMAC-0.sca&quot;>&#xD;&#xA;&lt;persistable uri=&quot;file:/C:/Castalia-3.2/Simulations/HIchan/results/TMAC-0.sca&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;/mruList>&#xD;&#xA;&lt;/workbench>"/>
  <tags>activeSchemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
  <tags>ModelMigrationProcessor.001</tags>
  <children xsi:type="basic:TrimmedWindow" xmi:id="_2vUVAkQHEfC4A_fUchu1mw" elementId="IDEWindow" contributorURI="platform:/plugin/org.eclipse.ui.workbench" selectedElement="_24L9IkQHEfC4A_fUchu1mw" label="%trimmedwindow.label.eclipseSDK" x="0" y="0" width="1024" height="768">
    <persistedState key="coolBarVisible" value="true"/>
    <persistedState key="perspectiveBarVisible" value="true"/>
    <persistedState key="workingSets" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;workingSets/>"/>
    <persistedState key="aggregateWorkingSetId" value="Aggregate for window 1749356221718"/>
    <tags>topLevel</tags>
    <tags>shellMaximized</tags>
    <children xsi:type="basic:PartSashContainer" xmi:id="_24L9IkQHEfC4A_fUchu1mw" selectedElement="_24L9I0QHEfC4A_fUchu1mw" horizontal="true">
      <children xsi:type="advanced:PerspectiveStack" xmi:id="_24L9I0QHEfC4A_fUchu1mw" elementId="org.eclipse.ui.ide.perspectivestack" containerData="7500" selectedElement="_27GHMEQHEfC4A_fUchu1mw">
        <children xsi:type="advanced:Perspective" xmi:id="_27GHMEQHEfC4A_fUchu1mw" elementId="org.omnetpp.main.OmnetppPerspective" selectedElement="_27GHMUQHEfC4A_fUchu1mw" label="Simulation" iconURI="platform:/plugin/org.omnetpp.main/icons/logo16.png">
          <persistedState key="persp.hiddenItems" value="persp.hideToolbarSC:org.eclipse.debug.ui.commands.RunToLine,persp.hideToolbarSC:org.eclipse.ui.edit.text.toggleShowSelectedElementOnly,"/>
          <tags>persp.actionSet:org.eclipse.ui.cheatsheets.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.search.searchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.annotationNavigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo</tags>
          <tags>persp.actionSet:org.eclipse.ui.externaltools.ExternalToolsSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.keyBindings</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.openFiles</tags>
          <tags>persp.actionSet:org.omnetpp.ned.ActionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.NavigateActionSet</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.launchActionSet</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ContentOutline</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.PropertySheet</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ProblemView</tags>
          <tags>persp.viewSC:org.eclipse.ui.navigator.ProjectExplorer</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.TaskList</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ProgressView</tags>
          <tags>persp.viewSC:org.omnetpp.inifile.ModuleParameters</tags>
          <tags>persp.viewSC:org.omnetpp.inifile.ModuleHierarchy</tags>
          <tags>persp.viewSC:org.omnetpp.inifile.NedInheritance</tags>
          <tags>persp.viewSC:org.omnetpp.scave.DatasetView</tags>
          <tags>persp.viewSC:org.omnetpp.scave.VectorBrowserView</tags>
          <tags>persp.viewSC:org.omnetpp.sequencechart.editors.SequenceChartView</tags>
          <tags>persp.viewSC:org.omnetpp.eventlogtable.editors.EventLogTableView</tags>
          <tags>persp.newWizSC:org.omnetpp.main.wizards.NewOmnetppProject</tags>
          <tags>persp.newWizSC:org.omnetpp.cdt.wizards.NewOmnetppCCProject</tags>
          <tags>persp.newWizSC:org.omnetpp.ned.editor.wizards.NewSimulation</tags>
          <tags>persp.newWizSC:org.omnetpp.ned.editor.wizards.NewSimpleModule</tags>
          <tags>persp.newWizSC:org.omnetpp.ned.editor.wizards.NewCompoundModule</tags>
          <tags>persp.newWizSC:org.omnetpp.ned.editor.wizards.NewNetwork</tags>
          <tags>persp.newWizSC:org.omnetpp.ned.editor.wizards.NewNedFile</tags>
          <tags>persp.newWizSC:org.omnetpp.msg.editor.wizards.NewMsgFile</tags>
          <tags>persp.newWizSC:org.omnetpp.inifile.editor.wizards.NewIniFile</tags>
          <tags>persp.newWizSC:org.omnetpp.scave.wizards.NewScaveFile</tags>
          <tags>persp.newWizSC:org.eclipse.ui.wizards.new.folder</tags>
          <tags>persp.newWizSC:org.eclipse.ui.wizards.new.file</tags>
          <tags>persp.newWizSC:org.eclipse.ui.editors.wizards.UntitledTextFileWizard</tags>
          <tags>persp.newWizSC:org.omnetpp.common.wizards.NewWizard</tags>
          <tags>persp.perspSC:org.eclipse.cdt.ui.CPerspective</tags>
          <tags>persp.newWizSC:org.omnetpp.cdt.wizards.NewOmnetppClass</tags>
          <tags>persp.perspSC:org.eclipse.ui.resourcePerspective</tags>
          <tags>persp.perspSC:org.omnetpp.main.OmnetppPerspective</tags>
          <children xsi:type="basic:PartSashContainer" xmi:id="_27GHMUQHEfC4A_fUchu1mw" selectedElement="_27GHOUQHEfC4A_fUchu1mw" horizontal="true">
            <children xsi:type="basic:PartSashContainer" xmi:id="_27GHMkQHEfC4A_fUchu1mw" containerData="2500" selectedElement="_27GHM0QHEfC4A_fUchu1mw">
              <children xsi:type="basic:PartStack" xmi:id="_27GHM0QHEfC4A_fUchu1mw" elementId="left" containerData="5000" selectedElement="_27GHNEQHEfC4A_fUchu1mw">
                <tags>newtablook</tags>
                <children xsi:type="advanced:Placeholder" xmi:id="_27GHNEQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.navigator.ProjectExplorer" ref="_27ESAEQHEfC4A_fUchu1mw"/>
                <children xsi:type="advanced:Placeholder" xmi:id="_27GHNUQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.views.ResourceNavigator" toBeRendered="false" ref="_27E5EEQHEfC4A_fUchu1mw"/>
              </children>
              <children xsi:type="basic:PartStack" xmi:id="_27GHNkQHEfC4A_fUchu1mw" elementId="leftbottom" containerData="5000" selectedElement="_27GHN0QHEfC4A_fUchu1mw">
                <tags>newtablook</tags>
                <children xsi:type="advanced:Placeholder" xmi:id="_27GHN0QHEfC4A_fUchu1mw" elementId="org.eclipse.ui.views.PropertySheet" ref="_27E5EUQHEfC4A_fUchu1mw"/>
                <children xsi:type="advanced:Placeholder" xmi:id="_27GHOEQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.views.ContentOutline" ref="_27E5EkQHEfC4A_fUchu1mw"/>
              </children>
            </children>
            <children xsi:type="basic:PartSashContainer" xmi:id="_27GHOUQHEfC4A_fUchu1mw" containerData="7500" selectedElement="_27GHOkQHEfC4A_fUchu1mw">
              <children xsi:type="advanced:Placeholder" xmi:id="_27GHOkQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.editorss" containerData="7500" ref="_27AnoEQHEfC4A_fUchu1mw"/>
              <children xsi:type="basic:PartStack" xmi:id="_27GHO0QHEfC4A_fUchu1mw" elementId="bottom" containerData="2500" selectedElement="_27GHP0QHEfC4A_fUchu1mw">
                <tags>newtablook</tags>
                <children xsi:type="advanced:Placeholder" xmi:id="_27GHPEQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.views.ProblemView" ref="_27E5E0QHEfC4A_fUchu1mw"/>
                <children xsi:type="advanced:Placeholder" xmi:id="_27GHPUQHEfC4A_fUchu1mw" elementId="org.omnetpp.inifile.ModuleHierarchy" ref="_27E5FEQHEfC4A_fUchu1mw"/>
                <children xsi:type="advanced:Placeholder" xmi:id="_27GHPkQHEfC4A_fUchu1mw" elementId="org.omnetpp.inifile.ModuleParameters" ref="_27E5FUQHEfC4A_fUchu1mw"/>
                <children xsi:type="advanced:Placeholder" xmi:id="_27GHP0QHEfC4A_fUchu1mw" elementId="org.omnetpp.inifile.NedInheritance" ref="_27E5FkQHEfC4A_fUchu1mw"/>
                <children xsi:type="advanced:Placeholder" xmi:id="_27GHQEQHEfC4A_fUchu1mw" elementId="org.omnetpp.main.NewVersionView" toBeRendered="false" ref="_27FgIEQHEfC4A_fUchu1mw"/>
                <children xsi:type="advanced:Placeholder" xmi:id="_27GHQUQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.views.TaskList" toBeRendered="false" ref="_27FgIUQHEfC4A_fUchu1mw"/>
                <children xsi:type="advanced:Placeholder" xmi:id="_27GHQkQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.views.BookmarkView" toBeRendered="false" ref="_27FgIkQHEfC4A_fUchu1mw"/>
                <children xsi:type="advanced:Placeholder" xmi:id="_27GHQ0QHEfC4A_fUchu1mw" elementId="org.eclipse.ui.console.ConsoleView" toBeRendered="false" ref="_27FgI0QHEfC4A_fUchu1mw"/>
                <children xsi:type="advanced:Placeholder" xmi:id="_27GHREQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.views.ProgressView" toBeRendered="false" ref="_27FgJEQHEfC4A_fUchu1mw"/>
              </children>
            </children>
          </children>
        </children>
      </children>
      <children xsi:type="basic:PartStack" xmi:id="_24L9JEQHEfC4A_fUchu1mw" elementId="stickyFolderRight" containerData="2500" selectedElement="_24L9JUQHEfC4A_fUchu1mw">
        <children xsi:type="advanced:Placeholder" xmi:id="_24L9JUQHEfC4A_fUchu1mw" elementId="org.eclipse.help.ui.HelpView" ref="_24LWEEQHEfC4A_fUchu1mw"/>
        <children xsi:type="advanced:Placeholder" xmi:id="_24L9JkQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.internal.introview" toBeRendered="false" ref="_24L9IEQHEfC4A_fUchu1mw"/>
        <children xsi:type="advanced:Placeholder" xmi:id="_24L9J0QHEfC4A_fUchu1mw" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" toBeRendered="false" ref="_24L9IUQHEfC4A_fUchu1mw"/>
      </children>
    </children>
    <sharedElements xsi:type="basic:Part" xmi:id="_24LWEEQHEfC4A_fUchu1mw" elementId="org.eclipse.help.ui.HelpView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Help" iconURI="platform:/plugin/org.eclipse.help.ui/icons/view16/help_view.gif" closeable="true">
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view pageId=&quot;all-topics-page&quot;/>"/>
      <tags>View</tags>
      <tags>categoryTag:Help</tags>
      <menus xmi:id="_AAZe0EQIEfC4A_fUchu1mw" elementId="org.eclipse.help.ui.HelpView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_AAZe0UQIEfC4A_fUchu1mw" elementId="org.eclipse.help.ui.HelpView"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_24L9IEQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.internal.introview" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Welcome" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_3J1t0EQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.internal.introview">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_3J1t0UQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.internal.introview" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_24L9IUQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Cheat Sheets" iconURI="platform:/plugin/org.eclipse.ui.cheatsheets/icons/view16/cheatsheet_view.gif" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:Help</tags>
    </sharedElements>
    <sharedElements xsi:type="advanced:Area" xmi:id="_27AnoEQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.editorss" selectedElement="_27BOsEQHEfC4A_fUchu1mw">
      <children xsi:type="basic:PartStack" xmi:id="_27BOsEQHEfC4A_fUchu1mw" elementId="org.eclipse.e4.primaryDataStack" selectedElement="_WUm9EEgAEfCvl7MoF-9-tw">
        <tags>newtablook</tags>
        <tags>org.eclipse.e4.primaryDataStack</tags>
        <tags>EditorStack</tags>
        <tags>active</tags>
        <children xsi:type="basic:Part" xmi:id="_rc_YQEQfEfCBofbSE2IXgg" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="TMAC1304.anf" iconURI="platform:/plugin/org.omnetpp.scave/icons/scavefile.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.omnetpp.scave.editors.ScaveEditor&quot; name=&quot;TMAC1304.anf&quot; partName=&quot;TMAC1304.anf&quot; title=&quot;TMAC1304.anf&quot; tooltip=&quot;Castalia1304/TMAC1304.anf&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/Castalia1304/TMAC1304.anf&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>org.omnetpp.scave.editors.ScaveEditor</tags>
          <tags>removeOnHide</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_u3CdQEQfEfCBofbSE2IXgg" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="Tictoc16.anf" iconURI="platform:/plugin/org.omnetpp.scave/icons/scavefile.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.omnetpp.scave.editors.ScaveEditor&quot; name=&quot;Tictoc16.anf&quot; partName=&quot;Tictoc16.anf&quot; title=&quot;Tictoc16.anf&quot; tooltip=&quot;tictoc/Tictoc16.anf&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/tictoc/Tictoc16.anf&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>org.omnetpp.scave.editors.ScaveEditor</tags>
          <tags>removeOnHide</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_WUm9EEgAEfCvl7MoF-9-tw" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" iconURI="platform:/plugin/org.omnetpp.scave/icons/scavefile.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.omnetpp.scave.editors.ScaveEditor&quot; name=&quot;TMAC-0.vec&quot; title=&quot;TMAC-0.vec&quot; tooltip=&quot;&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/Castalia1304/TMAC-0.vec&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>org.omnetpp.scave.editors.ScaveEditor</tags>
          <tags>removeOnHide</tags>
          <tags>active</tags>
          <tags>activeOnClose</tags>
        </children>
      </children>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_27ESAEQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.navigator.ProjectExplorer" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Project Explorer" iconURI="platform:/plugin/org.eclipse.ui.navigator.resources/icons/full/eview16/resource_persp.gif" closeable="true">
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view CommonNavigator.LINKING_ENABLED=&quot;0&quot; org.eclipse.cdt.ui.cview.groupincludes=&quot;false&quot; org.eclipse.cdt.ui.cview.groupmacros=&quot;false&quot; org.eclipse.cdt.ui.editor.CUChildren=&quot;true&quot; org.eclipse.ui.navigator.resources.workingSets.showTopLevelWorkingSets=&quot;0&quot;/>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_28TBEEQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.navigator.ProjectExplorer">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_28TBEUQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.navigator.ProjectExplorer"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_27E5EEQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.views.ResourceNavigator" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Navigator" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/filenav_nav.png" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_27E5EUQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.views.PropertySheet" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Properties" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/prop_ps.png" closeable="true">
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_3AngYEQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.views.PropertySheet">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_3AngYUQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.views.PropertySheet"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_27E5EkQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.views.ContentOutline" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Outline" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/outline_co.png" closeable="true">
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_SoODoEQKEfC4A_fUchu1mw" elementId="org.eclipse.ui.views.ContentOutline">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_SoODoUQKEfC4A_fUchu1mw" elementId="org.eclipse.ui.views.ContentOutline" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_27E5E0QHEfC4A_fUchu1mw" elementId="org.eclipse.ui.views.ProblemView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Problems" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" closeable="true">
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view PRIMARY_SORT_FIELD=&quot;org.eclipse.ui.ide.severityAndDescriptionField&quot; categoryGroup=&quot;org.eclipse.ui.ide.severity&quot; markerContentGenerator=&quot;org.eclipse.ui.ide.problemsGenerator&quot; partName=&quot;Problems&quot;>&#xD;&#xA;&lt;columnWidths org.eclipse.ui.ide.locationField=&quot;105&quot; org.eclipse.ui.ide.markerType=&quot;105&quot; org.eclipse.ui.ide.pathField=&quot;140&quot; org.eclipse.ui.ide.resourceField=&quot;105&quot; org.eclipse.ui.ide.severityAndDescriptionField=&quot;350&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.severityAndDescriptionField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.resourceField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.pathField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.locationField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.markerType&quot;/>&#xD;&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_3BlwwEQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.views.ProblemView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_3BlwwUQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.views.ProblemView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_27E5FEQHEfC4A_fUchu1mw" elementId="org.omnetpp.inifile.ModuleHierarchy" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Module Hierarchy" iconURI="platform:/plugin/org.omnetpp.inifile.editor/icons/full/eview16/modulehierarchy.png" closeable="true">
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:OMNeT++</tags>
      <menus xmi:id="_ZFUp8EQKEfC4A_fUchu1mw" elementId="org.omnetpp.inifile.ModuleHierarchy">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_ZFVRAEQKEfC4A_fUchu1mw" elementId="org.omnetpp.inifile.ModuleHierarchy" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_27E5FUQHEfC4A_fUchu1mw" elementId="org.omnetpp.inifile.ModuleParameters" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="NED Parameters" iconURI="platform:/plugin/org.omnetpp.inifile.editor/icons/full/eview16/moduleparameters.png" closeable="true">
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:OMNeT++</tags>
      <menus xmi:id="_ZTg28EQKEfC4A_fUchu1mw" elementId="org.omnetpp.inifile.ModuleParameters">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_ZTg28UQKEfC4A_fUchu1mw" elementId="org.omnetpp.inifile.ModuleParameters" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_27E5FkQHEfC4A_fUchu1mw" elementId="org.omnetpp.inifile.NedInheritance" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="NED Inheritance" iconURI="platform:/plugin/org.omnetpp.inifile.editor/icons/full/eview16/inheritance.png" closeable="true">
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:OMNeT++</tags>
      <menus xmi:id="_ZgoF4EQKEfC4A_fUchu1mw" elementId="org.omnetpp.inifile.NedInheritance">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_ZgoF4UQKEfC4A_fUchu1mw" elementId="org.omnetpp.inifile.NedInheritance"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_27FgIEQHEfC4A_fUchu1mw" elementId="org.omnetpp.main.NewVersionView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="New OMNeT++ Version" iconURI="platform:/plugin/org.omnetpp.main/icons/logo16.png" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:OMNeT++</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_27FgIUQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.views.TaskList" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Tasks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/tasks_tsk.png" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_27FgIkQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.views.BookmarkView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Bookmarks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/bkmrk_nav.png" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_27FgI0QHEfC4A_fUchu1mw" elementId="org.eclipse.ui.console.ConsoleView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Console" iconURI="platform:/plugin/org.eclipse.ui.console/icons/full/cview16/console_view.png" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_27FgJEQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.views.ProgressView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Progress" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/pview.png" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <trimBars xmi:id="_2vUVA0QHEfC4A_fUchu1mw" elementId="org.eclipse.ui.main.toolbar" contributorURI="platform:/plugin/org.eclipse.ui.workbench">
      <children xsi:type="menu:ToolBar" xmi:id="_2402UEQHEfC4A_fUchu1mw" elementId="group.file" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_2402UUQHEfC4A_fUchu1mw" elementId="group.file" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_241dYEQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.workbench.file">
        <tags>Draggable</tags>
        <children xsi:type="menu:HandledToolItem" xmi:id="_IIS6QEf-EfCvl7MoF-9-tw" elementId="print" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/print_edit.png" tooltip="Print" enabled="false" command="_2vn3YEQHEfC4A_fUchu1mw"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_241dYUQHEfC4A_fUchu1mw" elementId="additions" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_241dYkQHEfC4A_fUchu1mw" elementId="additions" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_27S7gEQHEfC4A_fUchu1mw" elementId="org.eclipse.debug.ui.launchActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_27X0AEQHEfC4A_fUchu1mw" elementId="org.eclipse.search.searchActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_241dY0QHEfC4A_fUchu1mw" elementId="group.nav" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_241dZEQHEfC4A_fUchu1mw" elementId="group.nav" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_241dZUQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.workbench.navigate">
        <tags>Draggable</tags>
        <children xsi:type="menu:HandledToolItem" xmi:id="_IIUvdUf-EfCvl7MoF-9-tw" elementId="org.eclipse.ui.window.pinEditor" visible="false" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/pin_editor.png" tooltip="Pin Editor" type="Check" command="_2vla7UQHEfC4A_fUchu1mw"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_241dZkQHEfC4A_fUchu1mw" elementId="group.editor" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_241dZ0QHEfC4A_fUchu1mw" elementId="group.editor" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_241daEQHEfC4A_fUchu1mw" elementId="group.help" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_241daUQHEfC4A_fUchu1mw" elementId="group.help" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_241dakQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.workbench.help">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_25hZ4EQHEfC4A_fUchu1mw" elementId="PerspectiveSpacer" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
        <tags>stretch</tags>
        <tags>SHOW_RESTORE_MENU</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_25iA8EQHEfC4A_fUchu1mw" elementId="PerspectiveSwitcher" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.e4.ui.workbench.addons.perspectiveswitcher.PerspectiveSwitcher">
        <tags>Draggable</tags>
        <tags>HIDEABLE</tags>
        <tags>SHOW_RESTORE_MENU</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_2vUVBEQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.trim.status" contributorURI="platform:/plugin/org.eclipse.ui.workbench" side="Bottom">
      <children xsi:type="menu:ToolControl" xmi:id="_25nggEQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.StatusLine" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>stretch</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_25-F0EQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.HeapStatus" toBeRendered="false" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_26CXQEQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.ProgressBar" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>Draggable</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_2vUVBUQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.trim.vertical1" contributorURI="platform:/plugin/org.eclipse.ui.workbench" toBeRendered="false" side="Left">
      <children xsi:type="menu:ToolControl" xmi:id="_3MeLEEQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.ide.perspectivestack(minimized)" toBeRendered="false" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.TrimStack">
        <tags>TrimStack</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_2vUVBkQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.trim.vertical2" contributorURI="platform:/plugin/org.eclipse.ui.workbench" side="Right"/>
  </children>
  <bindingTables xmi:id="_2vUVB0QHEfC4A_fUchu1mw" contributorURI="platform:/plugin/org.eclipse.ui.workbench" bindingContext="_2vUVCEQHEfC4A_fUchu1mw">
    <bindings xmi:id="_2v15cEQHEfC4A_fUchu1mw" keySequence="CTRL+INSERT" command="_2vmpGUQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v2gkUQHEfC4A_fUchu1mw" keySequence="CTRL+A" command="_2vmpDkQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v4Vx0QHEfC4A_fUchu1mw" keySequence="CTRL+V" command="_2vmB9EQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v4VyEQHEfC4A_fUchu1mw" keySequence="CTRL+X" command="_2vn3FEQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v4VyUQHEfC4A_fUchu1mw" keySequence="CTRL+Y" command="_2vn3WUQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v4VzEQHEfC4A_fUchu1mw" keySequence="CTRL+Z" command="_2vmCAUQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v6K6EQHEfC4A_fUchu1mw" keySequence="CTRL+F10" command="_2vmpL0QHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v6K7kQHEfC4A_fUchu1mw" keySequence="ALT+SHIFT+F3" command="_2voeeUQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v6K80QHEfC4A_fUchu1mw" keySequence="CTRL+1" command="_2vnQOUQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v6K9EQHEfC4A_fUchu1mw" keySequence="CTRL+SHIFT+L" command="_2vmB5UQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v6x9UQHEfC4A_fUchu1mw" keySequence="ALT+/" command="_2vnQCEQHEfC4A_fUchu1mw">
      <tags>locale:zh</tags>
    </bindings>
    <bindings xmi:id="_2v6yEEQHEfC4A_fUchu1mw" keySequence="SHIFT+DEL" command="_2vn3FEQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v7ZB0QHEfC4A_fUchu1mw" keySequence="CTRL+C" command="_2vmpGUQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v7ZCkQHEfC4A_fUchu1mw" keySequence="ALT+PAGE_UP" command="_2vnP_kQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v7ZIEQHEfC4A_fUchu1mw" keySequence="ALT+PAGE_DOWN" command="_2vn3IkQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v7ZJUQHEfC4A_fUchu1mw" keySequence="SHIFT+INSERT" command="_2vmB9EQHEfC4A_fUchu1mw"/>
  </bindingTables>
  <bindingTables xmi:id="_2v2ggEQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.contexts.window" bindingContext="_2vUVCUQHEfC4A_fUchu1mw">
    <bindings xmi:id="_2v2ggUQHEfC4A_fUchu1mw" keySequence="CTRL+SHIFT+NUMPAD_MULTIPLY" command="_2vla0kQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v2gh0QHEfC4A_fUchu1mw" keySequence="CTRL+TAB" command="_2vlayUQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v2gjUQHEfC4A_fUchu1mw" keySequence="CTRL+E" command="_2vmB7UQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v2gkEQHEfC4A_fUchu1mw" keySequence="ALT+ARROW_LEFT" command="_2vnQQ0QHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v2gl0QHEfC4A_fUchu1mw" keySequence="ALT+?" command="_2voeNUQHEfC4A_fUchu1mw">
      <tags>locale:zh</tags>
    </bindings>
    <bindings xmi:id="_2v2gmUQHEfC4A_fUchu1mw" keySequence="CTRL+=" command="_2vnQN0QHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v3HkkQHEfC4A_fUchu1mw" keySequence="ALT+SHIFT+Q O" command="_2voeaEQHEfC4A_fUchu1mw">
      <parameters xmi:id="_2v3Hk0QHEfC4A_fUchu1mw" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.views.ContentOutline"/>
    </bindings>
    <bindings xmi:id="_2v3HlEQHEfC4A_fUchu1mw" keySequence="ALT+SHIFT+Q X" command="_2voeaEQHEfC4A_fUchu1mw">
      <parameters xmi:id="_2v3HlUQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.views.ProblemView"/>
    </bindings>
    <bindings xmi:id="_2v3HlkQHEfC4A_fUchu1mw" keySequence="ALT+SHIFT+Q Q" command="_2voeaEQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v3Hl0QHEfC4A_fUchu1mw" keySequence="ALT+SHIFT+Q C" command="_2voeaEQHEfC4A_fUchu1mw">
      <parameters xmi:id="_2v3HmEQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.console.ConsoleView"/>
    </bindings>
    <bindings xmi:id="_2v3HmUQHEfC4A_fUchu1mw" keySequence="ALT+SHIFT+Q H" command="_2voeaEQHEfC4A_fUchu1mw">
      <parameters xmi:id="_2v3HmkQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.cheatsheets.views.CheatSheetView"/>
    </bindings>
    <bindings xmi:id="_2v3uoEQHEfC4A_fUchu1mw" keySequence="ALT+SHIFT+Q B" command="_2voeaEQHEfC4A_fUchu1mw">
      <parameters xmi:id="_2v3uoUQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.debug.ui.BreakpointView"/>
    </bindings>
    <bindings xmi:id="_2v3uokQHEfC4A_fUchu1mw" keySequence="ALT+SHIFT+Q V" command="_2voeaEQHEfC4A_fUchu1mw">
      <parameters xmi:id="_2v3uo0QHEfC4A_fUchu1mw" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.debug.ui.VariableView"/>
    </bindings>
    <bindings xmi:id="_2v3upEQHEfC4A_fUchu1mw" keySequence="ALT+SHIFT+Q S" command="_2voeaEQHEfC4A_fUchu1mw">
      <parameters xmi:id="_2v3upUQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.search.ui.views.SearchView"/>
    </bindings>
    <bindings xmi:id="_2v3upkQHEfC4A_fUchu1mw" keySequence="ALT+SHIFT+Q Y" command="_2voeaEQHEfC4A_fUchu1mw">
      <parameters xmi:id="_2v3up0QHEfC4A_fUchu1mw" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.team.sync.views.SynchronizeView"/>
    </bindings>
    <bindings xmi:id="_2v3uqEQHEfC4A_fUchu1mw" keySequence="ALT+SHIFT+Q Z" command="_2voeaEQHEfC4A_fUchu1mw">
      <parameters xmi:id="_2v3uqUQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.team.ui.GenericHistoryView"/>
    </bindings>
    <bindings xmi:id="_2v3urUQHEfC4A_fUchu1mw" keySequence="CTRL+SHIFT+B" command="_2vn3PEQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v3usEQHEfC4A_fUchu1mw" keySequence="ALT+CTRL+G" command="_2vmB6UQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v3usUQHEfC4A_fUchu1mw" keySequence="ALT+SHIFT+?" command="_2voeNUQHEfC4A_fUchu1mw">
      <tags>locale:zh</tags>
    </bindings>
    <bindings xmi:id="_2v4VtEQHEfC4A_fUchu1mw" keySequence="CTRL+SHIFT+E" command="_2vnQGkQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v4VuUQHEfC4A_fUchu1mw" keySequence="ALT+ARROW_RIGHT" command="_2vn3YUQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v4VukQHEfC4A_fUchu1mw" keySequence="CTRL+B" command="_2vla0EQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v4VvUQHEfC4A_fUchu1mw" keySequence="CTRL+SHIFT+F6" command="_2vnP80QHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v4VvkQHEfC4A_fUchu1mw" keySequence="CTRL+F6" command="_2vmCJEQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v4Vv0QHEfC4A_fUchu1mw" keySequence="CTRL+M" command="_2vnP9EQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v4VwEQHEfC4A_fUchu1mw" keySequence="F12" command="_2vn3NUQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v4VwUQHEfC4A_fUchu1mw" keySequence="ALT+-" command="_2vnQSEQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v4VwkQHEfC4A_fUchu1mw" keySequence="CTRL+," command="_2vmB9UQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v4Vw0QHEfC4A_fUchu1mw" keySequence="CTRL+." command="_2vmo_UQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v4VxEQHEfC4A_fUchu1mw" keySequence="DEL" command="_2vmB5EQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v48wkQHEfC4A_fUchu1mw" keySequence="F5" command="_2vmCHUQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v48xkQHEfC4A_fUchu1mw" keySequence="ALT+CR" command="_2vn3d0QHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v48x0QHEfC4A_fUchu1mw" keySequence="CTRL+P" command="_2vn3YEQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v48yEQHEfC4A_fUchu1mw" keySequence="CTRL+SHIFT+S" command="_2vmpKUQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v48yUQHEfC4A_fUchu1mw" keySequence="CTRL+S" command="_2vnQMEQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v48ykQHEfC4A_fUchu1mw" keySequence="CTRL+SHIFT+F4" command="_2voeckQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v48y0QHEfC4A_fUchu1mw" keySequence="CTRL+SHIFT+W" command="_2voeckQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v48zEQHEfC4A_fUchu1mw" keySequence="CTRL+F4" command="_2vmpEkQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v48z0QHEfC4A_fUchu1mw" keySequence="CTRL+W" command="_2vmpEkQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v480UQHEfC4A_fUchu1mw" keySequence="CTRL+N" command="_2vmpFEQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v480kQHEfC4A_fUchu1mw" keySequence="CTRL+-" command="_2vn3CUQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v5j0UQHEfC4A_fUchu1mw" keySequence="CTRL+F11" command="_2vn3ZkQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v5j0kQHEfC4A_fUchu1mw" keySequence="F11" command="_2vmCA0QHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v5j7UQHEfC4A_fUchu1mw" keySequence="CTRL+SHIFT+F11" command="_2vmpBUQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v5j7kQHEfC4A_fUchu1mw" keySequence="CTRL+SHIFT+N" command="_2vmCHEQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v5j70QHEfC4A_fUchu1mw" keySequence="F2" command="_2vmo_EQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v6K50QHEfC4A_fUchu1mw" keySequence="CTRL+Q" command="_2vn3P0QHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v6K70QHEfC4A_fUchu1mw" keySequence="CTRL+3" command="_2voee0QHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v6K8EQHEfC4A_fUchu1mw" keySequence="ALT+SHIFT+N" command="_2vmB_EQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v6K8UQHEfC4A_fUchu1mw" keySequence="ALT+SHIFT+W" command="_2vmo9UQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v6K8kQHEfC4A_fUchu1mw" keySequence="CTRL+SHIFT+R" command="_2vmpI0QHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v6K90QHEfC4A_fUchu1mw" keySequence="CTRL+SHIFT+NUMPAD_DIVIDE" command="_2vn3TkQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v6x8UQHEfC4A_fUchu1mw" keySequence="CTRL+{" command="_2vn3BkQHEfC4A_fUchu1mw">
      <parameters xmi:id="_2v6x8kQHEfC4A_fUchu1mw" elementId="Splitter.isHorizontal" name="Splitter.isHorizontal" value="false"/>
    </bindings>
    <bindings xmi:id="_2v6x80QHEfC4A_fUchu1mw" keySequence="ALT+SHIFT+F7" command="_2vmpIkQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v6x9EQHEfC4A_fUchu1mw" keySequence="ALT+F7" command="_2vnQQEQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v6x9kQHEfC4A_fUchu1mw" keySequence="CTRL+SHIFT+F8" command="_2vnQI0QHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v6x90QHEfC4A_fUchu1mw" keySequence="CTRL+F8" command="_2voelUQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v6x-EQHEfC4A_fUchu1mw" keySequence="CTRL+SHIFT+F7" command="_2vnQG0QHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v6x-UQHEfC4A_fUchu1mw" keySequence="CTRL+F7" command="_2vn3dEQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v6x-0QHEfC4A_fUchu1mw" keySequence="SHIFT+F5" command="_2vmCC0QHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v6yDkQHEfC4A_fUchu1mw" keySequence="F9" command="_2vnP8UQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v6yD0QHEfC4A_fUchu1mw" keySequence="SHIFT+F9" command="_2voeI0QHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v7ZAUQHEfC4A_fUchu1mw" keySequence="CTRL+F" command="_2vn3F0QHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v7ZCUQHEfC4A_fUchu1mw" keySequence="CTRL+#" command="_2vn3KUQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v7ZFEQHEfC4A_fUchu1mw" keySequence="CTRL+_" command="_2vn3BkQHEfC4A_fUchu1mw">
      <parameters xmi:id="_2v7ZFUQHEfC4A_fUchu1mw" elementId="Splitter.isHorizontal" name="Splitter.isHorizontal" value="true"/>
    </bindings>
    <bindings xmi:id="_2v7ZI0QHEfC4A_fUchu1mw" keySequence="CTRL+H" command="_2vn3eEQHEfC4A_fUchu1mw"/>
  </bindingTables>
  <bindingTables xmi:id="_2v2ggkQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.textEditorScope" bindingContext="_2vqTS0QHEfC4A_fUchu1mw">
    <bindings xmi:id="_2v2gg0QHEfC4A_fUchu1mw" keySequence="CTRL+SHIFT+NUMPAD_MULTIPLY" command="_2vla8UQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v2ghEQHEfC4A_fUchu1mw" keySequence="CTRL+SHIFT+J" command="_2vn3M0QHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v2gk0QHEfC4A_fUchu1mw" keySequence="CTRL+ARROW_UP" command="_2vn3XUQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v3uskQHEfC4A_fUchu1mw" keySequence="ALT+CTRL+ARROW_UP" command="_2vn3Z0QHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v3us0QHEfC4A_fUchu1mw" keySequence="CTRL+SHIFT+INSERT" command="_2vn3H0QHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v3utEQHEfC4A_fUchu1mw" keySequence="CTRL+SHIFT+DEL" command="_2vmCIkQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v4VsEQHEfC4A_fUchu1mw" keySequence="ALT+ARROW_DOWN" command="_2vn3FkQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v4Vs0QHEfC4A_fUchu1mw" keySequence="ALT+CTRL+ARROW_DOWN" command="_2voeE0QHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v4VtkQHEfC4A_fUchu1mw" keySequence="ALT+SHIFT+A" command="_2vnQL0QHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v4Vt0QHEfC4A_fUchu1mw" keySequence="CTRL+SHIFT+ARROW_RIGHT" command="_2voed0QHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v4Vu0QHEfC4A_fUchu1mw" keySequence="CTRL+ARROW_DOWN" command="_2vn3VkQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v5j5kQHEfC4A_fUchu1mw" keySequence="ALT+CTRL+/" command="_2voelkQHEfC4A_fUchu1mw">
      <tags>locale:zh</tags>
    </bindings>
    <bindings xmi:id="_2v5j50QHEfC4A_fUchu1mw" keySequence="CTRL+K" command="_2voeWkQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v5j8EQHEfC4A_fUchu1mw" keySequence="F2" command="_2vmB3EQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v6K4UQHEfC4A_fUchu1mw" keySequence="HOME" command="_2vnQK0QHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v6K4kQHEfC4A_fUchu1mw" keySequence="END" command="_2voegUQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v6K40QHEfC4A_fUchu1mw" keySequence="CTRL+SHIFT+Y" command="_2vnQSUQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v6K5EQHEfC4A_fUchu1mw" keySequence="CTRL+SHIFT+X" command="_2voePkQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v6K5UQHEfC4A_fUchu1mw" keySequence="CTRL+SHIFT+CR" command="_2vla2kQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v6K5kQHEfC4A_fUchu1mw" keySequence="SHIFT+CR" command="_2vnQAEQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v6K6UQHEfC4A_fUchu1mw" keySequence="CTRL+F10" command="_2vnQO0QHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v6K6kQHEfC4A_fUchu1mw" keySequence="INSERT" command="_2vmB4UQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v6K60QHEfC4A_fUchu1mw" keySequence="CTRL+L" command="_2vn3IUQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v6K7EQHEfC4A_fUchu1mw" keySequence="CTRL+J" command="_2vmpMUQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v6K7UQHEfC4A_fUchu1mw" keySequence="CTRL+SHIFT+K" command="_2voeZ0QHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v6K9UQHEfC4A_fUchu1mw" keySequence="CTRL+NUMPAD_SUBTRACT" command="_2vn3cEQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v6K9kQHEfC4A_fUchu1mw" keySequence="CTRL+NUMPAD_ADD" command="_2vnQKEQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v6K-EQHEfC4A_fUchu1mw" keySequence="CTRL+SHIFT+NUMPAD_DIVIDE" command="_2vnP-EQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v6K-UQHEfC4A_fUchu1mw" keySequence="CTRL+NUMPAD_MULTIPLY" command="_2vnQCkQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v6K-kQHEfC4A_fUchu1mw" keySequence="CTRL+NUMPAD_DIVIDE" command="_2vn3T0QHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v6x8EQHEfC4A_fUchu1mw" keySequence="CTRL+SHIFT+Q" command="_2vn3G0QHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v7ZAEQHEfC4A_fUchu1mw" keySequence="SHIFT+HOME" command="_2vn3LEQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v7ZC0QHEfC4A_fUchu1mw" keySequence="CTRL+ARROW_LEFT" command="_2vmCEUQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v7ZEkQHEfC4A_fUchu1mw" keySequence="SHIFT+END" command="_2vla1kQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v7ZE0QHEfC4A_fUchu1mw" keySequence="CTRL+HOME" command="_2vn3dkQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v7ZFkQHEfC4A_fUchu1mw" keySequence="CTRL+DEL" command="_2vmB_kQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v7ZF0QHEfC4A_fUchu1mw" keySequence="ALT+ARROW_UP" command="_2vn3e0QHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v7ZIUQHEfC4A_fUchu1mw" keySequence="CTRL+D" command="_2vmpLUQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v7ZIkQHEfC4A_fUchu1mw" keySequence="CTRL+ARROW_RIGHT" command="_2voeF0QHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v7ZJkQHEfC4A_fUchu1mw" keySequence="ALT+CTRL+J" command="_2vn3W0QHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v7ZJ0QHEfC4A_fUchu1mw" keySequence="CTRL+END" command="_2vmB4kQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v8AEEQHEfC4A_fUchu1mw" keySequence="CTRL+BS" command="_2vmo6EQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v8AE0QHEfC4A_fUchu1mw" keySequence="CTRL+SHIFT+ARROW_LEFT" command="_2vn3C0QHEfC4A_fUchu1mw"/>
  </bindingTables>
  <bindingTables xmi:id="_2v2ghUQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.ui.cEditorScope" bindingContext="_2vqTTkQHEfC4A_fUchu1mw">
    <bindings xmi:id="_2v2ghkQHEfC4A_fUchu1mw" keySequence="CTRL+TAB" command="_2voeYEQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v2giEQHEfC4A_fUchu1mw" keySequence="CTRL+I" command="_2vmCG0QHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v2gkkQHEfC4A_fUchu1mw" keySequence="ALT+C" command="_2vmB4EQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v2gmEQHEfC4A_fUchu1mw" keySequence="CTRL+=" command="_2vmB-kQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v3HkEQHEfC4A_fUchu1mw" keySequence="CTRL+SHIFT+F" command="_2voeQEQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v3uqkQHEfC4A_fUchu1mw" keySequence="ALT+CTRL+I" command="_2voemEQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v3urkQHEfC4A_fUchu1mw" keySequence="ALT+SHIFT+ARROW_LEFT" command="_2vmpHkQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v3ur0QHEfC4A_fUchu1mw" keySequence="CTRL+SHIFT+ARROW_DOWN" command="_2vnQSkQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v4VtUQHEfC4A_fUchu1mw" keySequence="ALT+SHIFT+ARROW_UP" command="_2voeXkQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v4810QHEfC4A_fUchu1mw" keySequence="CTRL+T" command="_2vlax0QHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v5j10QHEfC4A_fUchu1mw" keySequence="ALT+SHIFT+Z" command="_2voeKkQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v5j2EQHEfC4A_fUchu1mw" keySequence="CTRL+SHIFT+/" command="_2vnQDEQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v5j20QHEfC4A_fUchu1mw" keySequence="CTRL+/" command="_2voeHkQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v5j4EQHEfC4A_fUchu1mw" keySequence="ALT+SHIFT+S" command="_2vn3cUQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v5j6kQHEfC4A_fUchu1mw" keySequence="CTRL+SHIFT+O" command="_2vmo9kQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v6x_EQHEfC4A_fUchu1mw" keySequence="ALT+SHIFT+L" command="_2vla20QHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v6x_UQHEfC4A_fUchu1mw" keySequence="ALT+SHIFT+T" command="_2vmpH0QHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v6x_kQHEfC4A_fUchu1mw" keySequence="ALT+SHIFT+M" command="_2vmB8kQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v6x_0QHEfC4A_fUchu1mw" keySequence="ALT+SHIFT+R" command="_2voedUQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v6yAUQHEfC4A_fUchu1mw" keySequence="ALT+SHIFT+O" command="_2vn3JUQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v6yAkQHEfC4A_fUchu1mw" keySequence="CTRL+SHIFT+P" command="_2vnQD0QHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v6yA0QHEfC4A_fUchu1mw" keySequence="CTRL+O" command="_2vn3fUQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v6yBEQHEfC4A_fUchu1mw" keySequence="ALT+CTRL+S" command="_2vnQEkQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v6yBUQHEfC4A_fUchu1mw" keySequence="F4" command="_2vn3Q0QHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v6yB0QHEfC4A_fUchu1mw" keySequence="CTRL+SHIFT+T" command="_2vn3TEQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v6yCkQHEfC4A_fUchu1mw" keySequence="F3" command="_2vn3EkQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v7ZCEQHEfC4A_fUchu1mw" keySequence="CTRL+#" command="_2vmB-kQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v7ZDUQHEfC4A_fUchu1mw" keySequence="CTRL+G" command="_2vn3XEQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v7ZGUQHEfC4A_fUchu1mw" keySequence="CTRL+SHIFT+G" command="_2vn3KkQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v7ZHkQHEfC4A_fUchu1mw" keySequence="ALT+SHIFT+ARROW_DOWN" command="_2voePUQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v7ZH0QHEfC4A_fUchu1mw" keySequence="CTRL+SHIFT+ARROW_UP" command="_2voeLEQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v7ZJEQHEfC4A_fUchu1mw" keySequence="SHIFT+TAB" command="_2voeV0QHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v8AEkQHEfC4A_fUchu1mw" keySequence="ALT+SHIFT+ARROW_RIGHT" command="_2vnQLEQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v8AFEQHEfC4A_fUchu1mw" keySequence="ALT+CTRL+H" command="_2voeFEQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v8AFkQHEfC4A_fUchu1mw" keySequence="CTRL+SHIFT+H" command="_2vmo8kQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v8AGEQHEfC4A_fUchu1mw" keySequence="CTRL+SHIFT+\" command="_2vn3UkQHEfC4A_fUchu1mw"/>
  </bindingTables>
  <bindingTables xmi:id="_2v2giUQHEfC4A_fUchu1mw" elementId="org.omnetpp.context.msgEditor" bindingContext="_2vqTT0QHEfC4A_fUchu1mw">
    <bindings xmi:id="_2v2gikQHEfC4A_fUchu1mw" keySequence="CTRL+I" command="_2vla6kQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v5j3kQHEfC4A_fUchu1mw" keySequence="CTRL+/" command="_2voeEkQHEfC4A_fUchu1mw"/>
  </bindingTables>
  <bindingTables xmi:id="_2v2gi0QHEfC4A_fUchu1mw" elementId="org.omnetpp.context.nedTextEditor" bindingContext="_2vqTV0QHEfC4A_fUchu1mw">
    <bindings xmi:id="_2v2gjEQHEfC4A_fUchu1mw" keySequence="CTRL+I" command="_2vmpK0QHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v3HkUQHEfC4A_fUchu1mw" keySequence="CTRL+SHIFT+F" command="_2vnQOEQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v5j30QHEfC4A_fUchu1mw" keySequence="CTRL+/" command="_2vmCFEQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v5j7EQHEfC4A_fUchu1mw" keySequence="CTRL+SHIFT+O" command="_2vn3GEQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v6yDUQHEfC4A_fUchu1mw" keySequence="F3" command="_2voeGkQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v7ZHEQHEfC4A_fUchu1mw" keySequence="CTRL+SHIFT+G" command="_2vn3aEQHEfC4A_fUchu1mw"/>
  </bindingTables>
  <bindingTables xmi:id="_2v2gjkQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.ui.macroExpansionHoverScope" bindingContext="_2vqTWEQHEfC4A_fUchu1mw">
    <bindings xmi:id="_2v2gj0QHEfC4A_fUchu1mw" keySequence="ALT+ARROW_LEFT" command="_2vla40QHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v4VuEQHEfC4A_fUchu1mw" keySequence="ALT+ARROW_RIGHT" command="_2vn3MUQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v6yDEQHEfC4A_fUchu1mw" keySequence="F3" command="_2vn3EkQHEfC4A_fUchu1mw"/>
  </bindingTables>
  <bindingTables xmi:id="_2v2glEQHEfC4A_fUchu1mw" elementId="org.omnetpp.context.EventLogTable" bindingContext="_2vqTUkQHEfC4A_fUchu1mw">
    <bindings xmi:id="_2v2glUQHEfC4A_fUchu1mw" keySequence="CTRL+ARROW_UP" command="_2vla2UQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v2glkQHEfC4A_fUchu1mw" keySequence="SHIFT+ARROW_DOWN" command="_2vlaxEQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v4VsUQHEfC4A_fUchu1mw" keySequence="ALT+ARROW_DOWN" command="_2voeO0QHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v4VskQHEfC4A_fUchu1mw" keySequence="SHIFT+ARROW_UP" command="_2vn3JkQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v4VvEQHEfC4A_fUchu1mw" keySequence="CTRL+ARROW_DOWN" command="_2voekUQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v48w0QHEfC4A_fUchu1mw" keySequence="F5" command="_2vmo-UQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v5j6EQHEfC4A_fUchu1mw" keySequence="CTRL+K" command="_2voecEQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v7ZAkQHEfC4A_fUchu1mw" keySequence="CTRL+F" command="_2vmB_0QHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v7ZEEQHEfC4A_fUchu1mw" keySequence="CTRL+G" command="_2voeGUQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v7ZGEQHEfC4A_fUchu1mw" keySequence="ALT+ARROW_UP" command="_2voebUQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v7ZG0QHEfC4A_fUchu1mw" keySequence="CTRL+SHIFT+G" command="_2voedkQHEfC4A_fUchu1mw"/>
  </bindingTables>
  <bindingTables xmi:id="_2v3uq0QHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.ui.cViewScope" bindingContext="_2vqTREQHEfC4A_fUchu1mw">
    <bindings xmi:id="_2v3urEQHEfC4A_fUchu1mw" keySequence="ALT+CTRL+I" command="_2voemEQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v6yAEQHEfC4A_fUchu1mw" keySequence="ALT+SHIFT+R" command="_2voedUQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v6yBkQHEfC4A_fUchu1mw" keySequence="F4" command="_2vn3Q0QHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v6yCEQHEfC4A_fUchu1mw" keySequence="CTRL+SHIFT+T" command="_2vn3TEQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v6yC0QHEfC4A_fUchu1mw" keySequence="F3" command="_2vn3EkQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v7ZDkQHEfC4A_fUchu1mw" keySequence="CTRL+G" command="_2vn3XEQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v7ZGkQHEfC4A_fUchu1mw" keySequence="CTRL+SHIFT+G" command="_2vn3KkQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v8AFUQHEfC4A_fUchu1mw" keySequence="ALT+CTRL+H" command="_2voeFEQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v8AF0QHEfC4A_fUchu1mw" keySequence="CTRL+SHIFT+H" command="_2vmo8kQHEfC4A_fUchu1mw"/>
  </bindingTables>
  <bindingTables xmi:id="_2v4VxUQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.RepositoriesView" bindingContext="_2vqTQkQHEfC4A_fUchu1mw">
    <bindings xmi:id="_2v4VxkQHEfC4A_fUchu1mw" keySequence="CTRL+V" command="_2vmB80QHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v7ZBEQHEfC4A_fUchu1mw" keySequence="CTRL+C" command="_2vmB9kQHEfC4A_fUchu1mw"/>
  </bindingTables>
  <bindingTables xmi:id="_2v4VykQHEfC4A_fUchu1mw" elementId="org.eclipse.debug.ui.console" bindingContext="_2vqTSkQHEfC4A_fUchu1mw">
    <bindings xmi:id="_2v4Vy0QHEfC4A_fUchu1mw" keySequence="CTRL+Z" command="_2vnP-kQHEfC4A_fUchu1mw">
      <tags>platform:win32</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_2v48wEQHEfC4A_fUchu1mw" elementId="org.eclipse.debug.ui.debugging" bindingContext="_2vqTRUQHEfC4A_fUchu1mw">
    <bindings xmi:id="_2v48wUQHEfC4A_fUchu1mw" keySequence="F5" command="_2vn3WEQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v5j0EQHEfC4A_fUchu1mw" keySequence="CTRL+R" command="_2vmpHUQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v5j00QHEfC4A_fUchu1mw" keySequence="CTRL+F2" command="_2vnQEEQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v5j1EQHEfC4A_fUchu1mw" keySequence="F8" command="_2voen0QHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v5j1UQHEfC4A_fUchu1mw" keySequence="F7" command="_2voef0QHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v5j1kQHEfC4A_fUchu1mw" keySequence="F6" command="_2vla4kQHEfC4A_fUchu1mw"/>
  </bindingTables>
  <bindingTables xmi:id="_2v48xEQHEfC4A_fUchu1mw" elementId="org.omnetpp.context.SequenceChart" bindingContext="_2vqTVEQHEfC4A_fUchu1mw">
    <bindings xmi:id="_2v48xUQHEfC4A_fUchu1mw" keySequence="F5" command="_2voefEQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v5j6UQHEfC4A_fUchu1mw" keySequence="CTRL+K" command="_2vla3kQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v7ZA0QHEfC4A_fUchu1mw" keySequence="CTRL+F" command="_2vnP8EQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v7ZEUQHEfC4A_fUchu1mw" keySequence="CTRL+G" command="_2voeHUQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v7ZHUQHEfC4A_fUchu1mw" keySequence="CTRL+SHIFT+G" command="_2vmCBUQHEfC4A_fUchu1mw"/>
  </bindingTables>
  <bindingTables xmi:id="_2v48zUQHEfC4A_fUchu1mw" elementId="org.eclipse.debug.ui.memoryview" bindingContext="_2vqTUUQHEfC4A_fUchu1mw">
    <bindings xmi:id="_2v48zkQHEfC4A_fUchu1mw" keySequence="CTRL+W" command="_2vnQHUQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v480EQHEfC4A_fUchu1mw" keySequence="CTRL+N" command="_2vnQTEQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v481kQHEfC4A_fUchu1mw" keySequence="ALT+CTRL+N" command="_2vmB30QHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v482EQHEfC4A_fUchu1mw" keySequence="CTRL+T" command="_2vn3QUQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v482UQHEfC4A_fUchu1mw" keySequence="ALT+CTRL+M" command="_2vla6EQHEfC4A_fUchu1mw"/>
  </bindingTables>
  <bindingTables xmi:id="_2v4800QHEfC4A_fUchu1mw" elementId="org.eclipse.debug.ui.memory.abstractasynctablerendering" bindingContext="_2vqTR0QHEfC4A_fUchu1mw">
    <bindings xmi:id="_2v481EQHEfC4A_fUchu1mw" keySequence="CTRL+SHIFT+," command="_2voeZkQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v481UQHEfC4A_fUchu1mw" keySequence="CTRL+SHIFT+." command="_2vkzsEQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v7ZD0QHEfC4A_fUchu1mw" keySequence="CTRL+G" command="_2vnQFEQHEfC4A_fUchu1mw"/>
  </bindingTables>
  <bindingTables xmi:id="_2v5j2UQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.make.ui.makefileEditorScope" bindingContext="_2vqTTUQHEfC4A_fUchu1mw">
    <bindings xmi:id="_2v5j2kQHEfC4A_fUchu1mw" keySequence="CTRL+/" command="_2voeXUQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v6yCUQHEfC4A_fUchu1mw" keySequence="F3" command="_2voemUQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v8AEUQHEfC4A_fUchu1mw" keySequence="CTRL+\" command="_2vnQFUQHEfC4A_fUchu1mw"/>
  </bindingTables>
  <bindingTables xmi:id="_2v5j3EQHEfC4A_fUchu1mw" elementId="org.omnetpp.context.inifileEditor" bindingContext="_2vqTTEQHEfC4A_fUchu1mw">
    <bindings xmi:id="_2v5j3UQHEfC4A_fUchu1mw" keySequence="CTRL+/" command="_2vmCBkQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v5j60QHEfC4A_fUchu1mw" keySequence="CTRL+SHIFT+O" command="_2vnQB0QHEfC4A_fUchu1mw"/>
  </bindingTables>
  <bindingTables xmi:id="_2v5j4UQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.debug.ui.debugging" bindingContext="_2vqTSUQHEfC4A_fUchu1mw">
    <bindings xmi:id="_2v5j4kQHEfC4A_fUchu1mw" keySequence="SHIFT+F8" command="_2vn3OEQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v5j40QHEfC4A_fUchu1mw" keySequence="SHIFT+F6" command="_2vmpJ0QHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v5j5EQHEfC4A_fUchu1mw" keySequence="SHIFT+F7" command="_2vmo50QHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v5j5UQHEfC4A_fUchu1mw" keySequence="CTRL+F5" command="_2vn3UUQHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v6x-kQHEfC4A_fUchu1mw" keySequence="SHIFT+F5" command="_2vmB-EQHEfC4A_fUchu1mw"/>
  </bindingTables>
  <bindingTables xmi:id="_2v5j8UQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.context" bindingContext="_2vqTRkQHEfC4A_fUchu1mw">
    <bindings xmi:id="_2v6K4EQHEfC4A_fUchu1mw" keySequence="HOME" command="_2vmCH0QHEfC4A_fUchu1mw"/>
    <bindings xmi:id="_2v7ZDEQHEfC4A_fUchu1mw" keySequence="CTRL+G" command="_2vlaw0QHEfC4A_fUchu1mw"/>
  </bindingTables>
  <bindingTables xmi:id="_2v7ZBUQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.ReflogView" bindingContext="_2vqTQ0QHEfC4A_fUchu1mw">
    <bindings xmi:id="_2v7ZBkQHEfC4A_fUchu1mw" keySequence="CTRL+C" command="_2vmpEUQHEfC4A_fUchu1mw"/>
  </bindingTables>
  <bindingTables xmi:id="_27BOskQHEfC4A_fUchu1mw" bindingContext="_27BOsUQHEfC4A_fUchu1mw"/>
  <bindingTables xmi:id="_27B1wUQHEfC4A_fUchu1mw" bindingContext="_27B1wEQHEfC4A_fUchu1mw"/>
  <bindingTables xmi:id="_27B1w0QHEfC4A_fUchu1mw" bindingContext="_27B1wkQHEfC4A_fUchu1mw"/>
  <bindingTables xmi:id="_27B1xUQHEfC4A_fUchu1mw" bindingContext="_27B1xEQHEfC4A_fUchu1mw"/>
  <bindingTables xmi:id="_27B1x0QHEfC4A_fUchu1mw" bindingContext="_27B1xkQHEfC4A_fUchu1mw"/>
  <bindingTables xmi:id="_27B1yUQHEfC4A_fUchu1mw" bindingContext="_27B1yEQHEfC4A_fUchu1mw"/>
  <bindingTables xmi:id="_27B1y0QHEfC4A_fUchu1mw" bindingContext="_27B1ykQHEfC4A_fUchu1mw"/>
  <bindingTables xmi:id="_27B1zUQHEfC4A_fUchu1mw" bindingContext="_27B1zEQHEfC4A_fUchu1mw"/>
  <bindingTables xmi:id="_27B1z0QHEfC4A_fUchu1mw" bindingContext="_27B1zkQHEfC4A_fUchu1mw"/>
  <bindingTables xmi:id="_27B10UQHEfC4A_fUchu1mw" bindingContext="_27B10EQHEfC4A_fUchu1mw"/>
  <bindingTables xmi:id="_27B100QHEfC4A_fUchu1mw" bindingContext="_27B10kQHEfC4A_fUchu1mw"/>
  <bindingTables xmi:id="_27B11UQHEfC4A_fUchu1mw" bindingContext="_27B11EQHEfC4A_fUchu1mw"/>
  <bindingTables xmi:id="_27Cc0UQHEfC4A_fUchu1mw" bindingContext="_27Cc0EQHEfC4A_fUchu1mw"/>
  <bindingTables xmi:id="_27Cc00QHEfC4A_fUchu1mw" bindingContext="_27Cc0kQHEfC4A_fUchu1mw"/>
  <bindingTables xmi:id="_27Cc1UQHEfC4A_fUchu1mw" bindingContext="_27Cc1EQHEfC4A_fUchu1mw"/>
  <bindingTables xmi:id="_27Cc10QHEfC4A_fUchu1mw" bindingContext="_27Cc1kQHEfC4A_fUchu1mw"/>
  <bindingTables xmi:id="_27Cc2UQHEfC4A_fUchu1mw" bindingContext="_27Cc2EQHEfC4A_fUchu1mw"/>
  <bindingTables xmi:id="_27Cc20QHEfC4A_fUchu1mw" bindingContext="_27Cc2kQHEfC4A_fUchu1mw"/>
  <bindingTables xmi:id="_27Cc3UQHEfC4A_fUchu1mw" bindingContext="_27Cc3EQHEfC4A_fUchu1mw"/>
  <bindingTables xmi:id="_27Cc30QHEfC4A_fUchu1mw" bindingContext="_27Cc3kQHEfC4A_fUchu1mw"/>
  <bindingTables xmi:id="_27Cc4UQHEfC4A_fUchu1mw" bindingContext="_27Cc4EQHEfC4A_fUchu1mw"/>
  <bindingTables xmi:id="_27Cc40QHEfC4A_fUchu1mw" bindingContext="_27Cc4kQHEfC4A_fUchu1mw"/>
  <bindingTables xmi:id="_27Cc5UQHEfC4A_fUchu1mw" bindingContext="_27Cc5EQHEfC4A_fUchu1mw"/>
  <bindingTables xmi:id="_27DD4UQHEfC4A_fUchu1mw" bindingContext="_27DD4EQHEfC4A_fUchu1mw"/>
  <bindingTables xmi:id="_27DD40QHEfC4A_fUchu1mw" bindingContext="_27DD4kQHEfC4A_fUchu1mw"/>
  <bindingTables xmi:id="_27DD5UQHEfC4A_fUchu1mw" bindingContext="_27DD5EQHEfC4A_fUchu1mw"/>
  <bindingTables xmi:id="_27DD50QHEfC4A_fUchu1mw" bindingContext="_27DD5kQHEfC4A_fUchu1mw"/>
  <bindingTables xmi:id="_27DD6UQHEfC4A_fUchu1mw" bindingContext="_27DD6EQHEfC4A_fUchu1mw"/>
  <bindingTables xmi:id="_27DD60QHEfC4A_fUchu1mw" bindingContext="_27DD6kQHEfC4A_fUchu1mw"/>
  <bindingTables xmi:id="_27DD7UQHEfC4A_fUchu1mw" bindingContext="_27DD7EQHEfC4A_fUchu1mw"/>
  <bindingTables xmi:id="_27DD70QHEfC4A_fUchu1mw" bindingContext="_27DD7kQHEfC4A_fUchu1mw"/>
  <bindingTables xmi:id="_27DD8UQHEfC4A_fUchu1mw" bindingContext="_27DD8EQHEfC4A_fUchu1mw"/>
  <bindingTables xmi:id="_27DD80QHEfC4A_fUchu1mw" bindingContext="_27DD8kQHEfC4A_fUchu1mw"/>
  <bindingTables xmi:id="_27DD9UQHEfC4A_fUchu1mw" bindingContext="_27DD9EQHEfC4A_fUchu1mw"/>
  <rootContext xmi:id="_2vUVCEQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.contexts.dialogAndWindow" contributorURI="platform:/plugin/org.eclipse.ui.workbench" name="In Dialogs and Windows" description="Either a dialog or a window is open">
    <children xmi:id="_2vUVCUQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.contexts.window" contributorURI="platform:/plugin/org.eclipse.ui.workbench" name="In Windows" description="A window is open">
      <children xmi:id="_2vUVCkQHEfC4A_fUchu1mw" elementId="org.eclipse.e4.ui.contexts.views" contributorURI="platform:/plugin/org.eclipse.ui.workbench" name="%bindingcontext.name.bindingView"/>
      <children xmi:id="_2vqTQEQHEfC4A_fUchu1mw" elementId="org.eclipse.compare.compareEditorScope" name="Comparing in an Editor" description="Comparing in an Editor"/>
      <children xmi:id="_2vqTQkQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.RepositoriesView" name="In Git Repositories View"/>
      <children xmi:id="_2vqTQ0QHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.ReflogView" name="In Git Reflog View"/>
      <children xmi:id="_2vqTREQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.ui.cViewScope" name="In C/C++ Views" description="In C/C++ Views"/>
      <children xmi:id="_2vqTRUQHEfC4A_fUchu1mw" elementId="org.eclipse.debug.ui.debugging" name="Debugging" description="Debugging programs">
        <children xmi:id="_2vqTRkQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.context" name="In Disassembly" description="When debugging in assembly mode"/>
        <children xmi:id="_2vqTR0QHEfC4A_fUchu1mw" elementId="org.eclipse.debug.ui.memory.abstractasynctablerendering" name="In Table Memory Rendering" description="In Table Memory Rendering"/>
        <children xmi:id="_2vqTSUQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.debug.ui.debugging" name="Debugging C/C++" description="Debugging C/C++ Programs"/>
      </children>
      <children xmi:id="_2vqTSEQHEfC4A_fUchu1mw" elementId="org.eclipse.debug.ui.BreakpointView" name="In Breakpoints View" description="The breakpoints view context"/>
      <children xmi:id="_2vqTSkQHEfC4A_fUchu1mw" elementId="org.eclipse.debug.ui.console" name="In I/O Console" description="In I/O console"/>
      <children xmi:id="_2vqTS0QHEfC4A_fUchu1mw" elementId="org.eclipse.ui.textEditorScope" name="Editing Text" description="Editing Text Context">
        <children xmi:id="_2vqTTEQHEfC4A_fUchu1mw" elementId="org.omnetpp.context.inifileEditor" name="Editing INI File" description="OMNeT++ INI File Editor"/>
        <children xmi:id="_2vqTTUQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.make.ui.makefileEditorScope" name="Makefile Editor" description="Editor for makefiles"/>
        <children xmi:id="_2vqTTkQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.ui.cEditorScope" name="C/C++ Editor" description="Editor for C/C++ Source Files"/>
        <children xmi:id="_2vqTT0QHEfC4A_fUchu1mw" elementId="org.omnetpp.context.msgEditor" name="Editing MSG Source" description="OMNeT++ MSG File Editor"/>
        <children xmi:id="_2vqTVUQHEfC4A_fUchu1mw" elementId="org.omnetpp.context.nedEditor" name="Editing NED File" description="OMNeT++ NED Editor">
          <children xmi:id="_2vqTVkQHEfC4A_fUchu1mw" elementId="org.omnetpp.context.nedGraphEditor" name="Editing NED Graphically" description="OMNeT++ NED Graphical Editor"/>
          <children xmi:id="_2vqTV0QHEfC4A_fUchu1mw" elementId="org.omnetpp.context.nedTextEditor" name="Editing NED Source" description="OMNeT++ NED Source Editor"/>
        </children>
      </children>
      <children xmi:id="_2vqTUUQHEfC4A_fUchu1mw" elementId="org.eclipse.debug.ui.memoryview" name="In Memory View" description="In memory view"/>
      <children xmi:id="_2vqTUkQHEfC4A_fUchu1mw" elementId="org.omnetpp.context.EventLogTable" name="EventLogTableContext" description="OMNeT++ Eventlog Table"/>
      <children xmi:id="_2vqTU0QHEfC4A_fUchu1mw" elementId="org.eclipse.ui.console.ConsoleView" name="In Console View" description="In Console View"/>
      <children xmi:id="_2vqTVEQHEfC4A_fUchu1mw" elementId="org.omnetpp.context.SequenceChart" name="Viewing a Sequence Chart" description="OMNeT++ Sequence Chart"/>
    </children>
    <children xmi:id="_2vUVC0QHEfC4A_fUchu1mw" elementId="org.eclipse.ui.contexts.dialog" contributorURI="platform:/plugin/org.eclipse.ui.workbench" name="In Dialogs" description="A dialog is open"/>
    <children xmi:id="_2vqTWEQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.ui.macroExpansionHoverScope" name="In Macro Expansion Hover" description="In Macro Expansion Hover"/>
  </rootContext>
  <rootContext xmi:id="_2vqTQUQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.contexts.workbenchMenu" name="Workbench Menu" description="When no Workbench windows are active"/>
  <rootContext xmi:id="_2vqTUEQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.contexts.actionSet" name="Action Set" description="Parent context for action sets"/>
  <rootContext xmi:id="_27BOsUQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.debug.ui.debugActionSet" name="Auto::org.eclipse.cdt.debug.ui.debugActionSet"/>
  <rootContext xmi:id="_27B1wEQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.debug.ui.reverseDebuggingActionSet" name="Auto::org.eclipse.cdt.debug.ui.reverseDebuggingActionSet"/>
  <rootContext xmi:id="_27B1wkQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.debug.ui.tracepointActionSet" name="Auto::org.eclipse.cdt.debug.ui.tracepointActionSet"/>
  <rootContext xmi:id="_27B1xEQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.debug.ui.debugViewLayoutActionSet" name="Auto::org.eclipse.cdt.debug.ui.debugViewLayoutActionSet"/>
  <rootContext xmi:id="_27B1xkQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.dsf.debug.ui.updateModes" name="Auto::org.eclipse.cdt.dsf.debug.ui.updateModes"/>
  <rootContext xmi:id="_27B1yEQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.make.ui.updateActionSet" name="Auto::org.eclipse.cdt.make.ui.updateActionSet"/>
  <rootContext xmi:id="_27B1ykQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.make.ui.makeTargetActionSet" name="Auto::org.eclipse.cdt.make.ui.makeTargetActionSet"/>
  <rootContext xmi:id="_27B1zEQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.ui.CodingActionSet" name="Auto::org.eclipse.cdt.ui.CodingActionSet"/>
  <rootContext xmi:id="_27B1zkQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.ui.SearchActionSet" name="Auto::org.eclipse.cdt.ui.SearchActionSet"/>
  <rootContext xmi:id="_27B10EQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.ui.NavigationActionSet" name="Auto::org.eclipse.cdt.ui.NavigationActionSet"/>
  <rootContext xmi:id="_27B10kQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.ui.OpenActionSet" name="Auto::org.eclipse.cdt.ui.OpenActionSet"/>
  <rootContext xmi:id="_27B11EQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.ui.buildConfigActionSet" name="Auto::org.eclipse.cdt.ui.buildConfigActionSet"/>
  <rootContext xmi:id="_27Cc0EQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.ui.CElementCreationActionSet" name="Auto::org.eclipse.cdt.ui.CElementCreationActionSet"/>
  <rootContext xmi:id="_27Cc0kQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.ui.text.c.actionSet.presentation" name="Auto::org.eclipse.cdt.ui.text.c.actionSet.presentation"/>
  <rootContext xmi:id="_27Cc1EQHEfC4A_fUchu1mw" elementId="org.eclipse.debug.ui.breakpointActionSet" name="Auto::org.eclipse.debug.ui.breakpointActionSet"/>
  <rootContext xmi:id="_27Cc1kQHEfC4A_fUchu1mw" elementId="org.eclipse.debug.ui.debugActionSet" name="Auto::org.eclipse.debug.ui.debugActionSet"/>
  <rootContext xmi:id="_27Cc2EQHEfC4A_fUchu1mw" elementId="org.eclipse.debug.ui.launchActionSet" name="Auto::org.eclipse.debug.ui.launchActionSet"/>
  <rootContext xmi:id="_27Cc2kQHEfC4A_fUchu1mw" elementId="org.eclipse.debug.ui.profileActionSet" name="Auto::org.eclipse.debug.ui.profileActionSet"/>
  <rootContext xmi:id="_27Cc3EQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.gitaction" name="Auto::org.eclipse.egit.ui.gitaction"/>
  <rootContext xmi:id="_27Cc3kQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.navigation" name="Auto::org.eclipse.egit.ui.navigation"/>
  <rootContext xmi:id="_27Cc4EQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.cheatsheets.actionSet" name="Auto::org.eclipse.ui.cheatsheets.actionSet"/>
  <rootContext xmi:id="_27Cc4kQHEfC4A_fUchu1mw" elementId="org.eclipse.search.searchActionSet" name="Auto::org.eclipse.search.searchActionSet"/>
  <rootContext xmi:id="_27Cc5EQHEfC4A_fUchu1mw" elementId="org.eclipse.team.ui.actionSet" name="Auto::org.eclipse.team.ui.actionSet"/>
  <rootContext xmi:id="_27DD4EQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.edit.text.actionSet.annotationNavigation" name="Auto::org.eclipse.ui.edit.text.actionSet.annotationNavigation"/>
  <rootContext xmi:id="_27DD4kQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.edit.text.actionSet.navigation" name="Auto::org.eclipse.ui.edit.text.actionSet.navigation"/>
  <rootContext xmi:id="_27DD5EQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo" name="Auto::org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo"/>
  <rootContext xmi:id="_27DD5kQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.externaltools.ExternalToolsSet" name="Auto::org.eclipse.ui.externaltools.ExternalToolsSet"/>
  <rootContext xmi:id="_27DD6EQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.NavigateActionSet" name="Auto::org.eclipse.ui.NavigateActionSet"/>
  <rootContext xmi:id="_27DD6kQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.actionSet.keyBindings" name="Auto::org.eclipse.ui.actionSet.keyBindings"/>
  <rootContext xmi:id="_27DD7EQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.WorkingSetModificationActionSet" name="Auto::org.eclipse.ui.WorkingSetModificationActionSet"/>
  <rootContext xmi:id="_27DD7kQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.WorkingSetActionSet" name="Auto::org.eclipse.ui.WorkingSetActionSet"/>
  <rootContext xmi:id="_27DD8EQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.actionSet.openFiles" name="Auto::org.eclipse.ui.actionSet.openFiles"/>
  <rootContext xmi:id="_27DD8kQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.edit.text.actionSet.presentation" name="Auto::org.eclipse.ui.edit.text.actionSet.presentation"/>
  <rootContext xmi:id="_27DD9EQHEfC4A_fUchu1mw" elementId="org.omnetpp.ned.ActionSet" name="Auto::org.omnetpp.ned.ActionSet"/>
  <descriptors xmi:id="_2w_wAEQHEfC4A_fUchu1mw" elementId="org.eclipse.e4.ui.compatibility.editor" allowMultiple="true" category="org.eclipse.e4.primaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor">
    <tags>Editor</tags>
  </descriptors>
  <descriptors xmi:id="_2xBlMEQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.codan.internal.ui.views.ProblemDetails" label="Problem Details" iconURI="platform:/plugin/org.eclipse.cdt.codan.ui/icons/edit_bug.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:&amp;C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_2xF2oEQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.debug.ui.executablesView" label="Executables" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui/icons/obj16/exec_view_obj.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_2xGdsEQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.debug.ui.SignalsView" label="Signals" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui/icons/view16/signals_view.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_2xHEwEQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.dsf.gdb.ui.tracecontrol.view" label="Trace Control" iconURI="platform:/plugin/org.eclipse.cdt.dsf.gdb.ui/icons/full/view16/tracecontrol_view.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_2xHr0EQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.dsf.gdb.ui.osresources.view" label="OS Resources" iconURI="platform:/plugin/org.eclipse.cdt.dsf.gdb.ui/icons/full/view16/osresources_view.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_2xIS4EQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.view" label="Disassembly" iconURI="platform:/plugin/org.eclipse.cdt.dsf.ui/icons/disassembly.gif" allowMultiple="true" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_2xI58EQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.make.ui.views.MakeView" label="Make Target" iconURI="platform:/plugin/org.eclipse.cdt.make.ui/icons/view16/make_target.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Make</tags>
  </descriptors>
  <descriptors xmi:id="_2xJhAEQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.ui.CView" label="C/C++ Projects" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/cview.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:&amp;C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_2xKIEEQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.ui.IndexView" label="C/C++ Index" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/types.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:&amp;C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_2xL9QEQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.ui.includeBrowser" label="Include Browser" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/includeBrowser.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:&amp;C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_2xMkUEQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.ui.callHierarchy" label="Call Hierarchy" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/call_hierarchy.gif" allowMultiple="true" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:&amp;C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_2xNLYEQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.ui.typeHierarchy" label="Type Hierarchy" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/class_hi.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:&amp;C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_2xNLYUQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.texteditor.TemplatesView" label="Templates" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/templates.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_2xNycEQHEfC4A_fUchu1mw" elementId="org.eclipse.debug.ui.DebugView" label="Debug" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/debug_view.png" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_2xOZgEQHEfC4A_fUchu1mw" elementId="org.eclipse.debug.ui.BreakpointView" label="Breakpoints" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/breakpoint_view.png" allowMultiple="true" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_2xOZgUQHEfC4A_fUchu1mw" elementId="org.eclipse.debug.ui.VariableView" label="Variables" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/variable_view.png" allowMultiple="true" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_2xPAkEQHEfC4A_fUchu1mw" elementId="org.eclipse.debug.ui.ExpressionView" label="Expressions" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/watchlist_view.png" allowMultiple="true" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_2xPAkUQHEfC4A_fUchu1mw" elementId="org.eclipse.debug.ui.RegisterView" label="Registers" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/register_view.png" allowMultiple="true" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_2xPnoEQHEfC4A_fUchu1mw" elementId="org.eclipse.debug.ui.ModuleView" label="Modules" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/module_view.png" allowMultiple="true" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_2xPnoUQHEfC4A_fUchu1mw" elementId="org.eclipse.debug.ui.MemoryView" label="Memory" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/memory_view.png" allowMultiple="true" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_2xQOsEQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.RepositoriesView" label="Git Repositories" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/repo_rep.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_2xQ1wEQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.StagingView" label="Git Staging" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/staging.png" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_2xRc0EQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.InteractiveRebaseView" label="Git Interactive Rebase" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/rebase_interactive.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_2xRc0UQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.CompareTreeView" label="Git Tree Compare" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/obj16/gitrepository.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_2xSD4EQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.ReflogView" label="Git Reflog" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/reflog.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_2xSD4UQHEfC4A_fUchu1mw" elementId="org.eclipse.gef.ui.palette_view" label="Palette" iconURI="platform:/plugin/org.eclipse.gef/icons/palette_view.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_2xSq8EQHEfC4A_fUchu1mw" elementId="org.eclipse.help.ui.HelpView" label="Help" iconURI="platform:/plugin/org.eclipse.help.ui/icons/view16/help_view.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Help</tags>
  </descriptors>
  <descriptors xmi:id="_2xTSAEQHEfC4A_fUchu1mw" elementId="org.eclipse.linuxtools.dataviewers.charts.view" label="Chart View" iconURI="platform:/plugin/org.eclipse.linuxtools.dataviewers.charts/icons/chart_icon.png" allowMultiple="true" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Charts</tags>
  </descriptors>
  <descriptors xmi:id="_2xUgIEQHEfC4A_fUchu1mw" elementId="org.eclipse.linuxtools.valgrind.ui.valgrindview" label="Valgrind" iconURI="platform:/plugin/org.eclipse.linuxtools.valgrind.ui/icons/valgrind-icon.png" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Profiling</tags>
  </descriptors>
  <descriptors xmi:id="_2xUgIUQHEfC4A_fUchu1mw" elementId="org.eclipse.search.SearchResultView" label="Classic Search" iconURI="platform:/plugin/org.eclipse.search/icons/full/eview16/searchres.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_2xVHMEQHEfC4A_fUchu1mw" elementId="org.eclipse.search.ui.views.SearchView" label="Search" iconURI="platform:/plugin/org.eclipse.search/icons/full/eview16/searchres.gif" allowMultiple="true" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_2xVuQEQHEfC4A_fUchu1mw" elementId="org.eclipse.team.sync.views.SynchronizeView" label="Synchronize" iconURI="platform:/plugin/org.eclipse.team.ui/icons/full/eview16/synch_synch.gif" allowMultiple="true" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Team</tags>
  </descriptors>
  <descriptors xmi:id="_2xWVUEQHEfC4A_fUchu1mw" elementId="org.eclipse.team.ui.GenericHistoryView" label="History" iconURI="platform:/plugin/org.eclipse.team.ui/icons/full/eview16/history_view.gif" allowMultiple="true" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Team</tags>
  </descriptors>
  <descriptors xmi:id="_2xWVUUQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.internal.introview" label="Welcome" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_2xW8YEQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.browser.view" label="Internal Web Browser" iconURI="platform:/plugin/org.eclipse.ui.browser/icons/obj16/internal_browser.gif" allowMultiple="true" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_2xW8YUQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" label="Cheat Sheets" iconURI="platform:/plugin/org.eclipse.ui.cheatsheets/icons/view16/cheatsheet_view.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Help</tags>
  </descriptors>
  <descriptors xmi:id="_2xXjcEQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.console.ConsoleView" label="Console" iconURI="platform:/plugin/org.eclipse.ui.console/icons/full/cview16/console_view.png" allowMultiple="true" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_2xYKgEQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.views.ProgressView" label="Progress" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/pview.png" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_2xYxkEQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.views.ResourceNavigator" label="Navigator" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/filenav_nav.png" category="org.eclipse.e4.primaryNavigationStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_2xYxkUQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.views.BookmarkView" label="Bookmarks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/bkmrk_nav.png" allowMultiple="true" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_2xYxkkQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.views.TaskList" label="Tasks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/tasks_tsk.png" allowMultiple="true" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_2xZYoEQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.views.ProblemView" label="Problems" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" allowMultiple="true" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_2xZYoUQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.views.AllMarkersView" label="Markers" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" allowMultiple="true" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_2xZ_sEQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.navigator.ProjectExplorer" label="Project Explorer" iconURI="platform:/plugin/org.eclipse.ui.navigator.resources/icons/full/eview16/resource_persp.gif" category="org.eclipse.e4.primaryNavigationStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_2xamwEQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.views.PropertySheet" label="Properties" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/prop_ps.png" allowMultiple="true" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_2xamwUQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.views.ContentOutline" label="Outline" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/outline_co.png" category="org.eclipse.e4.secondaryNavigationStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_2xbN0EQHEfC4A_fUchu1mw" elementId="org.omnetpp.eventlogtable.editors.EventLogTableView" label="Event Log" iconURI="platform:/plugin/org.omnetpp.eventlogtable/icons/eventlog.png" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:OMNeT++</tags>
  </descriptors>
  <descriptors xmi:id="_2xb04EQHEfC4A_fUchu1mw" elementId="org.omnetpp.inifile.ModuleHierarchy" label="Module Hierarchy" iconURI="platform:/plugin/org.omnetpp.inifile.editor/icons/full/eview16/modulehierarchy.png" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:OMNeT++</tags>
  </descriptors>
  <descriptors xmi:id="_2xb04UQHEfC4A_fUchu1mw" elementId="org.omnetpp.inifile.ModuleParameters" label="NED Parameters" iconURI="platform:/plugin/org.omnetpp.inifile.editor/icons/full/eview16/moduleparameters.png" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:OMNeT++</tags>
  </descriptors>
  <descriptors xmi:id="_2xcb8EQHEfC4A_fUchu1mw" elementId="org.omnetpp.inifile.NedInheritance" label="NED Inheritance" iconURI="platform:/plugin/org.omnetpp.inifile.editor/icons/full/eview16/inheritance.png" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:OMNeT++</tags>
  </descriptors>
  <descriptors xmi:id="_2xcb8UQHEfC4A_fUchu1mw" elementId="org.omnetpp.main.NewVersionView" label="New OMNeT++ Version" iconURI="platform:/plugin/org.omnetpp.main/icons/logo16.png" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:OMNeT++</tags>
  </descriptors>
  <descriptors xmi:id="_2xdDAEQHEfC4A_fUchu1mw" elementId="org.omnetpp.scave.VectorBrowserView" label="Output Vector" iconURI="platform:/plugin/org.omnetpp.scave/icons/full/eview16/outvector.png" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:OMNeT++</tags>
  </descriptors>
  <descriptors xmi:id="_2xdDAUQHEfC4A_fUchu1mw" elementId="org.omnetpp.scave.DatasetView" label="Dataset" iconURI="platform:/plugin/org.omnetpp.scave/icons/full/eview16/dataset.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:OMNeT++</tags>
  </descriptors>
  <descriptors xmi:id="_2xdqEEQHEfC4A_fUchu1mw" elementId="org.omnetpp.sequencechart.editors.SequenceChartView" label="Sequence Chart" iconURI="platform:/plugin/org.omnetpp.sequencechart/icons/full/eview16/sequencechart.png" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:OMNeT++</tags>
  </descriptors>
  <descriptors xmi:id="_2xeRIEQHEfC4A_fUchu1mw" elementId="com.swtworkbench.community.xswt.editor.views.XSWTPreview" label="XSWT Preview" iconURI="platform:/plugin/org.swtworkbench.xswt/icons/xswt.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:XSWT</tags>
  </descriptors>
  <trimContributions xmi:id="_2r10UF9tEeO-yojH_y4TJA" elementId="org.eclipse.ui.ide.application.trimcontribution.QuickAccess" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" toBeRendered="false" parentId="org.eclipse.ui.main.toolbar" positionInParent="last">
    <children xsi:type="menu:ToolControl" xmi:id="_76uUAF9tEeO-yojH_y4TJA" elementId="Spacer Glue" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
      <tags>glue</tags>
      <tags>move_after:PerspectiveSpacer</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
    <children xsi:type="menu:ToolControl" xmi:id="_8tJPcF9tEeO-yojH_y4TJA" elementId="SearchField" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.quickaccess.SearchField">
      <tags>move_after:Spacer Glue</tags>
      <tags>HIDEABLE</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
    <children xsi:type="menu:ToolControl" xmi:id="_9LgmcF9tEeO-yojH_y4TJA" elementId="Search-PS Glue" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
      <tags>glue</tags>
      <tags>move_after:SearchField</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
  </trimContributions>
  <commands xmi:id="_2vkzsEQHEfC4A_fUchu1mw" elementId="org.eclipse.debug.ui.command.nextpage" commandName="Next Page of Memory" description="Load next page of memory" category="_2vkMqUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vlawEQHEfC4A_fUchu1mw" elementId="org.omnetpp.main.commands.reportBug" commandName="Report Bug or Enhancement..." description="Report a bug or enhancement in the OMNeT++ bugtracker" category="_2vkMt0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vlawUQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.ui.search.findrefs.project" commandName="References in Project" description="Search for references to the selected element in the enclosing project" category="_2vkMs0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vlawkQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.edit.text.removeTrailingWhitespace" commandName="Remove Trailing Whitespace" description="Removes the trailing whitespace of each line" category="_2vkMqEQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vlaw0QHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.commands.gotoAddress" commandName="Go to Address..." description="Navigate to address" category="_2vkMqUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vlaxEQHEfC4A_fUchu1mw" elementId="org.omnetpp.eventlogtable.gotoNextModuleEvent" commandName="Go to Next Module Event" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vlaxUQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.edit.text.showChangeRulerInformation" commandName="Show Quick Diff Ruler Tooltip" description="Displays quick diff or revision information for the caret line in a focused hover" category="_2vkMrkQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vlaxkQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.RepositoriesViewClone" commandName="Clone a Git Repository" category="_2vkMq0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vlax0QHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.ui.edit.open.quick.type.hierarchy" commandName="Quick Type Hierarchy" description="Shows quick type hierarchy" category="_2vkMs0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vlayEQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.edit.text.select.textStart" commandName="Select Text Start" description="Select to the beginning of the text" category="_2vkMrkQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vlayUQHEfC4A_fUchu1mw" elementId="org.omnetpp.cdt.gotoCppDefinitionForNedType" commandName="Open C++ Definition" description="Opens the C++ definition for a NED type (simple module or channel)" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vlaykQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.debug.ui.command.groupDebugContexts" commandName="Group" description="Groups the selected debug contexts" category="_2vkMrUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vlay0QHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.RepositoriesViewRefresh" commandName="Refresh" category="_2vkMq0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vlazEQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.cheatsheets.openCheatSheetURL" commandName="Open Cheat Sheet from URL" description="Open a Cheat Sheet from file at a specified URL." category="_2vkMt0QHEfC4A_fUchu1mw">
    <parameters xmi:id="_2vlazUQHEfC4A_fUchu1mw" elementId="cheatSheetId" name="Identifier" optional="false"/>
    <parameters xmi:id="_2vlazkQHEfC4A_fUchu1mw" elementId="name" name="Name" optional="false"/>
    <parameters xmi:id="_2vlaz0QHEfC4A_fUchu1mw" elementId="url" name="URL" optional="false"/>
  </commands>
  <commands xmi:id="_2vla0EQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.project.buildAll" commandName="Build All" description="Build all projects" category="_2vkMpEQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vla0UQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.window.lockToolBar" commandName="Lock the Toolbars" description="Lock the Toolbars" category="_2vkMoUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vla0kQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.navigate.expandAll" commandName="Expand All" description="Expand the current tree" category="_2vkMu0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vla00QHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.dsf.gdb.ui.command.selectNextTraceRecord" commandName="Next Trace Record" description="Select Next Trace Record" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vla1EQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.file.import" commandName="Import" description="Import" category="_2vkMqEQHEfC4A_fUchu1mw">
    <parameters xmi:id="_2vla1UQHEfC4A_fUchu1mw" elementId="importWizardId" name="Import Wizard"/>
  </commands>
  <commands xmi:id="_2vla1kQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.edit.text.select.lineEnd" commandName="Select Line End" description="Select to the end of the line of text" category="_2vkMrkQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vla10QHEfC4A_fUchu1mw" elementId="org.eclipse.ui.help.helpSearch" commandName="Help Search" description="Open the help search" category="_2vkMt0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vla2EQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.team.CompareWithHead" commandName="Compare with HEAD Revision" category="_2vkMq0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vla2UQHEfC4A_fUchu1mw" elementId="org.omnetpp.eventlogtable.gotoEventCause" commandName="Go to Event Cause" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vla2kQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.edit.text.smartEnterInverse" commandName="Insert Line Above Current Line" description="Adds a new line above the current line" category="_2vkMrkQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vla20QHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.ui.refactor.extract.local.variable" commandName="Extract Local Variable - Refactoring " description="Extract a local variable for the selected expression" category="_2vkMo0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vla3EQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.externalTools.commands.OpenExternalToolsConfigurations" commandName="External Tools..." description="Open external tools launch configuration dialog" category="_2vkMqUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vla3UQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.edit.text.goto.windowEnd" commandName="Window End" description="Go to the end of the window" category="_2vkMrkQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vla3kQHEfC4A_fUchu1mw" elementId="org.omnetpp.sequencechart.findNext" commandName="Find Next" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vla30QHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.ui.edit.text.c.add.include" commandName="Add Include" description="Create include statement on selection" category="_2vkMs0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vla4EQHEfC4A_fUchu1mw" elementId="org.eclipse.team.ui.TeamSynchronizingPerspective" commandName="Team Synchronizing" description="Open the Team Synchronizing Perspective" category="_2vkMoEQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vla4UQHEfC4A_fUchu1mw" elementId="org.eclipse.help.ui.indexcommand" commandName="Index" description="Show Keyword Index" category="_2vkMt0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vla4kQHEfC4A_fUchu1mw" elementId="org.eclipse.debug.ui.commands.StepOver" commandName="Step Over" description="Step over" category="_2vkMqUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vla40QHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.ui.hover.backwardMacroExpansion" commandName="Back" description="Step backward in macro expansions" category="_2vkMs0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vla5EQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.RepositoriesViewOpenInEditor" commandName="Open in Editor" category="_2vkMq0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vla5UQHEfC4A_fUchu1mw" elementId="org.omnetpp.ned.cleanupNedFiles" commandName="Clean Up NED Files..." description="Clean up and organize all import statements in NED files" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vla5kQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.history.RenameBranch" commandName="Rename Branch..." category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vla50QHEfC4A_fUchu1mw" elementId="org.eclipse.linuxtools.valgrind.launch.exportCommand" commandName="Export Valgrind Log Files" description="Exports Valgrind log output to a directory" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vla6EQHEfC4A_fUchu1mw" elementId="org.eclipse.debug.ui.commands.addMemoryMonitor" commandName="Add Memory Block" description="Add memory block" category="_2vkMqUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vla6UQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.RepositoriesViewRebase" commandName="Rebase on" category="_2vkMq0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vla6kQHEfC4A_fUchu1mw" elementId="org.omnetpp.msg.editor.CorrectIndentation" commandName="Correct Indentation" description="Reindent selected lines of the MSG source" category="_2vkMrkQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vla60QHEfC4A_fUchu1mw" elementId="org.eclipse.ui.edit.revertToSaved" commandName="Revert to Saved" description="Revert to the last saved state" category="_2vkMsEQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vla7EQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.team.Rebase" commandName="Rebase on" category="_2vkMq0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vla7UQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.window.pinEditor" commandName="Pin Editor" description="Pin the current editor" category="_2vkMoUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vla7kQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.commit.Reword" commandName="Reword Commit" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vla70QHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.team.Tag" commandName="Tag" category="_2vkMq0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vla8EQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.team.ShowRepositoriesView" commandName="Show Git Repositories View" category="_2vkMq0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vla8UQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.edit.text.folding.restore" commandName="Reset Structure" description="Resets the folding structure" category="_2vkMrkQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vla8kQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.file.restartWorkbench" commandName="Restart" description="Restart the workbench" category="_2vkMqEQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmB0EQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.RepositoriesViewConfigurePush" commandName="Configure Push..." category="_2vkMq0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmB0UQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.file.export" commandName="Export" description="Export" category="_2vkMqEQHEfC4A_fUchu1mw">
    <parameters xmi:id="_2vmB0kQHEfC4A_fUchu1mw" elementId="exportWizardId" name="Export Wizard"/>
  </commands>
  <commands xmi:id="_2vmB00QHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.debug.command.breakpointProperties" commandName="C/C++ Breakpoint Properties" description="View and edit properties for a given C/C++ breakpoint" category="_2vkMqUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmB1EQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.edit.text.select.lineDown" commandName="Select Line Down" description="Extend the selection to the next line of text" category="_2vkMrkQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmB1UQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.PushHeadToGerrit" commandName="Push Current Head to Gerrit" category="_2vkMq0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmB1kQHEfC4A_fUchu1mw" elementId="org.eclipse.ltk.ui.refactoring.commands.renameResource" commandName="Rename Resource" description="Rename the selected resource and notify LTK participants." category="_2vkMukQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmB10QHEfC4A_fUchu1mw" elementId="org.eclipse.ui.window.resetPerspective" commandName="Reset Perspective" description="Reset the current perspective to its default state" category="_2vkMoUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmB2EQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.window.showContextMenu" commandName="Show Context Menu" description="Show the context menu" category="_2vkMoUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmB2UQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.team.stash.drop" commandName="Delete Stashed Commit..." category="_2vkMq0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmB2kQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.ConfigureUpstreamPush" commandName="Configure Upstream Push" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmB20QHEfC4A_fUchu1mw" elementId="org.eclipse.ui.window.savePerspective" commandName="Save Perspective As" description="Save the current perspective" category="_2vkMoUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmB3EQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.edit.text.showInformation" commandName="Show Tooltip Description" description="Displays information for the current caret location in a focused hover" category="_2vkMrkQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmB3UQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.ide.markCompleted" commandName="Mark Completed" description="Mark the selected tasks as completed" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmB3kQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.team.ConfigureFetch" commandName="Configure Upstream Fetch" category="_2vkMq0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmB30QHEfC4A_fUchu1mw" elementId="org.eclipse.debug.ui.commands.nextMemoryBlock" commandName="Next Memory Monitor" description="Show renderings from next memory monitor." category="_2vkMqUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmB4EQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.ui.refactor.extract.constant" commandName="Extract Constant - Refactoring " description="Extract a constant for the selected expression" category="_2vkMo0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmB4UQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.edit.text.toggleOverwrite" commandName="Toggle Overwrite" description="Toggle overwrite mode" category="_2vkMrkQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmB4kQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.edit.text.goto.textEnd" commandName="Text End" description="Go to the end of the text" category="_2vkMrkQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmB40QHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.history.Squash" commandName="Squash Commits" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmB5EQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.edit.delete" commandName="Delete" description="Delete the selection" category="_2vkMsEQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmB5UQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.window.showKeyAssist" commandName="Show Key Assist" description="Show the key assist dialog" category="_2vkMoUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmB5kQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.file.saveAs" commandName="Save As" description="Save the current contents to another location" category="_2vkMqEQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmB50QHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.RepositoriesViewChangeCredentials" commandName="Change Credentials" category="_2vkMq0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmB6EQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.ide.showInSystemExplorer" commandName="Show In (System Explorer)" description="Show in system's explorer (file manager)" category="_2vkMu0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmB6UQHEfC4A_fUchu1mw" elementId="org.eclipse.search.ui.performTextSearchWorkspace" commandName="Find Text in Workspace" description="Searches the files in the workspace for specific text." category="_2vkMr0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmB6kQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.ui.refactor.getters.and.setters" commandName="Generate Getters and Setters..." description="Generates getters and setters for a selected field" category="_2vkMs0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmB60QHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.ui.refactor.hide.method" commandName="Hide Member Function..." category="_2vkMo0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmB7EQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.history.CreateTag" commandName="Create Tag..." category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmB7UQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.window.openEditorDropDown" commandName="Quick Switch Editor" description="Open the editor drop down list" category="_2vkMoUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmB7kQHEfC4A_fUchu1mw" elementId="org.eclipse.debug.ui.commands.ProfileLast" commandName="Profile" description="Launch in profile mode" category="_2vkMqUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmB70QHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.ui.specific_content_assist.command" commandName="C/C++ Content Assist" description="A parameterizable command that invokes content assist with a single completion proposal category" category="_2vkMsEQHEfC4A_fUchu1mw">
    <parameters xmi:id="_2vmB8EQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.ui.specific_content_assist.category_id" name="type" optional="false"/>
  </commands>
  <commands xmi:id="_2vmB8UQHEfC4A_fUchu1mw" elementId="org.eclipse.search.ui.performTextSearchFile" commandName="Find Text in File" description="Searches the files in the file for specific text." category="_2vkMr0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmB8kQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.ui.refactor.extract.function" commandName="Extract Function - Refactoring " description="Extract a function for the selected list of expressions or statements" category="_2vkMo0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmB80QHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.RepositoriesViewPaste" commandName="Paste Repository Path or URI" category="_2vkMq0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmB9EQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.edit.paste" commandName="Paste" description="Paste from the clipboard" category="_2vkMsEQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmB9UQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.navigate.previous" commandName="Previous" description="Navigate to the previous item" category="_2vkMu0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmB9kQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.RepositoriesViewCopyPath" commandName="Copy Path to Clipboard" category="_2vkMq0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmB90QHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.team.Ignore" commandName="Ignore" category="_2vkMq0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmB-EQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.debug.ui.command.reverseStepInto" commandName="Reverse Step Into" description="Perform Reverse Step Into" category="_2vkMskQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmB-UQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.team.RemoveFromIndex" commandName="Remove from Git Index" category="_2vkMq0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmB-kQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.ui.edit.open.quick.macro.explorer" commandName="Explore Macro Expansion" description="Opens a quick view for macro expansion exploration" category="_2vkMs0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmB-0QHEfC4A_fUchu1mw" elementId="org.eclipse.ui.edit.text.goto.lineUp" commandName="Line Up" description="Go up one line of text" category="_2vkMrkQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmB_EQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.file.newQuickMenu" commandName="New menu" description="Open the New menu" category="_2vkMqEQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmB_UQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.edit.text.deleteNext" commandName="Delete Next" description="Delete the next character" category="_2vkMrkQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmB_kQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.edit.text.deleteNextWord" commandName="Delete Next Word" description="Delete the next word" category="_2vkMrkQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmB_0QHEfC4A_fUchu1mw" elementId="org.omnetpp.eventlogtable.findText" commandName="Find..." category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmCAEQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.debug.ui.command.castToType" commandName="Cast To Type..." category="_2vkMqkQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmCAUQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.edit.undo" commandName="Undo" description="Undo the last operation" category="_2vkMsEQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmCAkQHEfC4A_fUchu1mw" elementId="org.omnetpp.ned.editor.text.DistributeAllGateLabels" commandName="Distribute Gate Labels" description="Distribute gate labels to connected gates" category="_2vkMrkQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmCA0QHEfC4A_fUchu1mw" elementId="org.eclipse.debug.ui.commands.DebugLast" commandName="Debug" description="Launch in debug mode" category="_2vkMqUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmCBEQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.navigate.back" commandName="Back" description="Navigate back" category="_2vkMu0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmCBUQHEfC4A_fUchu1mw" elementId="org.omnetpp.sequencechart.gotoSimulationTime" commandName="Go to Simulation Time..." category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmCBkQHEfC4A_fUchu1mw" elementId="org.omnetpp.inifile.editor.text.ToggleComment" commandName="Toggle Comment" description="Comment/Uncomment the selected lines" category="_2vkMrkQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmCB0QHEfC4A_fUchu1mw" elementId="org.omnetpp.cdt.projectFeaturesCommand" commandName="Project &amp;Features..." category="_2vkMpEQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmCCEQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.ui.excludeCommand" commandName="Exclude from Build" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmCCUQHEfC4A_fUchu1mw" elementId="org.eclipse.debug.ui.commands.ToggleLineBreakpoint" commandName="Toggle Line Breakpoint" description="Creates or removes a line breakpoint" category="_2vkMqUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmCCkQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.editors.lineNumberToggle" commandName="Show Line Numbers" description="Toggle display of line numbers" category="_2vkMrkQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmCC0QHEfC4A_fUchu1mw" elementId="org.eclipse.debug.ui.commands.ToggleStepFilters" commandName="Use Step Filters" description="Toggles enablement of debug step filters" category="_2vkMqUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmCDEQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.RepositoriesLinkWithSelection" commandName="Link with Selection" category="_2vkMq0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmCDUQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.org.eclipse.egit.ui.AbortRebase" commandName="Abort Rebase" category="_2vkMq0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmCDkQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.team.Fetch" commandName="Fetch" category="_2vkMq0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmCD0QHEfC4A_fUchu1mw" elementId="org.eclipse.ui.window.newEditor" commandName="New Editor" description="Open another editor on the active editor's input" category="_2vkMoUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmCEEQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.history.SetQuickdiffBaseline" commandName="Set quickdiff baseline" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmCEUQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.edit.text.goto.wordPrevious" commandName="Previous Word" description="Go to the previous word" category="_2vkMrkQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmCEkQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.RepositoriesViewDelete" commandName="Delete Repository" category="_2vkMq0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmCE0QHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.history.ShowBlame" commandName="Show Annotations" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmCFEQHEfC4A_fUchu1mw" elementId="org.omnetpp.ned.editor.text.ToggleComment" commandName="Toggle Comment" description="Comment/Uncomment the selected lines" category="_2vkMrkQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmCFUQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.edit.text.recenter" commandName="Recenter" description="Scroll cursor line to center, top and bottom" category="_2vkMrkQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmCFkQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.team.Pull" commandName="Pull" category="_2vkMq0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmCF0QHEfC4A_fUchu1mw" elementId="org.eclipse.ui.help.installationDialog" commandName="Installation Information" description="Open the installation dialog" category="_2vkMt0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmCGEQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.debug.ui.command.restoreDefaultType" commandName="Restore Original Type" description="View and edit properties for a given C/C++ breakpoint" category="_2vkMqkQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmCGUQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.team.CompareWithIndex" commandName="Compare with Git Index" category="_2vkMq0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmCGkQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.edit.text.shiftRight" commandName="Shift Right" description="Shift a block of text to the right" category="_2vkMsEQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmCG0QHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.ui.edit.text.c.indent" commandName="Indent Line" description="Indents the current line" category="_2vkMs0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmCHEQHEfC4A_fUchu1mw" elementId="org.omnetpp.ned.openNedType" commandName="Open NED Type" description="Open NED Type" category="_2vkMu0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmCHUQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.file.refresh" commandName="Refresh" description="Refresh the selected items" category="_2vkMqEQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmCHkQHEfC4A_fUchu1mw" elementId="org.eclipse.gef.ui.palette_view" commandName="Palette" category="_2vkMpUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmCH0QHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.commands.gotoPC" commandName="Go to Program Counter" description="Navigate to current program counter" category="_2vkMqUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmCIEQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.history.Merge" commandName="Merge" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmCIUQHEfC4A_fUchu1mw" elementId="org.eclipse.search.ui.performTextSearchWorkingSet" commandName="Find Text in Working Set" description="Searches the files in the working set for specific text." category="_2vkMr0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmCIkQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.edit.text.delete.line.to.end" commandName="Delete to End of Line" description="Delete to the end of a line of text" category="_2vkMrkQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmCI0QHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.history.CreateBranch" commandName="Create Branch" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmCJEQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.window.nextEditor" commandName="Next Editor" description="Switch to the next editor" category="_2vkMoUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmo4EQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.team.ShowBlame" commandName="Show Annotations" category="_2vkMq0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmo4UQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.navigate.forward" commandName="Forward" description="Navigate forward" category="_2vkMu0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmo4kQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.edit.text.goto.pageUp" commandName="Page Up" description="Go up one page" category="_2vkMrkQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmo40QHEfC4A_fUchu1mw" elementId="org.eclipse.ui.file.closeAllSaved" commandName="Close All Saved" description="Close all saved editors" category="_2vkMqEQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmo5EQHEfC4A_fUchu1mw" elementId="org.omnetpp.common.installSimulationModels" commandName="Install Simulation Models..." description="Install Simulation Models" category="_2vkMt0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmo5UQHEfC4A_fUchu1mw" elementId="org.eclipse.debug.ui.commands.OpenRunConfigurations" commandName="Run..." description="Open run launch configuration dialog" category="_2vkMqUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmo5kQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.team.AssumeUnchanged" commandName="Assume Unchanged" category="_2vkMq0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmo50QHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.debug.ui.command.uncall" commandName="Uncall" description="Perform Uncall" category="_2vkMskQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmo6EQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.edit.text.deletePreviousWord" commandName="Delete Previous Word" description="Delete the previous word" category="_2vkMrkQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmo6UQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.edit.text.deletePrevious" commandName="Delete Previous" description="Delete the previous character" category="_2vkMrkQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmo6kQHEfC4A_fUchu1mw" elementId="org.eclipse.debug.ui.commands.SkipAllBreakpoints" commandName="Skip All Breakpoints" description="Sets whether or not any breakpoint should suspend execution" category="_2vkMqUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmo60QHEfC4A_fUchu1mw" elementId="org.eclipse.ui.edit.text.set.mark" commandName="Set Mark" description="Set the mark" category="_2vkMrkQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmo7EQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.team.OpenCommit" commandName="Open Git Commit" category="_2vkMq0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmo7UQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.team.RenameBranch" commandName="Rename Branch" category="_2vkMq0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmo7kQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.ui.menu.rebuildIndex" commandName="Rebuild Index" category="_2vkMpEQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmo70QHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.team.AddToIndex" commandName="Add to Git Index" category="_2vkMq0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmo8EQHEfC4A_fUchu1mw" elementId="org.eclipse.epp.mpc.ui.command.showMarketplaceWizard" commandName="Eclipse Marketplace" description="Show the Eclipse Marketplace wizard" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmo8UQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.file.exit" commandName="Exit" description="Exit the application" category="_2vkMqEQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmo8kQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.ui.navigate.open.type.in.hierarchy" commandName="Open Type in Hierarchy" description="Open a type in the type hierarchy view" category="_2vkMu0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmo80QHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.team.ConfigurePush" commandName="Configure Upstream Push" category="_2vkMq0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmo9EQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.window.customizePerspective" commandName="Customize Perspective" description="Customize the current perspective" category="_2vkMoUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmo9UQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.navigate.showInQuickMenu" commandName="Show In..." description="Open the Show In menu" category="_2vkMu0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmo9kQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.ui.edit.text.c.organize.includes" commandName="Organize Includes" description="Evaluates all required includes and replaces the current includes" category="_2vkMs0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmo90QHEfC4A_fUchu1mw" elementId="org.eclipse.ui.ide.deleteCompleted" commandName="Delete Completed Tasks" description="Delete the tasks marked as completed" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmo-EQHEfC4A_fUchu1mw" elementId="org.eclipse.debug.ui.DebugPerspective" commandName="Debug" description="Open the debug perspective" category="_2vkMoEQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmo-UQHEfC4A_fUchu1mw" elementId="org.omnetpp.eventlogtable.refresh" commandName="Refresh" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmo-kQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.CheckoutCommand" commandName="Checkout" category="_2vkMq0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmo-0QHEfC4A_fUchu1mw" elementId="org.eclipse.ltk.ui.refactoring.commands.deleteResources" commandName="Delete Resources" description="Delete the selected resources and notify LTK participants." category="_2vkMukQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmo_EQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.edit.rename" commandName="Rename" description="Rename the selected item" category="_2vkMqEQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmo_UQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.navigate.next" commandName="Next" description="Navigate to the next item" category="_2vkMu0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmo_kQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.project.buildAutomatically" commandName="Build Automatically" description="Toggle the workspace build automatically function" category="_2vkMpEQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmo_0QHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.internal.reflog.OpenInCommitViewerCommand" commandName="Open in Commit Viewer" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmpAEQHEfC4A_fUchu1mw" elementId="org.omnetpp.cdt.cleanSelectedProjects" commandName="Clean Local" description="Clean the selected project(s) without cleaning referred projects." category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmpAUQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.edit.text.select.lineUp" commandName="Select Line Up" description="Extend the selection to the previous line of text" category="_2vkMrkQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmpAkQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.team.ShowHistory" commandName="Show in History" category="_2vkMq0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmpA0QHEfC4A_fUchu1mw" elementId="org.eclipse.compare.compareWithOther" commandName="Compare With Other Resource" description="Compare resources, clipboard contents or editors" category="_2vkMuEQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmpBEQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.ui.edit.text.c.goto.next.bookmark" commandName="Next Bookmark" description="Goto next bookmark of the selected file" category="_2vkMs0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmpBUQHEfC4A_fUchu1mw" elementId="org.omnetpp.common.handlers.fullScreen" commandName="Full Screen" category="_2vkMpEQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmpBkQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.browser.openBundleResource" commandName="Open Resource in Browser" description="Opens a bundle resource in the default web browser." category="_2vkMoUQHEfC4A_fUchu1mw">
    <parameters xmi:id="_2vmpB0QHEfC4A_fUchu1mw" elementId="plugin" name="Plugin"/>
    <parameters xmi:id="_2vmpCEQHEfC4A_fUchu1mw" elementId="path" name="Path"/>
  </commands>
  <commands xmi:id="_2vmpCUQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.team.stash.apply" commandName="Apply Stashed Changes" category="_2vkMq0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmpCkQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.ContinueRebase" commandName="Continue Rebase" category="_2vkMq0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmpC0QHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.team.Push" commandName="Push" category="_2vkMq0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmpDEQHEfC4A_fUchu1mw" elementId="org.eclipse.compare.copyAllLeftToRight" commandName="Copy All from Left to Right" description="Copy All Changes from Left to Right" category="_2vkMuEQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmpDUQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.edit.text.toggleShowWhitespaceCharacters" commandName="Show Whitespace Characters" description="Shows whitespace characters in current text editor" category="_2vkMrkQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmpDkQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.edit.selectAll" commandName="Select All" description="Select all" category="_2vkMsEQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmpD0QHEfC4A_fUchu1mw" elementId="org.eclipse.ui.window.preferences" commandName="Preferences" description="Open the preferences dialog" category="_2vkMoUQHEfC4A_fUchu1mw">
    <parameters xmi:id="_2vmpEEQHEfC4A_fUchu1mw" elementId="preferencePageId" name="Preference Page"/>
  </commands>
  <commands xmi:id="_2vmpEUQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.internal.reflog.CopyCommand" commandName="Copy SHA-1" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmpEkQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.file.close" commandName="Close" description="Close the active editor" category="_2vkMqEQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmpE0QHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.ui.edit.text.c.comment" commandName="Comment" description="Turn the selected lines into // style comments" category="_2vkMs0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmpFEQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.newWizard" commandName="New" description="Open the New item wizard" category="_2vkMqEQHEfC4A_fUchu1mw">
    <parameters xmi:id="_2vmpFUQHEfC4A_fUchu1mw" elementId="newWizardId" name="New Wizard"/>
  </commands>
  <commands xmi:id="_2vmpFkQHEfC4A_fUchu1mw" elementId="org.eclipse.help.ui.ignoreMissingPlaceholders" commandName="Do not warn of missing documentation" description="Sets the help preferences to no longer report a warning about the current set of missing documents." category="_2vkMt0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmpF0QHEfC4A_fUchu1mw" elementId="org.eclipse.ui.help.tipsAndTricksAction" commandName="Tips and Tricks" description="Open the tips and tricks help page" category="_2vkMt0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmpGEQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.ui.edit.text.c.find.word" commandName="Find Word" description="Select a word and find the next occurrence" category="_2vkMs0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmpGUQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.edit.copy" commandName="Copy" description="Copy the selection to the clipboard" category="_2vkMsEQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmpGkQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.ui.deleteConfigsCommand" commandName="Reset to Default" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmpG0QHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.history.Revert" commandName="Revert Commit" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmpHEQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.RepositoriesViewCreateBranch" commandName="Create Branch..." category="_2vkMq0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmpHUQHEfC4A_fUchu1mw" elementId="org.eclipse.debug.ui.commands.RunToLine" commandName="Run to Line" description="Resume and break when execution reaches the current line" category="_2vkMqUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmpHkQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.ui.edit.text.c.select.previous" commandName="Select Previous C/C++ Element" description="Expand the selection to enclosing C/C++ element" category="_2vkMsEQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmpH0QHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.ui.refactor.toggle.function" commandName="Toggle Function - Refactoring " description="Toggles the implementation between header and implementation file" category="_2vkMo0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmpIEQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.ide.copyConfigCommand" commandName="Copy Configuration Data To Clipboard" description="Copies the configuration data (system properties, installed bundles, etc) to the clipboard." category="_2vkMsEQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmpIUQHEfC4A_fUchu1mw" elementId="org.eclipse.debug.ui.commands.Restart" commandName="Restart" description="Restart a process or debug target without terminating and re-launching" category="_2vkMqUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmpIkQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.part.previousPage" commandName="Previous Page" description="Switch to the previous page" category="_2vkMu0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmpI0QHEfC4A_fUchu1mw" elementId="org.eclipse.ui.navigate.openResource" commandName="Open Resource" description="Open an editor on a particular resource" category="_2vkMu0QHEfC4A_fUchu1mw">
    <parameters xmi:id="_2vmpJEQHEfC4A_fUchu1mw" elementId="filePath" name="File Path" typeId="org.eclipse.ui.ide.resourcePath"/>
  </commands>
  <commands xmi:id="_2vmpJUQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.team.Synchronize" commandName="Synchronize" category="_2vkMq0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmpJkQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.ui.edit.opencview" commandName="Show in C/C++ Project view" description="Show the selected resource in the C/C++ Project view" category="_2vkMs0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmpJ0QHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.debug.ui.command.reverseStepOver" commandName="Reverse Step Over" description="Perform Reverse Step Over" category="_2vkMskQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmpKEQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.help.helpContents" commandName="Help Contents" description="Open the help contents" category="_2vkMt0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmpKUQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.file.saveAll" commandName="Save All" description="Save all current contents" category="_2vkMqEQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmpKkQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.team.GarbageCollect" commandName="Collect Garbage" category="_2vkMq0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmpK0QHEfC4A_fUchu1mw" elementId="org.omnetpp.ned.editor.text.CorrectIndentation" commandName="Correct Indentation" description="Reindent selected lines of the NED source" category="_2vkMrkQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmpLEQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.edit.text.select.textEnd" commandName="Select Text End" description="Select to the end of the text" category="_2vkMrkQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmpLUQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.edit.text.delete.line" commandName="Delete Line" description="Delete a line of text" category="_2vkMrkQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmpLkQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.team.PushBranch" commandName="Push Branch..." category="_2vkMq0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmpL0QHEfC4A_fUchu1mw" elementId="org.eclipse.ui.window.showViewMenu" commandName="Show View Menu" description="Show the view menu" category="_2vkMoUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmpMEQHEfC4A_fUchu1mw" elementId="org.eclipse.linuxtools.valgrind.launch.clearMarkersCommand" commandName="Remove Valgrind Markers" description="Removes all Valgrind markers" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmpMUQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.edit.findIncremental" commandName="Incremental Find" description="Incremental find" category="_2vkMsEQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vmpMkQHEfC4A_fUchu1mw" elementId="org.eclipse.search.ui.performTextSearchProject" commandName="Find Text in Project" description="Searches the files in the project for specific text." category="_2vkMr0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vnP8EQHEfC4A_fUchu1mw" elementId="org.omnetpp.sequencechart.findText" commandName="Find..." category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vnP8UQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.make.ui.targetBuildLastCommand" commandName="Rebuild Last Target" description="Rebuild the last make target for the selected container or project." category="_2vkMpEQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vnP8kQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.history.DeleteBranch" commandName="Delete Branch..." category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vnP80QHEfC4A_fUchu1mw" elementId="org.eclipse.ui.window.previousEditor" commandName="Previous Editor" description="Switch to the previous editor" category="_2vkMoUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vnP9EQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.window.maximizePart" commandName="Maximize Active View or Editor" description="Toggles maximize/restore state of active view or editor" category="_2vkMoUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vnP9UQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.ide.configureColumns" commandName="Configure Columns..." description="Configure the columns in the markers view" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vnP9kQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.debug.ui.command.debugNewExecutable" commandName="Debug New Executable" description="Debug a new executable" category="_2vkMrUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vnP90QHEfC4A_fUchu1mw" elementId="org.eclipse.ui.editors.revisions.id.toggle" commandName="Toggle Revision Id Display" description="Toggles the display of the revision id" category="_2vkMrkQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vnP-EQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.edit.text.folding.collapse_all" commandName="Collapse All" description="Collapses all folded regions" category="_2vkMrkQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vnP-UQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.commit.CherryPick" commandName="Cherry Pick" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vnP-kQHEfC4A_fUchu1mw" elementId="org.eclipse.debug.ui.commands.eof" commandName="EOF" description="Send end of file" category="_2vkMqUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vnP-0QHEfC4A_fUchu1mw" elementId="org.eclipse.ui.perspectives.showPerspective" commandName="Show Perspective" description="Show a particular perspective" category="_2vkMoEQHEfC4A_fUchu1mw">
    <parameters xmi:id="_2vnP_EQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.perspectives.showPerspective.perspectiveId" name="Parameter"/>
    <parameters xmi:id="_2vnP_UQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.perspectives.showPerspective.newWindow" name="In New Window"/>
  </commands>
  <commands xmi:id="_2vnP_kQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.navigate.previousSubTab" commandName="Previous Sub-Tab" description="Switch to the previous sub-tab" category="_2vkMu0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vnP_0QHEfC4A_fUchu1mw" elementId="org.eclipse.ui.editors.quickdiff.revert" commandName="Revert Lines" description="Revert the current selection, block or deleted lines" category="_2vkMrkQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vnQAEQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.edit.text.smartEnter" commandName="Insert Line Below Current Line" description="Adds a new line below the current line" category="_2vkMrkQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vnQAUQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.edit.text.goto.pageDown" commandName="Page Down" description="Go down one page" category="_2vkMrkQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vnQAkQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.team.Reset" commandName="Reset..." category="_2vkMq0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vnQA0QHEfC4A_fUchu1mw" elementId="org.eclipse.ui.edit.text.goto.windowStart" commandName="Window Start" description="Go to the start of the window" category="_2vkMrkQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vnQBEQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.debug.ui.command.stopTracing" commandName="Stop Tracing " description="Stop Tracing Experiment" category="_2vkMuUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vnQBUQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.navigate.goInto" commandName="Go Into" description="Navigate into the selected item" category="_2vkMu0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vnQBkQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.debug.ui.command.ungroupDebugContexts" commandName="Ungroup" description="Ungroups the selected debug contexts" category="_2vkMrUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vnQB0QHEfC4A_fUchu1mw" elementId="org.omnetpp.inifile.editor.text.AddMissingKeys" commandName="Add Missing Keys" description="Add missing keys to the INI file" category="_2vkMrkQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vnQCEQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.edit.text.contentAssist.proposals" commandName="Content Assist" description="Content Assist" category="_2vkMsEQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vnQCUQHEfC4A_fUchu1mw" elementId="org.omnetpp.ned.editor.text.InferAllGateLabels" commandName="Infer Gate Labels" description="Infer gate labels from connected gates" category="_2vkMrkQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vnQCkQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.edit.text.folding.expand_all" commandName="Expand All" description="Expands all folded regions" category="_2vkMrkQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vnQC0QHEfC4A_fUchu1mw" elementId="org.eclipse.ui.navigate.nextTab" commandName="Next Tab" description="Switch to the next tab" category="_2vkMu0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vnQDEQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.ui.edit.text.c.add.block.comment" commandName="Add Block Comment" description="Enclose the selection with a block comment" category="_2vkMs0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vnQDUQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.team.Discard" commandName="Replace with File in Git Index" category="_2vkMq0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vnQDkQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.ide.OpenMarkersView" commandName="Open Another" description="Open another view" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vnQD0QHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.ui.edit.text.c.goto.matching.bracket" commandName="Go to Matching Bracket" description="Moves the cursor to the matching bracket" category="_2vkMs0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vnQEEQHEfC4A_fUchu1mw" elementId="org.eclipse.debug.ui.commands.Terminate" commandName="Terminate" description="Terminate" category="_2vkMqUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vnQEUQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.team.NoAssumeUnchanged" commandName="No Assume Unchanged" category="_2vkMq0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vnQEkQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.ui.edit.text.c.sort.lines" commandName="Sort Lines" description="Sort selected lines alphabetically" category="_2vkMs0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vnQE0QHEfC4A_fUchu1mw" elementId="org.eclipse.ui.edit.text.select.windowEnd" commandName="Select Window End" description="Select to the end of the window" category="_2vkMrkQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vnQFEQHEfC4A_fUchu1mw" elementId="org.eclipse.debug.ui.command.gotoaddress" commandName="Go to Address" description="Go to Address" category="_2vkMqUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vnQFUQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.make.ui.edit.text.makefile.uncomment" commandName="Uncomment" description="Uncomment the selected # style comment lines" category="_2vkMp0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vnQFkQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.command.shareProject" commandName="Share with Git" description="Share the project using Git" category="_2vkMsUQHEfC4A_fUchu1mw">
    <parameters xmi:id="_2vnQF0QHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.command.projectNameParameter" name="Project" optional="false"/>
  </commands>
  <commands xmi:id="_2vnQGEQHEfC4A_fUchu1mw" elementId="org.eclipse.debug.ui.commands.Suspend" commandName="Suspend" description="Suspend" category="_2vkMqUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vnQGUQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.ui.search.findrefs.workingset" commandName="References in Working Set" description="Search for references to the selected element in a working set" category="_2vkMs0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vnQGkQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.window.switchToEditor" commandName="Switch to Editor" description="Switch to an editor" category="_2vkMoUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vnQG0QHEfC4A_fUchu1mw" elementId="org.eclipse.ui.window.previousView" commandName="Previous View" description="Switch to the previous view" category="_2vkMoUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vnQHEQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.ui.navigate.open.element.in.call.hierarchy" commandName="Open Element in Call Hierarchy" description="Open an element in the call hierarchy view" category="_2vkMu0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vnQHUQHEfC4A_fUchu1mw" elementId="org.eclipse.debug.ui.commands.closeRendering" commandName="Close Rendering" description="Close the selected rendering." category="_2vkMqUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vnQHkQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.navigate.linkWithEditor" commandName="Toggle Link with Editor " description="Toggles linking of a view's selection with the active editor's selection" category="_2vkMu0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vnQH0QHEfC4A_fUchu1mw" elementId="org.eclipse.team.ui.synchronizeAll" commandName="Synchronize..." description="Synchronize resources in the workspace with another location" category="_2vkMtkQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vnQIEQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.help.displayHelp" commandName="Display Help" description="Display a Help topic" category="_2vkMt0QHEfC4A_fUchu1mw">
    <parameters xmi:id="_2vnQIUQHEfC4A_fUchu1mw" elementId="href" name="Help topic href"/>
  </commands>
  <commands xmi:id="_2vnQIkQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.team.Branch" commandName="Branch" category="_2vkMq0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vnQI0QHEfC4A_fUchu1mw" elementId="org.eclipse.ui.window.previousPerspective" commandName="Previous Perspective" description="Switch to the previous perspective" category="_2vkMoUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vnQJEQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.project.closeProject" commandName="Close Project" description="Close the selected project" category="_2vkMpEQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vnQJUQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.team.Disconnect" commandName="Disconnect" category="_2vkMq0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vnQJkQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.ui.refactoring.command.ExtractConstant" commandName="Extract Constant..." category="_2vkMo0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vnQJ0QHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.FetchGerritChange" commandName="Fetch From Gerrit" category="_2vkMq0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vnQKEQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.edit.text.folding.expand" commandName="Expand" description="Expands the folded region at the current selection" category="_2vkMrkQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vnQKUQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.RepositoriesViewRemove" commandName="Remove Repository" category="_2vkMq0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vnQKkQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.team.PushTags" commandName="Push Tags..." category="_2vkMq0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vnQK0QHEfC4A_fUchu1mw" elementId="org.eclipse.ui.edit.text.goto.lineStart" commandName="Line Start" description="Go to the start of the line of text" category="_2vkMrkQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vnQLEQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.ui.edit.text.c.select.next" commandName="Select Next C/C++ Element" description="Expand the selection to next C/C++ element" category="_2vkMsEQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vnQLUQHEfC4A_fUchu1mw" elementId="org.eclipse.help.ui.closeTray" commandName="Close User Assistance Tray" description="Close the user assistance tray containing context help information and cheat sheets." category="_2vkMt0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vnQLkQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.project.properties" commandName="Properties" description="Display the properties of the selected item's project " category="_2vkMpEQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vnQL0QHEfC4A_fUchu1mw" elementId="org.eclipse.ui.edit.text.toggleBlockSelectionMode" commandName="Toggle Block Selection" description="Toggle block / column selection in the current text editor" category="_2vkMsEQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vnQMEQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.file.save" commandName="Save" description="Save the current contents" category="_2vkMqEQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vnQMUQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.history.CompareWithWorkingTree" commandName="Compare with Working Directory" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vnQMkQHEfC4A_fUchu1mw" elementId="org.eclipse.debug.ui.commands.ToggleWatchpoint" commandName="Toggle Watchpoint" description="Creates or removes a watchpoint" category="_2vkMqUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vnQM0QHEfC4A_fUchu1mw" elementId="org.eclipse.ui.file.closePart" commandName="Close Part" description="Close the active workbench part" category="_2vkMoUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vnQNEQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.RepositoriesViewConfigureBranch" commandName="Configure Branch" category="_2vkMq0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vnQNUQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.commit.Squash" commandName="Squash Commits" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vnQNkQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.history.OpenInCommitViewerCommand" commandName="Open in Commit Viewer" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vnQN0QHEfC4A_fUchu1mw" elementId="org.eclipse.gef.zoom_in" commandName="Zoom In" description="Zoom In" category="_2vkMtUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vnQOEQHEfC4A_fUchu1mw" elementId="org.omnetpp.ned.editor.text.FormatSource" commandName="Format NED Source" description="Format the NED source file" category="_2vkMrkQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vnQOUQHEfC4A_fUchu1mw" elementId="org.eclipse.jdt.ui.edit.text.java.correction.assist.proposals" commandName="Quick Fix" description="Suggest possible fixes for a problem" category="_2vkMsEQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vnQOkQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.editors.revisions.rendering.cycle" commandName="Cycle Revision Coloring Mode" description="Cycles through the available coloring modes for revisions" category="_2vkMrkQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vnQO0QHEfC4A_fUchu1mw" elementId="org.eclipse.ui.edit.text.showRulerContextMenu" commandName="Show Ruler Context Menu" description="Show the context menu for the ruler" category="_2vkMoUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vnQPEQHEfC4A_fUchu1mw" elementId="org.eclipse.debug.ui.commands.ToggleMethodBreakpoint" commandName="Toggle Method Breakpoint" description="Creates or removes a method breakpoint" category="_2vkMqUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vnQPUQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.window.minimizePart" commandName="Minimize Active View or Editor" description="Minimizes the active view or editor" category="_2vkMoUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vnQPkQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.team.Untrack" commandName="Untrack" category="_2vkMq0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vnQP0QHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.ui.menu.updateUnresolvedIncludes" commandName="Re-resolve Unresolved Includes" category="_2vkMpEQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vnQQEQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.part.nextPage" commandName="Next Page" description="Switch to the next page" category="_2vkMu0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vnQQUQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.team.ReplaceWithRef" commandName="Replace with branch, tag, or reference" category="_2vkMq0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vnQQkQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.edit.text.delete.line.to.beginning" commandName="Delete to Beginning of Line" description="Delete to the beginning of a line of text" category="_2vkMrkQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vnQQ0QHEfC4A_fUchu1mw" elementId="org.eclipse.ui.navigate.backwardHistory" commandName="Backward History" description="Move backward in the editor navigation history" category="_2vkMu0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vnQREQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.edit.text.swap.mark" commandName="Swap Mark" description="Swap the mark with the cursor position" category="_2vkMrkQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vnQRUQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.ui.refactoring.command.ExtractLocalVariable" commandName="Extract Local Variable..." category="_2vkMo0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vnQRkQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.project.buildProject" commandName="Build Project" description="Build the selected project" category="_2vkMpEQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vnQR0QHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.debug.ui.command.saveTraceData" commandName="Save Trace Data " description="Save Trace Data to File" category="_2vkMuUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vnQSEQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.window.showSystemMenu" commandName="Show System Menu" description="Show the system menu" category="_2vkMoUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vnQSUQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.edit.text.lowerCase" commandName="To Lower Case" description="Changes the selection to lower case" category="_2vkMrkQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vnQSkQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.ui.edit.text.c.goto.next.member" commandName="Go to Next Member" description="Move the caret to the next member of the translation unit" category="_2vkMs0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vnQS0QHEfC4A_fUchu1mw" elementId="org.eclipse.ui.edit.text.select.pageDown" commandName="Select Page Down" description="Select to the bottom of the page" category="_2vkMrkQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vnQTEQHEfC4A_fUchu1mw" elementId="org.eclipse.debug.ui.commands.newRendering" commandName="New Rendering" description="Add a new rendering." category="_2vkMqUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vnQTUQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.ui.menu.freshenAllFiles" commandName="Freshen All Files in Index" category="_2vkMpEQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vnQTkQHEfC4A_fUchu1mw" elementId="org.eclipse.equinox.p2.ui.discovery.commands.ShowBundleCatalog" commandName="Show Bundle Catalog" category="_2vkMsUQHEfC4A_fUchu1mw">
    <parameters xmi:id="_2vnQT0QHEfC4A_fUchu1mw" elementId="org.eclipse.equinox.p2.ui.discovery.commands.DirectoryParameter" name="Directory URL"/>
    <parameters xmi:id="_2vn3AEQHEfC4A_fUchu1mw" elementId="org.eclipse.equinox.p2.ui.discovery.commands.TagsParameter" name="Tags"/>
  </commands>
  <commands xmi:id="_2vn3AUQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.team.SimplePush" commandName="Push to Upstream" category="_2vkMq0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3AkQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.team.MergeTool" commandName="Merge Tool" category="_2vkMq0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3A0QHEfC4A_fUchu1mw" elementId="org.eclipse.ui.edit.text.open.hyperlink" commandName="Open Hyperlink" description="Opens the hyperlink at the caret location or opens a chooser if more than one hyperlink is available" category="_2vkMrkQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3BEQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.history.OpenInTextEditorCommand" commandName="Open in Text Editor" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3BUQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.edit.text.openLocalFile" commandName="Open File..." description="Open a file" category="_2vkMqEQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3BkQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.window.splitEditor" commandName="Toggle Split Editor" description="Split or join the currently active editor." category="_2vkMoUQHEfC4A_fUchu1mw">
    <parameters xmi:id="_2vn3B0QHEfC4A_fUchu1mw" elementId="Splitter.isHorizontal" name="Orientation" optional="false"/>
  </commands>
  <commands xmi:id="_2vn3CEQHEfC4A_fUchu1mw" elementId="org.eclipse.compare.selectPreviousChange" commandName="Select Previous Change" description="Select Previous Change" category="_2vkMuEQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3CUQHEfC4A_fUchu1mw" elementId="org.eclipse.gef.zoom_out" commandName="Zoom Out" description="Zoom Out" category="_2vkMtUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3CkQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.edit.text.toggleShowSelectedElementOnly" commandName="Show Selected Element Only" description="Show Selected Element Only" category="_2vkMoUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3C0QHEfC4A_fUchu1mw" elementId="org.eclipse.ui.edit.text.select.wordPrevious" commandName="Select Previous Word" description="Select the previous word" category="_2vkMrkQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3DEQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.history.Reset" commandName="Reset..." category="_2vkMsUQHEfC4A_fUchu1mw">
    <parameters xmi:id="_2vn3DUQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.history.ResetMode" name="Reset mode" optional="false"/>
  </commands>
  <commands xmi:id="_2vn3DkQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.ToggleCoolbarAction" commandName="Toggle Toolbar Visibility" description="Toggles the visibility of the window toolbar" category="_2vkMoUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3D0QHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.RebaseInteractiveCurrent" commandName="%RebaseInteractiveCurrentHandler.name" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3EEQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.debug.ui.command.reverseToggle" commandName="Reverse Toggle" description="Toggle Reverse Debugging" category="_2vkMskQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3EUQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.history.CompareVersionsInTree" commandName="Compare in Tree" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3EkQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.ui.edit.opendecl" commandName="Open Declaration" description="Open an editor on the selected element's declaration(s)" category="_2vkMs0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3E0QHEfC4A_fUchu1mw" elementId="org.eclipse.ui.project.openProject" commandName="Open Project" description="Open a project" category="_2vkMpEQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3FEQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.edit.cut" commandName="Cut" description="Cut the selection to the clipboard" category="_2vkMsEQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3FUQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.commit.Edit" commandName="Edit Commit" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3FkQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.edit.text.moveLineDown" commandName="Move Lines Down" description="Moves the selected lines down" category="_2vkMrkQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3F0QHEfC4A_fUchu1mw" elementId="org.eclipse.ui.edit.findReplace" commandName="Find and Replace" description="Find and replace text" category="_2vkMsEQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3GEQHEfC4A_fUchu1mw" elementId="org.omnetpp.ned.editor.text.OrganizeImports" commandName="Organize Imports" description="Organizes the import statements in a NED source file" category="_2vkMrkQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3GUQHEfC4A_fUchu1mw" elementId="org.eclipse.compare.copyLeftToRight" commandName="Copy from Left to Right" description="Copy Current Change from Left to Right" category="_2vkMuEQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3GkQHEfC4A_fUchu1mw" elementId="org.eclipse.team.ui.applyPatch" commandName="Apply Patch..." description="Apply a patch to one or more workspace projects." category="_2vkMtkQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3G0QHEfC4A_fUchu1mw" elementId="org.eclipse.quickdiff.toggle" commandName="Quick Diff Toggle" description="Toggles quick diff information display on the line number ruler" category="_2vkMsEQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3HEQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.debug.ui.command.startTracing" commandName="Start Tracing " description="Start Tracing Experiment" category="_2vkMuUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3HUQHEfC4A_fUchu1mw" elementId="org.eclipse.ltk.ui.refactoring.commands.moveResources" commandName="Move Resources" description="Move the selected resources and notify LTK participants." category="_2vkMukQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3HkQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.RepositoriesViewRenameBranch" commandName="Rename Branch..." category="_2vkMq0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3H0QHEfC4A_fUchu1mw" elementId="org.eclipse.ui.edit.text.toggleInsertMode" commandName="Toggle Insert Mode" description="Toggle insert mode" category="_2vkMsEQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3IEQHEfC4A_fUchu1mw" elementId="org.omnetpp.ned.editor.text.ConvertToNewFormat" commandName="Convert to 4.x Format" description="Convert the NED source to the OMNeT++ 4.x format" category="_2vkMrkQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3IUQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.edit.text.goto.line" commandName="Go to Line" description="Go to a specified line of text" category="_2vkMu0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3IkQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.navigate.nextSubTab" commandName="Next Sub-Tab" description="Switch to the next sub-tab" category="_2vkMu0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3I0QHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.team.stash.create" commandName="Stash Changes" category="_2vkMq0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3JEQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.RepositoriesViewRemoveRemote" commandName="Delete Remote" category="_2vkMq0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3JUQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.ui.edit.text.c.toggleMarkOccurrences" commandName="Toggle Mark Occurrences" description="Toggles mark occurrences in C/C++ editors" category="_2vkMs0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3JkQHEfC4A_fUchu1mw" elementId="org.omnetpp.eventlogtable.gotoPreviousModuleEvent" commandName="Go to Previous Module Event" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3J0QHEfC4A_fUchu1mw" elementId="org.eclipse.ui.edit.text.goto.columnPrevious" commandName="Previous Column" description="Go to the previous column" category="_2vkMrkQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3KEQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.externaltools.ExternalToolMenuDelegateToolbar" commandName="Run Last Launched External Tool" description="Runs the last launched external Tool" category="_2vkMqUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3KUQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.team.Commit" commandName="Commit..." category="_2vkMq0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3KkQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.ui.search.findrefs" commandName="References" description="Search for references to the selected element in the workspace" category="_2vkMs0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3K0QHEfC4A_fUchu1mw" elementId="org.eclipse.ui.edit.addBookmark" commandName="Add Bookmark" description="Add a bookmark" category="_2vkMsEQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3LEQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.edit.text.select.lineStart" commandName="Select Line Start" description="Select to the beginning of the line of text" category="_2vkMrkQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3LUQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.history.CheckoutCommand" commandName="Checkout" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3LkQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.edit.text.cut.line.to.end" commandName="Cut to End of Line" description="Cut to the end of a line of text" category="_2vkMrkQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3L0QHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.ui.search.finddecl.project" commandName="Declaration in Project" description="Search for declarations of the selected element in the enclosing project" category="_2vkMs0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3MEQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.RepositoriesViewAddRepository" commandName="Add a Git Repository" category="_2vkMq0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3MUQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.ui.hover.forwardMacroExpansion" commandName="Forward" description="Step forward in macro expansions" category="_2vkMs0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3MkQHEfC4A_fUchu1mw" elementId="org.eclipse.equinox.p2.ui.sdk.update" commandName="Check for Updates" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3M0QHEfC4A_fUchu1mw" elementId="org.eclipse.ui.edit.findIncrementalReverse" commandName="Incremental Find Reverse" description="Incremental find reverse" category="_2vkMsEQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3NEQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.project.rebuildAll" commandName="Rebuild All" description="Rebuild all projects" category="_2vkMpEQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3NUQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.window.activateEditor" commandName="Activate Editor" description="Activate the editor" category="_2vkMoUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3NkQHEfC4A_fUchu1mw" elementId="org.eclipse.compare.copyAllRightToLeft" commandName="Copy All from Right to Left" description="Copy All Changes from Right to Left" category="_2vkMuEQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3N0QHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.history.PushCommit" commandName="Push Commit..." category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3OEQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.debug.ui.command.reverseResume" commandName="Reverse Resume" description="Perform Reverse Resume" category="_2vkMskQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3OUQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.project.closeUnrelatedProjects" commandName="Close Unrelated Projects" description="Close unrelated projects" category="_2vkMpEQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3OkQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.edit.text.goto.lineDown" commandName="Line Down" description="Go down one line of text" category="_2vkMrkQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3O0QHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.RepositoriesViewConfigureGerritRemote" commandName="Gerrit Configuration..." category="_2vkMq0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3PEQHEfC4A_fUchu1mw" elementId="org.eclipse.debug.ui.commands.ToggleBreakpoint" commandName="Toggle Breakpoint" description="Creates or removes a breakpoint" category="_2vkMqUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3PUQHEfC4A_fUchu1mw" elementId="org.eclipse.compare.ignoreWhiteSpace" commandName="Ignore White Space" description="Ignore white space where applicable" category="_2vkMuEQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3PkQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.navigate.previousTab" commandName="Previous Tab" description="Switch to the previous tab" category="_2vkMu0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3P0QHEfC4A_fUchu1mw" elementId="org.eclipse.ui.edit.text.gotoLastEditPosition" commandName="Last Edit Location" description="Last edit location" category="_2vkMu0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3QEQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.SkipRebase" commandName="Skip commit and continue" category="_2vkMq0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3QUQHEfC4A_fUchu1mw" elementId="org.eclipse.debug.ui.commands.toggleMemoryMonitorsPane" commandName="Toggle Memory Monitors Pane" description="Toggle visibility of the Memory Monitors Pane" category="_2vkMqUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3QkQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.RebaseCurrent" commandName="Rebase" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3Q0QHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.ui.edit.open.type.hierarchy" commandName="Open Type Hierarchy" description="Open a type hierarchy on the selected element" category="_2vkMu0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3REQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.ide.copyBuildIdCommand" commandName="Copy Build Id To Clipboard" description="Copies the build id to the clipboard." category="_2vkMsEQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3RUQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.dialogs.openInputDialog" commandName="Open Input Dialog" description="Open an Input Dialog" category="_2vkMrEQHEfC4A_fUchu1mw">
    <parameters xmi:id="_2vn3RkQHEfC4A_fUchu1mw" elementId="title" name="Title"/>
    <parameters xmi:id="_2vn3R0QHEfC4A_fUchu1mw" elementId="message" name="Message"/>
    <parameters xmi:id="_2vn3SEQHEfC4A_fUchu1mw" elementId="initialValue" name="Initial Value"/>
    <parameters xmi:id="_2vn3SUQHEfC4A_fUchu1mw" elementId="cancelReturns" name="Return Value on Cancel"/>
  </commands>
  <commands xmi:id="_2vn3SkQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.history.ShowVersions" commandName="Open" category="_2vkMsUQHEfC4A_fUchu1mw">
    <parameters xmi:id="_2vn3S0QHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.history.CompareMode" name="Compare mode"/>
  </commands>
  <commands xmi:id="_2vn3TEQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.ui.navigate.opentype" commandName="Open Element" description="Open an element in an Editor" category="_2vkMs0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3TUQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.views.properties.NewPropertySheetCommand" commandName="Properties" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3TkQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.navigate.collapseAll" commandName="Collapse All" description="Collapse the current tree" category="_2vkMu0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3T0QHEfC4A_fUchu1mw" elementId="org.eclipse.ui.edit.text.folding.toggle" commandName="Toggle Folding" description="Toggles folding in the current editor" category="_2vkMrkQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3UEQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.team.submodule.update" commandName="Update Submodule" category="_2vkMq0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3UUQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.debug.ui.command.StepIntoSelection" commandName="Step Into Selection" description="Step into the current selected statement" category="_2vkMqUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3UkQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.ui.edit.text.c.remove.block.comment" commandName="Remove Block Comment" description="Remove the block comment enclosing the selection" category="_2vkMs0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3U0QHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.commit.Revert" commandName="Revert Commit" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3VEQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.RepositoriesViewCreateRepository" commandName="Create a Repository" category="_2vkMq0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3VUQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.file.revert" commandName="Revert" description="Revert to the last saved state" category="_2vkMqEQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3VkQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.edit.text.scroll.lineDown" commandName="Scroll Line Down" description="Scroll down one line of text" category="_2vkMrkQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3V0QHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.team.SimpleFetch" commandName="Fetch from Upstream" category="_2vkMq0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3WEQHEfC4A_fUchu1mw" elementId="org.eclipse.debug.ui.commands.StepInto" commandName="Step Into" description="Step into" category="_2vkMqUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3WUQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.edit.redo" commandName="Redo" description="Redo the last operation" category="_2vkMsEQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3WkQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.commit.Checkout" commandName="Checkout" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3W0QHEfC4A_fUchu1mw" elementId="org.eclipse.ui.edit.text.join.lines" commandName="Join Lines" description="Join lines of text" category="_2vkMrkQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3XEQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.ui.search.finddecl" commandName="Declaration" description="Search for declarations of the selected element in the workspace" category="_2vkMs0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3XUQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.edit.text.scroll.lineUp" commandName="Scroll Line Up" description="Scroll up one line of text" category="_2vkMrkQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3XkQHEfC4A_fUchu1mw" elementId="org.eclipse.debug.ui.commands.TerminateAndRelaunch" commandName="Terminate and Relaunch" description="Terminate and Relaunch" category="_2vkMqUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3X0QHEfC4A_fUchu1mw" elementId="org.eclipse.equinox.p2.ui.sdk.installationDetails" commandName="Installation Details" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3YEQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.file.print" commandName="Print" description="Print" category="_2vkMqEQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3YUQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.navigate.forwardHistory" commandName="Forward History" description="Move forward in the editor navigation history" category="_2vkMu0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3YkQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.edit.text.select.pageUp" commandName="Select Page Up" description="Select to the top of the page" category="_2vkMrkQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3Y0QHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.team.CompareWithPrevious" commandName="Compare with Previous Revision" category="_2vkMq0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3ZEQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.ui.menu.findUnresolvedIncludes" commandName="Search for Unresolved Includes" category="_2vkMpEQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3ZUQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.help.dynamicHelp" commandName="Dynamic Help" description="Open the dynamic help" category="_2vkMt0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3ZkQHEfC4A_fUchu1mw" elementId="org.eclipse.debug.ui.commands.RunLast" commandName="Run" description="Launch in run mode" category="_2vkMqUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3Z0QHEfC4A_fUchu1mw" elementId="org.eclipse.ui.edit.text.copyLineUp" commandName="Duplicate Lines" description="Duplicates the selected lines and leaves the selection unchanged" category="_2vkMrkQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3aEQHEfC4A_fUchu1mw" elementId="org.omnetpp.ned.editor.text.performTextSearchWorkspace" commandName="Search Text in NED Files" description="Perform text search in workspace NED files" category="_2vkMrkQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3aUQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.commit.CreateBranch" commandName="Create Branch..." category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3akQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.navigate.removeFromWorkingSet" commandName="Remove From Working Set" description="Removes the selected object from a working set." category="_2vkMsEQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3a0QHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.team.ReplaceWithCommit" commandName="Replace with commit" category="_2vkMq0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3bEQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.team.DeleteBranch" commandName="Delete Branch" category="_2vkMq0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3bUQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.RepositoriesToggleBranchHierarchy" commandName="Toggle Branch Representation" category="_2vkMq0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3bkQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.team.ApplyPatch" commandName="Apply Patch" category="_2vkMq0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3b0QHEfC4A_fUchu1mw" elementId="org.eclipse.ui.edit.text.showRulerAnnotationInformation" commandName="Show Ruler Annotation Tooltip" description="Displays annotation information for the caret line in a focused hover" category="_2vkMrkQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3cEQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.edit.text.folding.collapse" commandName="Collapse" description="Collapses the folded region at the current selection" category="_2vkMrkQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3cUQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.ui.edit.text.c.source.quickMenu" commandName="Show Source Quick Menu" description="Shows the source quick menu" category="_2vkMs0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3ckQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.commit.ShowInHistory" commandName="Show in History" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3c0QHEfC4A_fUchu1mw" elementId="org.eclipse.equinox.p2.ui.sdk.install" commandName="Install New Software..." category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3dEQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.window.nextView" commandName="Next View" description="Switch to the next view" category="_2vkMoUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3dUQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.project.buildLast" commandName="Repeat Working Set Build" description="Repeat the last working set build" category="_2vkMpEQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3dkQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.edit.text.goto.textStart" commandName="Text Start" description="Go to the beginning of the text" category="_2vkMrkQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3d0QHEfC4A_fUchu1mw" elementId="org.eclipse.ui.file.properties" commandName="Properties" description="Display the properties of the selected item" category="_2vkMqEQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3eEQHEfC4A_fUchu1mw" elementId="org.eclipse.search.ui.openSearchDialog" commandName="Open Search Dialog" description="Open the Search dialog" category="_2vkMr0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3eUQHEfC4A_fUchu1mw" elementId="org.eclipse.debug.ui.actions.WatchCommand" commandName="Watch" description="Create a watch expression from the current selection and add it to the Expressions view" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3ekQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.file.openWorkspace" commandName="Switch Workspace" description="Open the workspace selection dialog" category="_2vkMqEQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3e0QHEfC4A_fUchu1mw" elementId="org.eclipse.ui.edit.text.moveLineUp" commandName="Move Lines Up" description="Moves the selected lines up" category="_2vkMrkQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3fEQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.ide.configureFilters" commandName="Configure Contents..." description="Configure the filters to apply to the markers view" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3fUQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.ui.edit.open.outline" commandName="Show outline" description="Shows outline" category="_2vkMs0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2vn3fkQHEfC4A_fUchu1mw" elementId="org.omnetpp.common.openWebBrowser" commandName="Open Web Page From Plugin" description="Open Web Page From Plugin" category="_2vkMt0QHEfC4A_fUchu1mw">
    <parameters xmi:id="_2voeEEQHEfC4A_fUchu1mw" elementId="pluginId" name="pluginId" optional="false"/>
    <parameters xmi:id="_2voeEUQHEfC4A_fUchu1mw" elementId="path" name="path" optional="false"/>
  </commands>
  <commands xmi:id="_2voeEkQHEfC4A_fUchu1mw" elementId="org.omnetpp.msg.editor.ToggleComment" commandName="Toggle Comment" description="Comment/Uncomment the selected lines" category="_2vkMrkQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voeE0QHEfC4A_fUchu1mw" elementId="org.eclipse.ui.edit.text.copyLineDown" commandName="Copy Lines" description="Duplicates the selected lines and moves the selection to the copy" category="_2vkMrkQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voeFEQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.ui.edit.open.call.hierarchy" commandName="Open Call Hierarchy" description="Open the call hierarchy for the selected element" category="_2vkMu0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voeFUQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.ui.menu.updateWithModifiedFiles" commandName="Update Index with Modified Files" category="_2vkMpEQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voeFkQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.debug.ui.command.connect" commandName="Connect" description="Connect to a process" category="_2vkMrUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voeF0QHEfC4A_fUchu1mw" elementId="org.eclipse.ui.edit.text.goto.wordNext" commandName="Next Word" description="Go to the next word" category="_2vkMrkQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voeGEQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.history.CherryPick" commandName="Cherry Pick" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voeGUQHEfC4A_fUchu1mw" elementId="org.omnetpp.eventlogtable.gotoEvent" commandName="Go to Event..." category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voeGkQHEfC4A_fUchu1mw" elementId="org.omnetpp.ned.editor.text.GotoDeclaration" commandName="Go to Declaration" description="Go to declaration of NED element" category="_2vkMrkQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voeG0QHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.ConfigureUpstreamFetch" commandName="Configure Upstream Fetch" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voeHEQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.team.Merge" commandName="Merge" category="_2vkMq0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voeHUQHEfC4A_fUchu1mw" elementId="org.omnetpp.sequencechart.gotoEvent" commandName="Go to Event..." category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voeHkQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.ui.edit.text.c.toggle.comment" commandName="Comment/Uncomment" description="Comment/Uncomment the selected lines" category="_2vkMs0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voeH0QHEfC4A_fUchu1mw" elementId="org.eclipse.ui.navigate.up" commandName="Up" description="Navigate up one level" category="_2vkMu0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voeIEQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.history.ResetQuickdiffBaseline" commandName="Reset quickdiff baseline" category="_2vkMsUQHEfC4A_fUchu1mw">
    <parameters xmi:id="_2voeIUQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.history.ResetQuickdiffBaselineTarget" name="Reset target (HEAD, HEAD^1)" optional="false"/>
  </commands>
  <commands xmi:id="_2voeIkQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.team.submodule.add" commandName="Add Submodule" category="_2vkMq0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voeI0QHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.make.ui.targetBuildCommand" commandName="Make Target Build" description="Invoke a make target build for the selected container." category="_2vkMpEQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voeJEQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.window.closePerspective" commandName="Close Perspective" description="Close the current perspective" category="_2vkMoUQHEfC4A_fUchu1mw">
    <parameters xmi:id="_2voeJUQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.window.closePerspective.perspectiveId" name="Perspective Id"/>
  </commands>
  <commands xmi:id="_2voeJkQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.window.hideShowEditors" commandName="Toggle Editor Area Visibility" description="Toggles the visibility of the editor area" category="_2vkMoUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voeJ0QHEfC4A_fUchu1mw" elementId="org.eclipse.ui.project.rebuildProject" commandName="Rebuild Project" description="Rebuild the selected projects" category="_2vkMpEQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voeKEQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.history.Edit" commandName="Edit Commit" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voeKUQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.edit.move" commandName="Move..." description="Move the selected item" category="_2vkMqEQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voeKkQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.ui.edit.text.c.surround.with.quickMenu" commandName="Surround With Quick Menu" description="Shows the Surround With quick menu" category="_2vkMs0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voeK0QHEfC4A_fUchu1mw" elementId="org.eclipse.ui.edit.text.clear.mark" commandName="Clear Mark" description="Clear the mark" category="_2vkMrkQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voeLEQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.ui.edit.text.c.goto.prev.member" commandName="Go to Previous Member" description="Move the caret to the previous member of the translation unit" category="_2vkMs0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voeLUQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.team.ReplaceWithHead" commandName="Replace with HEAD revision" category="_2vkMq0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voeLkQHEfC4A_fUchu1mw" elementId="org.eclipse.debug.ui.commands.OpenProfileConfigurations" commandName="Profile..." description="Open profile launch configuration dialog" category="_2vkMqUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voeL0QHEfC4A_fUchu1mw" elementId="org.eclipse.ui.cheatsheets.openCheatSheet" commandName="Open Cheat Sheet" description="Open a Cheat Sheet." category="_2vkMt0QHEfC4A_fUchu1mw">
    <parameters xmi:id="_2voeMEQHEfC4A_fUchu1mw" elementId="cheatSheetId" name="Identifier"/>
  </commands>
  <commands xmi:id="_2voeMUQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.RepositoriesViewOpen" commandName="Open" category="_2vkMq0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voeMkQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.RepositoriesToggleBranchCommit" commandName="Toggle Latest Branch Commit" category="_2vkMq0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voeM0QHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.debug.ui.command.castToArray" commandName="Cast To Type..." category="_2vkMqkQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voeNEQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.history.CompareVersions" commandName="Compare with each other" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voeNUQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.edit.text.contentAssist.contextInformation" commandName="Context Information" description="Show Context Information" category="_2vkMsEQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voeNkQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.browser.openBrowser" commandName="Open Browser" description="Opens the default web browser." category="_2vkMoUQHEfC4A_fUchu1mw">
    <parameters xmi:id="_2voeN0QHEfC4A_fUchu1mw" elementId="url" name="URL"/>
    <parameters xmi:id="_2voeOEQHEfC4A_fUchu1mw" elementId="browserId" name="Browser Id"/>
    <parameters xmi:id="_2voeOUQHEfC4A_fUchu1mw" elementId="name" name="Browser Name"/>
    <parameters xmi:id="_2voeOkQHEfC4A_fUchu1mw" elementId="tooltip" name="Browser Tooltip"/>
  </commands>
  <commands xmi:id="_2voeO0QHEfC4A_fUchu1mw" elementId="org.omnetpp.eventlogtable.gotoNextEvent" commandName="Go to Next Event" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voePEQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.edit.text.select.columnNext" commandName="Select Next Column" description="Select the next column" category="_2vkMrkQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voePUQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.ui.edit.text.c.select.last" commandName="Restore Last C/C++ Selection" description="Restore last selection in C/C++ editor" category="_2vkMsEQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voePkQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.edit.text.upperCase" commandName="To Upper Case" description="Changes the selection to upper case" category="_2vkMrkQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voeP0QHEfC4A_fUchu1mw" elementId="org.eclipse.debug.ui.commands.DropToFrame" commandName="Drop to Frame" description="Drop to Frame" category="_2vkMqUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voeQEQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.ui.edit.text.c.format" commandName="Format" description="Format Source Code" category="_2vkMs0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voeQUQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.RepositoriesViewNewRemote" commandName="Create Remote..." category="_2vkMq0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voeQkQHEfC4A_fUchu1mw" elementId="org.eclipse.search.ui.openFileSearchPage" commandName="File Search" description="Open the Search dialog's file search page" category="_2vkMr0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voeQ0QHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.debug.ui.command.resumeWithoutSignal" commandName="Resume Without Signal" description="Resume Without Signal" category="_2vkMtEQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voeREQHEfC4A_fUchu1mw" elementId="org.eclipse.team.ui.synchronizeLast" commandName="Repeat last synchronization" description="Repeat the last synchronization" category="_2vkMtkQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voeRUQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.history.CreatePatch" commandName="Create Patch" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voeRkQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.team.clean" commandName="Clean..." category="_2vkMq0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voeR0QHEfC4A_fUchu1mw" elementId="org.eclipse.ui.edit.text.goto.columnNext" commandName="Next Column" description="Go to the next column" category="_2vkMrkQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voeSEQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.navigate.selectWorkingSets" commandName="Select Working Sets" description="Select the working sets that are applicable for this window." category="_2vkMoUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voeSUQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.help.aboutAction" commandName="About" description="Open the about dialog" category="_2vkMt0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voeSkQHEfC4A_fUchu1mw" elementId="org.eclipse.compare.copyRightToLeft" commandName="Copy from Right to Left" description="Copy Current Change from Right to Left" category="_2vkMuEQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voeS0QHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.ui.edit.text.c.uncomment" commandName="Uncomment" description="Uncomment the selected // style comment lines" category="_2vkMs0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voeTEQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.command.configureTrace" commandName="Configure Git Debug Trace" category="_2vkMq0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voeTUQHEfC4A_fUchu1mw" elementId="org.eclipse.compare.selectNextChange" commandName="Select Next Change" description="Select Next Change" category="_2vkMuEQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voeTkQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.commands.rulerToggleBreakpoint" commandName="Toggle Breakpoint" description="Toggle breakpoint in disassembly ruler" category="_2vkMqUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voeT0QHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.ui.search.finddecl.workingset" commandName="Declaration in Working Set" description="Search for declarations of the selected element in a working set" category="_2vkMs0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voeUEQHEfC4A_fUchu1mw" elementId="org.eclipse.debug.ui.commands.RemoveAllBreakpoints" commandName="Remove All Breakpoints" description="Removes all breakpoints" category="_2vkMqUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voeUUQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.window.newWindow" commandName="New Window" description="Open another window" category="_2vkMoUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voeUkQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.navigate.showResourceByPath" commandName="Show Resource in Navigator" description="Show a resource in the Navigator given its path" category="_2vkMu0QHEfC4A_fUchu1mw">
    <parameters xmi:id="_2voeU0QHEfC4A_fUchu1mw" elementId="resourcePath" name="Resource Path" typeId="org.eclipse.ui.ide.resourcePath" optional="false"/>
  </commands>
  <commands xmi:id="_2voeVEQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.ui.refactor.implement.method" commandName="Implement Method - Source Generation " description="Implements a method for a selected method declaration" category="_2vkMs0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voeVUQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.make.ui.targetCreateCommand" commandName="Create Make Target" description="Create a new make build target for the selected container." category="_2vkMpEQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voeVkQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.dsf.gdb.ui.command.selectPreviousTraceRecord" commandName="Previous Trace Record" description="Select Previous Trace Record" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voeV0QHEfC4A_fUchu1mw" elementId="org.eclipse.ui.edit.text.shiftLeft" commandName="Shift Left" description="Shift a block of text to the left" category="_2vkMsEQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voeWEQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.project.cleanAction" commandName="Build Clean" description="Discard old built state" category="_2vkMpEQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voeWUQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.activeContextInfo" commandName="Show activeContext Info" category="_2vkMoUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voeWkQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.edit.findNext" commandName="Find Next" description="Find next item" category="_2vkMsEQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voeW0QHEfC4A_fUchu1mw" elementId="org.eclipse.debug.ui.commands.Disconnect" commandName="Disconnect" description="Disconnect" category="_2vkMqUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voeXEQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.commit.CreateTag" commandName="Create Tag..." category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voeXUQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.make.ui.edit.text.makefile.comment" commandName="Comment" description="Turn the selected lines into # style comments" category="_2vkMp0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voeXkQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.ui.edit.text.c.select.enclosing" commandName="Select Enclosing C/C++ Element" description="Expand the selection to enclosing C/C++ element" category="_2vkMsEQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voeX0QHEfC4A_fUchu1mw" elementId="org.eclipse.ui.edit.text.select.windowStart" commandName="Select Window Start" description="Select to the start of the window" category="_2vkMrkQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voeYEQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.ui.edit.text.c.toggle.source.header" commandName="Toggle Source/Header" description="Toggles between corresponding source and header files" category="_2vkMs0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voeYUQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.internal.reflog.CheckoutCommand" commandName="Checkout" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voeYkQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.help.quickStartAction" commandName="Welcome" description="Show help for beginning users" category="_2vkMt0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voeY0QHEfC4A_fUchu1mw" elementId="org.eclipse.ui.edit.addTask" commandName="Add Task..." description="Add a task" category="_2vkMsEQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voeZEQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.window.closeAllPerspectives" commandName="Close All Perspectives" description="Close all open perspectives" category="_2vkMoUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voeZUQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.editors.quickdiff.revertLine" commandName="Revert Line" description="Revert the current line" category="_2vkMrkQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voeZkQHEfC4A_fUchu1mw" elementId="org.eclipse.debug.ui.command.prevpage" commandName="Previous Page of Memory" description="Load previous page of memory" category="_2vkMqUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voeZ0QHEfC4A_fUchu1mw" elementId="org.eclipse.ui.edit.findPrevious" commandName="Find Previous" description="Find previous item" category="_2vkMsEQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voeaEQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.views.showView" commandName="Show View" description="Shows a particular view" category="_2vkMpUQHEfC4A_fUchu1mw">
    <parameters xmi:id="_2voeaUQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.views.showView.viewId" name="View"/>
    <parameters xmi:id="_2voeakQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.views.showView.secondaryId" name="Secondary Id"/>
    <parameters xmi:id="_2voea0QHEfC4A_fUchu1mw" elementId="org.eclipse.ui.views.showView.makeFast" name="As FastView"/>
  </commands>
  <commands xmi:id="_2voebEQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.debug.ui.commands.viewMemory" commandName="View Memory" description="View variable in memory view" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voebUQHEfC4A_fUchu1mw" elementId="org.omnetpp.eventlogtable.gotoPreviousEvent" commandName="Go to Previous Event" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voebkQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.edit.text.delimiter.windows" commandName="Convert Line Delimiters to Windows (CRLF, \r\n, 0D0A, &#xa4;&#xb6;)" description="Converts the line delimiters to Windows (CRLF, \r\n, 0D0A, &#xa4;&#xb6;)" category="_2vkMqEQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voeb0QHEfC4A_fUchu1mw" elementId="org.eclipse.ui.edit.text.cut.line" commandName="Cut Line" description="Cut a line of text" category="_2vkMrkQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voecEQHEfC4A_fUchu1mw" elementId="org.omnetpp.eventlogtable.findNext" commandName="Find Next" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voecUQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.edit.text.select.columnPrevious" commandName="Select Previous Column" description="Select the previous column" category="_2vkMrkQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voeckQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.file.closeAll" commandName="Close All" description="Close all editors" category="_2vkMqEQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voec0QHEfC4A_fUchu1mw" elementId="org.eclipse.ui.edit.text.cut.line.to.beginning" commandName="Cut to Beginning of Line" description="Cut to the beginning of a line of text" category="_2vkMrkQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voedEQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.codan.commands.runCodanCommand" commandName="Run Code Analysis" category="_2vkMokQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voedUQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.ui.edit.text.rename.element" commandName="Rename - Refactoring " description="Rename the selected element" category="_2vkMo0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voedkQHEfC4A_fUchu1mw" elementId="org.omnetpp.eventlogtable.gotoSimulationTime" commandName="Go to Simulation Time..." category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voed0QHEfC4A_fUchu1mw" elementId="org.eclipse.ui.edit.text.select.wordNext" commandName="Select Next Word" description="Select the next word" category="_2vkMrkQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voeeEQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.navigate.goToResource" commandName="Go to" description="Go to a particular resource in the active view" category="_2vkMu0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voeeUQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.window.spy" commandName="Show Contributing Plug-in" description="Shows contribution information for the currently selected element" category="_2vkMoUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voeekQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.team.submodule.sync" commandName="Sync Submodule" category="_2vkMq0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voee0QHEfC4A_fUchu1mw" elementId="org.eclipse.ui.window.quickAccess" commandName="Quick Access" description="Quickly access UI elements" category="_2vkMoUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voefEQHEfC4A_fUchu1mw" elementId="org.omnetpp.sequencechart.refresh" commandName="Refresh" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voefUQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.dsf.debug.ui.refreshAll" commandName="Refresh Debug Views" description="Refresh all data in debug views" category="_2vkMqUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voefkQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.team.CompareIndexWithHead" commandName="Compare File in Git Index with HEAD Revision" category="_2vkMq0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voef0QHEfC4A_fUchu1mw" elementId="org.eclipse.debug.ui.commands.StepReturn" commandName="Step Return" description="Step return" category="_2vkMqUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voegEQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.navigate.addToWorkingSet" commandName="Add to Working Set" description="Adds the selected object to a working set." category="_2vkMsEQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voegUQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.edit.text.goto.lineEnd" commandName="Line End" description="Go to the end of the line of text" category="_2vkMrkQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voegkQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.team.CreatePatch" commandName="Create Patch" category="_2vkMq0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voeg0QHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.RepositoriesViewImportProjects" commandName="Import Projects..." description="Import or create in local Git repository" category="_2vkMq0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voehEQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.navigate.showIn" commandName="Show In" category="_2vkMu0QHEfC4A_fUchu1mw">
    <parameters xmi:id="_2voehUQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.navigate.showIn.targetId" name="Show In Target Id" optional="false"/>
  </commands>
  <commands xmi:id="_2voehkQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.edit.text.delimiter.unix" commandName="Convert Line Delimiters to Unix (LF, \n, 0A, &#xb6;)" description="Converts the line delimiters to Unix (LF, \n, 0A, &#xb6;)" category="_2vkMqEQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voeh0QHEfC4A_fUchu1mw" elementId="org.eclipse.ui.dialogs.openMessageDialog" commandName="Open Message Dialog" description="Open a Message Dialog" category="_2vkMrEQHEfC4A_fUchu1mw">
    <parameters xmi:id="_2voeiEQHEfC4A_fUchu1mw" elementId="title" name="Title"/>
    <parameters xmi:id="_2voeiUQHEfC4A_fUchu1mw" elementId="message" name="Message"/>
    <parameters xmi:id="_2voeikQHEfC4A_fUchu1mw" elementId="imageType" name="Image Type Constant" typeId="org.eclipse.ui.dialogs.Integer"/>
    <parameters xmi:id="_2voei0QHEfC4A_fUchu1mw" elementId="defaultIndex" name="Default Button Index" typeId="org.eclipse.ui.dialogs.Integer"/>
    <parameters xmi:id="_2voejEQHEfC4A_fUchu1mw" elementId="buttonLabel0" name="First Button Label"/>
    <parameters xmi:id="_2voejUQHEfC4A_fUchu1mw" elementId="buttonLabel1" name="Second Button Label"/>
    <parameters xmi:id="_2voejkQHEfC4A_fUchu1mw" elementId="buttonLabel2" name="Third Button Label"/>
    <parameters xmi:id="_2voej0QHEfC4A_fUchu1mw" elementId="buttonLabel3" name="Fourth Button Label"/>
    <parameters xmi:id="_2voekEQHEfC4A_fUchu1mw" elementId="cancelReturns" name="Return Value on Cancel"/>
  </commands>
  <commands xmi:id="_2voekUQHEfC4A_fUchu1mw" elementId="org.omnetpp.eventlogtable.gotoMessageArrival" commandName="Go to Message Arrival" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voekkQHEfC4A_fUchu1mw" elementId="org.eclipse.equinox.p2.ui.discovery.commands.ShowRepositoryCatalog" commandName="Show Repository Catalog" category="_2vkMsUQHEfC4A_fUchu1mw">
    <parameters xmi:id="_2voek0QHEfC4A_fUchu1mw" elementId="org.eclipse.equinox.p2.ui.discovery.commands.RepositoryParameter" name="P2 Repository URI"/>
  </commands>
  <commands xmi:id="_2voelEQHEfC4A_fUchu1mw" elementId="org.eclipse.debug.ui.commands.OpenDebugConfigurations" commandName="Debug..." description="Open debug launch configuration dialog" category="_2vkMqUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voelUQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.window.nextPerspective" commandName="Next Perspective" description="Switch to the next perspective" category="_2vkMoUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voelkQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.edit.text.hippieCompletion" commandName="Word Completion" description="Context insensitive completion" category="_2vkMsEQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voel0QHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.RepositoriesViewClearCredentials" commandName="Clear Credentials" category="_2vkMq0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voemEQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.ui.edit.open.include.browser" commandName="Open Include Browser" description="Open an include browser on the selected element" category="_2vkMu0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voemUQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.make.ui.edit.text.makefile.opendecl" commandName="Open declaration" description="Follow to the directive definition" category="_2vkMp0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voemkQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.file.closeOthers" commandName="Close Others" description="Close all editors except the one that is active" category="_2vkMqEQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voem0QHEfC4A_fUchu1mw" elementId="org.omnetpp.neddoc.commands.neddocCommand" commandName="Generate NED Documentation..." category="_2vkMpEQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voenEQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.editors.revisions.author.toggle" commandName="Toggle Revision Author Display" description="Toggles the display of the revision author" category="_2vkMrkQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voenUQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.history.Reword" commandName="Reword Commit" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voenkQHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.RepositoriesViewConfigureFetch" commandName="Configure Fetch..." category="_2vkMq0QHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voen0QHEfC4A_fUchu1mw" elementId="org.eclipse.debug.ui.commands.Resume" commandName="Resume" description="Resume" category="_2vkMqUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_2voeoEQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.ui.menu.createParserLog" commandName="Create Parser Log File" category="_2vkMpEQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_23R-MEQHEfC4A_fUchu1mw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.debugActionSet/org.eclipse.cdt.debug.ui.actions.ResumeAtLine" commandName="Resume At Line (C/C++)" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_23SlQEQHEfC4A_fUchu1mw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.debugActionSet/org.eclipse.cdt.debug.ui.actions.MoveToLine" commandName="Move to Line (C/C++)" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_23SlQUQHEfC4A_fUchu1mw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.debugActionSet/org.eclipse.cdt.debug.ui.actions.ToggleInstructionStepMode" commandName="Instruction Stepping Mode" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_23SlQkQHEfC4A_fUchu1mw" elementId="AUTOGEN:::org.eclipse.cdt.make.ui.updateActionSet/org.eclipse.cdt.make.ui.UpdateMakeAction" commandName="Update Old Make Project..." description="Update Old Make Project" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_23TzYEQHEfC4A_fUchu1mw" elementId="AUTOGEN:::org.eclipse.cdt.make.ui.makeTargetActionSet/org.eclipse.cdt.make.ui.actions.buildLastTargetAction" commandName="Rebuild Last Target" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_23TzYUQHEfC4A_fUchu1mw" elementId="AUTOGEN:::org.eclipse.cdt.make.ui.makeTargetActionSet/org.eclipse.cdt.make.ui.makeTargetAction" commandName="Build..." category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_23TzYkQHEfC4A_fUchu1mw" elementId="org.eclipse.ltk.ui.refactor.show.refactoring.history" commandName="History..." category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_23UacEQHEfC4A_fUchu1mw" elementId="org.eclipse.ltk.ui.refactor.create.refactoring.script" commandName="Create Script..." category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_23UacUQHEfC4A_fUchu1mw" elementId="org.eclipse.ltk.ui.refactor.apply.refactoring.script" commandName="Apply Script..." category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_23UackQHEfC4A_fUchu1mw" elementId="AUTOGEN:::org.eclipse.cdt.ui.SearchActionSet/org.eclipse.cdt.ui.actions.OpenCSearchPage" commandName="C/C++..." category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_23Uac0QHEfC4A_fUchu1mw" elementId="AUTOGEN:::org.eclipse.cdt.ui.buildConfigActionSet/org.eclipse.cdt.ui.buildActiveConfigToolbarAction" commandName="Build Active Configuration" description="Build the active configurations of selected projects" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_23UadEQHEfC4A_fUchu1mw" elementId="AUTOGEN:::org.eclipse.cdt.ui.buildConfigActionSet/org.eclipse.cdt.ui.buildConfigToolbarAction" commandName="Active Build Configuration" description="Manage configurations for the current project" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_23UadUQHEfC4A_fUchu1mw" elementId="AUTOGEN:::org.eclipse.cdt.ui.buildConfigActionSet/org.eclipse.cdt.ui.manageConfigsAction2" commandName="Manage..." category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_23UadkQHEfC4A_fUchu1mw" elementId="AUTOGEN:::org.eclipse.cdt.ui.buildConfigActionSet/org.eclipse.cdt.ui.buildConfigMenuAction" commandName="Set Active" description="Change active build configuration for project(s)" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_23Uad0QHEfC4A_fUchu1mw" elementId="AUTOGEN:::org.eclipse.cdt.ui.buildConfigActionSet/org.eclipse.cdt.ui.wsselection" commandName="Manage Working Sets..." category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_23UaeEQHEfC4A_fUchu1mw" elementId="AUTOGEN:::org.eclipse.cdt.ui.CElementCreationActionSet/org.eclipse.cdt.ui.actions.NewTypeDropDown" commandName="Class..." description="New C++ Class" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_23UaeUQHEfC4A_fUchu1mw" elementId="AUTOGEN:::org.eclipse.cdt.ui.CElementCreationActionSet/org.eclipse.cdt.ui.actions.NewFileDropDown" commandName="Source File..." description="New C/C++ Source File" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_23UaekQHEfC4A_fUchu1mw" elementId="AUTOGEN:::org.eclipse.cdt.ui.CElementCreationActionSet/org.eclipse.cdt.ui.actions.NewFolderDropDown" commandName="Source Folder..." description="New C/C++ Source Folder" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_23Uae0QHEfC4A_fUchu1mw" elementId="AUTOGEN:::org.eclipse.cdt.ui.CElementCreationActionSet/org.eclipse.cdt.ui.actions.NewProjectDropDown" commandName="Project..." description="New C/C++ Project" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_23VBgEQHEfC4A_fUchu1mw" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunWithConfigurationAction" commandName="Run As" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_23VBgUQHEfC4A_fUchu1mw" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunHistoryMenuAction" commandName="Run History" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_23VBgkQHEfC4A_fUchu1mw" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunDropDownAction" commandName="Run" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_23VBg0QHEfC4A_fUchu1mw" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugWithConfigurationAction" commandName="Debug As" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_23VBhEQHEfC4A_fUchu1mw" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugHistoryMenuAction" commandName="Debug History" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_23VokEQHEfC4A_fUchu1mw" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugDropDownAction" commandName="Debug" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_23VokUQHEfC4A_fUchu1mw" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileDropDownAction" commandName="Profile" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_23VokkQHEfC4A_fUchu1mw" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileWithConfigurationAction" commandName="Profile As" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_23Vok0QHEfC4A_fUchu1mw" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileHistoryMenuAction" commandName="Profile History" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_23VolEQHEfC4A_fUchu1mw" elementId="AUTOGEN:::org.eclipse.ui.cheatsheets.actionSet/org.eclipse.ui.cheatsheets.actions.CheatSheetHelpMenuAction" commandName="Cheat Sheets..." category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_23VolUQHEfC4A_fUchu1mw" elementId="AUTOGEN:::org.eclipse.search.searchActionSet/org.eclipse.search.OpenSearchDialogPage" commandName="Search..." description="Search" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_23VolkQHEfC4A_fUchu1mw" elementId="AUTOGEN:::org.eclipse.team.ui.actionSet/org.eclipse.team.ui.synchronizeAll" commandName="Synchronize..." description="Synchronize..." category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_23Vol0QHEfC4A_fUchu1mw" elementId="AUTOGEN:::org.eclipse.team.ui.actionSet/org.eclipse.team.ui.ConfigureProject" commandName="Share Project..." description="Share the project with others using a version and configuration management system." category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_23WPoEQHEfC4A_fUchu1mw" elementId="AUTOGEN:::org.eclipse.ui.externaltools.ExternalToolsSet/org.eclipse.ui.externaltools.ExternalToolMenuDelegateMenu" commandName="External Tools" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_23WPoUQHEfC4A_fUchu1mw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.CEditor.BreakpointRulerActions/org.eclipse.cdt.debug.ui.CEditor.RulerTobbleBreakpointAction" commandName="%Dummy.label" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_23WPokQHEfC4A_fUchu1mw" elementId="AUTOGEN:::org.eclipse.cdt.ui.editor.asm.AsmEditor.BreakpointRulerActions/org.eclipse.cdt.debug.ui.CEditor.RulerTobbleBreakpointAction" commandName="%Dummy.label" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_23WPo0QHEfC4A_fUchu1mw" elementId="AUTOGEN:::org.eclipse.ui.texteditor.ruler.actions/org.eclipse.ui.texteditor.BookmarkRulerAction" commandName="dummy" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_23WPpEQHEfC4A_fUchu1mw" elementId="AUTOGEN:::org.eclipse.ui.texteditor.ruler.actions/org.eclipse.cdt.internal.ui.text.correction.CSelectRulerAction" commandName="dummy" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_23WPpUQHEfC4A_fUchu1mw" elementId="AUTOGEN:::org.eclipse.ui.texteditor.ruler.actions/org.eclipse.ui.texteditor.SelectRulerAction" commandName="Text Editor Ruler Single-Click" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_23WPpkQHEfC4A_fUchu1mw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.debugview.toolbar/org.eclipse.cdt.debug.internal.ui.actions.ToggleInstructionStepModeActionDelegate" commandName="Instruction Stepping Mode" description="Instruction Stepping Mode" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_23W2sEQHEfC4A_fUchu1mw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.debugView.menu/org.eclipse.cdt.debug.internal.ui.actions.ShowFullPathsAction" commandName="Show Full Paths" description="Show Full Paths" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_23W2sUQHEfC4A_fUchu1mw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.breakpointView.menu/org.eclipse.cdt.debug.internal.ui.actions.ShowFullPathsAction" commandName="Show Full Paths" description="Show Full Paths" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_23W2skQHEfC4A_fUchu1mw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.breakpointView.menu/org.eclipse.cdt.debug.ui.addWatchpoint" commandName="Add Watchpoint (C/C++)..." description="Add Watchpoint (C/C++)" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_23W2s0QHEfC4A_fUchu1mw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.breakpointView.menu/org.eclipse.cdt.debug.internal.ui.actions.AddEventBreakpointActionDelegate" commandName="Add Event Breakpoint (C/C++)..." description="Add Event Breakpoint (C/C++)" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_23W2tEQHEfC4A_fUchu1mw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.breakpointView.menu/org.eclipse.cdt.debug.ui.addFunctionBreakpoint" commandName="Add Function Breakpoint (C/C++)..." description="Add Function Breakpoint (C/C++)" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_23W2tUQHEfC4A_fUchu1mw" elementId="AUTOGEN:::org.eclipse.debug.ui.variablesView.toolbar/org.eclipse.cdt.debug.internal.ui.actions.RemoveAllGlobalsActionDelegate" commandName="Remove All Global Variables" description="Remove All Global Variables" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_23W2tkQHEfC4A_fUchu1mw" elementId="AUTOGEN:::org.eclipse.debug.ui.variablesView.toolbar/org.eclipse.cdt.debug.internal.ui.actions.RemoveGlobalsActionDelegate" commandName="Remove Global Variables" description="Remove Selected Global Variables" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_23W2t0QHEfC4A_fUchu1mw" elementId="AUTOGEN:::org.eclipse.debug.ui.variablesView.toolbar/org.eclipse.cdt.debug.internal.ui.actions.AddGlobalsActionDelegate" commandName="Add Global Variables..." description="Add Global Variables" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_23W2uEQHEfC4A_fUchu1mw" elementId="AUTOGEN:::org.eclipse.debug.ui.modulesView.toolbar/org.eclipse.cdt.debug.ui.LoadSymbolsForAllAction" commandName="Load Symbols For All" description="Load Symbols For All Modules" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_23W2uUQHEfC4A_fUchu1mw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.expression.toolbar/org.eclipse.pinclone.expression.pinDebugContext" commandName="Pin to Debug Context" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_23W2ukQHEfC4A_fUchu1mw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.expression.toolbar/org.eclipse.pinclone.expression.clone" commandName="Open New View" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_23W2u0QHEfC4A_fUchu1mw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.variable.toolbar/org.eclipse.pinclone.variable.pinDebugContext" commandName="Pin to Debug Context" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_23XdwEQHEfC4A_fUchu1mw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.variable.toolbar/org.eclipse.pinclone.variable.clone" commandName="Open New View" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_23XdwUQHEfC4A_fUchu1mw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.register.toolbar/org.eclipse.pinclone.register.pinDebugContext" commandName="Pin to Debug Context" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_23XdwkQHEfC4A_fUchu1mw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.register.toolbar/org.eclipse.pinclone.register.clone" commandName="Open New View" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_23Xdw0QHEfC4A_fUchu1mw" elementId="AUTOGEN:::org.eclipse.cdt.dsf.debug.ui.viewmodel.variables.update.Refresh/org.eclipse.cdt.dsf.debug.ui.variables.viewmodel.update.actions.refresh" commandName="Refresh" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_23XdxEQHEfC4A_fUchu1mw" elementId="AUTOGEN:::org.eclipse.cdt.dsf.debug.ui.viewmodel.registers.update.Refresh/org.eclipse.cdt.dsf.debug.ui.registers.viewmodel.update.actions.refresh" commandName="Refresh" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_23XdxUQHEfC4A_fUchu1mw" elementId="AUTOGEN:::org.eclipse.cdt.dsf.debug.ui.viewmodel.expressions.update.Refresh/org.eclipse.cdt.dsf.debug.ui.expressions.viewmodel.update.actions.refresh" commandName="Refresh" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_23XdxkQHEfC4A_fUchu1mw" elementId="AUTOGEN:::org.eclipse.cdt.dsf.debug.ui.viewmodel.debugview.update.Refresh/org.eclipse.cdt.dsf.debug.ui.debugview.viewmodel.update.actions.refresh" commandName="Refresh" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_23Xdx0QHEfC4A_fUchu1mw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.disassembly.toolbar/org.eclipse.pinclone.disassembly.pinDebugContext" commandName="Pin to Debug Context" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_23XdyEQHEfC4A_fUchu1mw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.disassembly.toolbar/org.eclipse.pinclone.disassembly.clone" commandName="Open New View" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_23XdyUQHEfC4A_fUchu1mw" elementId="AUTOGEN:::org.eclipse.debug.ui.PulldownActions/org.eclipse.debug.ui.debugview.pulldown.ViewManagementAction" commandName="View Management..." category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_23XdykQHEfC4A_fUchu1mw" elementId="AUTOGEN:::org.eclipse.debug.ui.debugview.toolbar/org.eclipse.debug.ui.debugview.toolbar.removeAllTerminated" commandName="Remove All Terminated" description="Remove All Terminated Launches" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_23Xdy0QHEfC4A_fUchu1mw" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.removeAll" commandName="Remove All" description="Remove All Breakpoints" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_23XdzEQHEfC4A_fUchu1mw" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.linkWithDebugView" commandName="Link with Debug View" description="Link with Debug View" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_23XdzUQHEfC4A_fUchu1mw" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.workingSets" commandName="Working Sets..." description="Manage Working Sets" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_23XdzkQHEfC4A_fUchu1mw" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.clearDefaultBreakpointGroup" commandName="Deselect Default Working Set" description="Deselect Default Working Set" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_23YE0EQHEfC4A_fUchu1mw" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.setDefaultBreakpointGroup" commandName="Select Default Working Set..." description="Select Default Working Set" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_23YE0UQHEfC4A_fUchu1mw" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.groupByAction" commandName="Group By" description="Show" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_23YE0kQHEfC4A_fUchu1mw" elementId="AUTOGEN:::org.eclipse.debug.ui.expressionsView.toolbar/org.eclipse.debug.ui.expresssionsView.toolbar.removeAll" commandName="Remove All" description="Remove All Expressions" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_23YE00QHEfC4A_fUchu1mw" elementId="AUTOGEN:::org.eclipse.debug.ui.expressionsView.toolbar/org.eclipse.debug.ui.expresssionsView.toolbar.AddWatchExpression" commandName="Add Watch Expression..." description="Create a new watch expression" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_23YE1EQHEfC4A_fUchu1mw" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.PinMemoryBlockAction" commandName="Pin Memory Monitor" description="Pin Memory Monitor" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_23YE1UQHEfC4A_fUchu1mw" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.NewMemoryViewAction" commandName="New Memory View" description="New Memory View" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_23YE1kQHEfC4A_fUchu1mw" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.togglemonitors" commandName="Toggle Memory Monitors Pane" description="Toggle Memory Monitors Pane" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_23YE10QHEfC4A_fUchu1mw" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.linkrenderingpanes" commandName="Link Memory Rendering Panes" description="Link Memory Rendering Panes" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_23YE2EQHEfC4A_fUchu1mw" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.tablerendering.preferencesaction" commandName="Table Renderings Preferences..." description="&amp;Table Renderings Preferences..." category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_23YE2UQHEfC4A_fUchu1mw" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.togglesplitpane" commandName="Toggle Split Pane" description="Toggle Split Pane" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_23YE2kQHEfC4A_fUchu1mw" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.switchMemoryBlock" commandName="Switch Memory Monitor" description="Switch Memory Monitor" category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <commands xmi:id="_23YE20QHEfC4A_fUchu1mw" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.memoryViewPreferencesAction" commandName="Preferences..." description="&amp;Preferences..." category="_2vkMsUQHEfC4A_fUchu1mw"/>
  <addons xmi:id="_2vUVDEQHEfC4A_fUchu1mw" elementId="org.eclipse.e4.core.commands.service" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.core.commands/org.eclipse.e4.core.commands.CommandServiceAddon"/>
  <addons xmi:id="_2vUVDUQHEfC4A_fUchu1mw" elementId="org.eclipse.e4.ui.contexts.service" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.services/org.eclipse.e4.ui.services.ContextServiceAddon"/>
  <addons xmi:id="_2vUVDkQHEfC4A_fUchu1mw" elementId="org.eclipse.e4.ui.bindings.service" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.bindings/org.eclipse.e4.ui.bindings.BindingServiceAddon"/>
  <addons xmi:id="_2vUVD0QHEfC4A_fUchu1mw" elementId="org.eclipse.e4.ui.workbench.commands.model" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.CommandProcessingAddon"/>
  <addons xmi:id="_2vUVEEQHEfC4A_fUchu1mw" elementId="org.eclipse.e4.ui.workbench.contexts.model" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.ContextProcessingAddon"/>
  <addons xmi:id="_2vUVEUQHEfC4A_fUchu1mw" elementId="org.eclipse.e4.ui.workbench.bindings.model" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.swt/org.eclipse.e4.ui.workbench.swt.util.BindingProcessingAddon"/>
  <addons xmi:id="_2vUVEkQHEfC4A_fUchu1mw" elementId="Cleanup Addon" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.cleanupaddon.CleanupAddon"/>
  <addons xmi:id="_2vUVE0QHEfC4A_fUchu1mw" elementId="DnD Addon" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.dndaddon.DnDAddon"/>
  <addons xmi:id="_2vUVFEQHEfC4A_fUchu1mw" elementId="MinMax Addon" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.MinMaxAddon"/>
  <addons xmi:id="_2vUVFUQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.workbench.addon.0" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.HandlerProcessingAddon"/>
  <addons xmi:id="_2v91QEQHEfC4A_fUchu1mw" elementId="SplitterAddon" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.splitteraddon.SplitterAddon"/>
  <categories xmi:id="_2vkMoEQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.category.perspectives" name="Perspectives" description="Commands for opening perspectives"/>
  <categories xmi:id="_2vkMoUQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.category.window" name="Window"/>
  <categories xmi:id="_2vkMokQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.codan.ui.commands.category" name="Code Analysis"/>
  <categories xmi:id="_2vkMo0QHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.ui.category.refactoring" name="Refactor - C++" description="C/C++ Refactorings"/>
  <categories xmi:id="_2vkMpEQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.category.project" name="Project"/>
  <categories xmi:id="_2vkMpUQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.category.views" name="Views" description="Commands for opening views"/>
  <categories xmi:id="_2vkMpkQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.ide.markerContents" name="Contents" description="The category for menu contents"/>
  <categories xmi:id="_2vkMp0QHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.make.ui.category.source" name="Makefile Source" description="Makefile Source Actions"/>
  <categories xmi:id="_2vkMqEQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.category.file" name="File"/>
  <categories xmi:id="_2vkMqUQHEfC4A_fUchu1mw" elementId="org.eclipse.debug.ui.category.run" name="Run/Debug" description="Run/Debug command category"/>
  <categories xmi:id="_2vkMqkQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.debug.ui.category.casting" name="Cast to Type or Array" description="Set of commands for displaying variables and expressions as other types or arrays."/>
  <categories xmi:id="_2vkMq0QHEfC4A_fUchu1mw" elementId="org.eclipse.egit.ui.commandCategory" name="Git"/>
  <categories xmi:id="_2vkMrEQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.category.dialogs" name="Dialogs" description="Commands for opening dialogs"/>
  <categories xmi:id="_2vkMrUQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.debug.ui.category.debugViewLayout" name="Debug View Layout Commands" description="Set of commands for controlling the Debug View Layout"/>
  <categories xmi:id="_2vkMrkQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.category.textEditor" name="Text Editing" description="Text Editing Commands"/>
  <categories xmi:id="_2vkMr0QHEfC4A_fUchu1mw" elementId="org.eclipse.search.ui.category.search" name="Search" description="Search command category"/>
  <categories xmi:id="_2vkMsEQHEfC4A_fUchu1mw" elementId="org.eclipse.ui.category.edit" name="Edit"/>
  <categories xmi:id="_2vkMsUQHEfC4A_fUchu1mw" elementId="org.eclipse.core.commands.categories.autogenerated" name="Uncategorized" description="Commands that were either auto-generated or have no category"/>
  <categories xmi:id="_2vkMskQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.debug.ui.category.reverseDebugging" name="Reverse Debugging Commands" description="Set of commands for Reverse Debugging"/>
  <categories xmi:id="_2vkMs0QHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.ui.category.source" name="C/C++ Source" description="C/C++ Source Actions"/>
  <categories xmi:id="_2vkMtEQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.debug.ui.category.runControl" name="Run Control Commands" description="Set of commands for Run Control"/>
  <categories xmi:id="_2vkMtUQHEfC4A_fUchu1mw" elementId="org.eclipse.gef.category.view" name="View" description="View"/>
  <categories xmi:id="_2vkMtkQHEfC4A_fUchu1mw" elementId="org.eclipse.team.ui.category.team" name="Team" description="Actions that apply when working with a Team"/>
  <categories xmi:id="_2vkMt0QHEfC4A_fUchu1mw" elementId="org.eclipse.ui.category.help" name="Help"/>
  <categories xmi:id="_2vkMuEQHEfC4A_fUchu1mw" elementId="org.eclipse.compare.ui.category.compare" name="Compare" description="Compare command category"/>
  <categories xmi:id="_2vkMuUQHEfC4A_fUchu1mw" elementId="org.eclipse.cdt.debug.ui.category.tracing" name="Tracing Commands" description="Category for Tracing Commands"/>
  <categories xmi:id="_2vkMukQHEfC4A_fUchu1mw" elementId="org.eclipse.ltk.ui.category.refactoring" name="Refactoring"/>
  <categories xmi:id="_2vkMu0QHEfC4A_fUchu1mw" elementId="org.eclipse.ui.category.navigate" name="Navigate"/>
</application:Application>
