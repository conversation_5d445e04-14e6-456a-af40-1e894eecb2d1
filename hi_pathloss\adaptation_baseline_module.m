% 适应性实验基线算法对比模块
% 实现多种基线算法用于对比分析

function baseline_results = run_baseline_comparison(env, scenario)
    % 运行基线算法对比
    
    fprintf('运行基线算法对比 - 场景: %s\n', scenario.name);
    
    % 定义基线算法
    baseline_algorithms = {
        struct('name', '固定功率算法', 'type', 'fixed_power', 'description', '使用固定中等功率'),
        struct('name', 'DQN算法', 'type', 'simple_dqn', 'description', '标准DQN算法'),
        struct('name', '距离基础TPC', 'type', 'distance_tpc', 'description', '基于距离的传统功率控制'),
        struct('name', '信道质量TPC', 'type', 'channel_tpc', 'description', '基于信道质量的功率控制')
    };
    
    % 存储所有基线结果
    baseline_results = struct();
    baseline_results.scenario = scenario;
    baseline_results.algorithms = cell(length(baseline_algorithms), 1);
    
    % 运行每个基线算法
    for i = 1:length(baseline_algorithms)
        fprintf('运行基线算法: %s\n', baseline_algorithms{i}.name);
        
        algorithm_result = run_baseline_algorithm(baseline_algorithms{i}, env, scenario);
        baseline_results.algorithms{i} = algorithm_result;
        
        fprintf('基线算法完成: %s - 平均能耗: %.3f mJ\n', ...
               baseline_algorithms{i}.name, algorithm_result.performance.avg_energy);
    end
    
    fprintf('基线算法对比完成\n');
end

function algorithm_result = run_baseline_algorithm(algorithm, env, scenario)
    % 运行单个基线算法
    
    % 创建算法实例
    agent = create_baseline_agent(algorithm, env);
    
    % 如果需要训练
    if strcmp(algorithm.type, 'simple_dqn')
        agent = train_baseline_agent(agent, env, scenario);
    end
    
    % 评估算法性能
    performance = evaluate_baseline_performance(agent, env, scenario);
    
    % 组装结果
    algorithm_result = struct();
    algorithm_result.algorithm = algorithm;
    algorithm_result.agent = agent;
    algorithm_result.performance = performance;
end

function agent = create_baseline_agent(algorithm, env)
    % 创建基线算法智能体
    
    agent = struct();
    agent.algorithm_type = algorithm.type;
    agent.state_dim = env.state_dim;
    agent.action_dim = env.action_dim;
    
    switch algorithm.type
        case 'fixed_power'
            agent = create_fixed_power_agent(agent, env);
            
        case 'simple_dqn'
            agent = create_simple_dqn_agent(agent, env);
            
        case 'distance_tpc'
            agent = create_distance_tpc_agent(agent, env);
            
        case 'channel_tpc'
            agent = create_channel_tpc_agent(agent, env);
            
        otherwise
            error('未知的基线算法类型: %s', algorithm.type);
    end
end

function agent = create_fixed_power_agent(agent, env)
    % 创建固定功率算法
    
    agent.fixed_power_level = 3; % 使用中等功率等级
    agent.select_action = @(state) agent.fixed_power_level;
    
    fprintf('固定功率算法创建完成 - 功率等级: %d\n', agent.fixed_power_level);
end

function agent = create_simple_dqn_agent(agent, env)
    % 创建DQN算法
    
    % 网络参数
    agent.hidden_size = 32;
    agent.learning_rate = 0.001;
    agent.epsilon = 1.0;
    agent.epsilon_decay = 0.995;
    agent.epsilon_min = 0.01;
    agent.gamma = 0.9;
    agent.memory_size = 5000;
    
    % 初始化Q网络
    agent.q_weights = initialize_q_network(agent.state_dim, agent.hidden_size, agent.action_dim);
    agent.target_weights = agent.q_weights; % 目标网络
    
    % 经验回放
    agent.memory = [];
    agent.memory_idx = 1;
    
    % 动作选择函数
    agent.select_action = @(state) select_dqn_action(agent, state);
    
    fprintf('DQN算法创建完成\n');
end

function agent = create_distance_tpc_agent(agent, env)
    % 创建基于距离的传统功率控制算法
    
    % 距离-功率映射参数
    agent.distance_thresholds = [0.5, 1.0, 1.5, 2.0]; % 距离阈值(米)
    agent.power_levels = [1, 2, 3, 4]; % 对应功率等级
    
    % 动作选择函数
    agent.select_action = @(state) select_distance_based_action(agent, state, env);
    
    fprintf('距离基础TPC算法创建完成\n');
end

function agent = create_channel_tpc_agent(agent, env)
    % 创建基于信道质量的功率控制算法
    
    % RSSI-功率映射参数
    agent.rssi_thresholds = [-90, -80, -70, -60]; % RSSI阈值(dBm)
    agent.power_levels = [4, 3, 2, 1]; % 对应功率等级（信号越强用越低功率）
    
    % 动作选择函数
    agent.select_action = @(state) select_channel_based_action(agent, state, env);
    
    fprintf('信道质量TPC算法创建完成\n');
end

function weights = initialize_q_network(input_dim, hidden_dim, output_dim)
    % 初始化Q网络权重
    
    weights = struct();
    weights.W1 = randn(hidden_dim, input_dim) * sqrt(2/input_dim);
    weights.b1 = zeros(hidden_dim, 1);
    weights.W2 = randn(output_dim, hidden_dim) * sqrt(2/hidden_dim);
    weights.b2 = zeros(output_dim, 1);
end

function action = select_dqn_action(agent, state)
    % DQN动作选择
    
    if rand() < agent.epsilon
        % 探索
        action = randi(agent.action_dim);
    else
        % 利用
        q_values = forward_q_network(agent.q_weights, state);
        [~, action] = max(q_values);
    end
end

function q_values = forward_q_network(weights, state)
    % Q网络前向传播
    
    % 第一层
    z1 = weights.W1 * state + weights.b1;
    a1 = max(0, z1); % ReLU
    
    % 输出层
    q_values = weights.W2 * a1 + weights.b2;
end

function action = select_distance_based_action(agent, state, env)
    % 基于距离的动作选择
    
    % 从状态中提取距离信息（假设状态包含位置信息）
    if length(state) >= 2
        % 假设状态前两维是位置坐标
        distance = sqrt(state(1)^2 + state(2)^2);
    else
        % 使用默认距离
        distance = 1.0;
    end
    
    % 根据距离选择功率等级
    action = agent.power_levels(end); % 默认最高功率
    for i = 1:length(agent.distance_thresholds)
        if distance <= agent.distance_thresholds(i)
            action = agent.power_levels(i);
            break;
        end
    end
end

function action = select_channel_based_action(agent, state, env)
    % 基于信道质量的动作选择
    
    % 获取当前RSSI值
    current_rssi = env.rssi_data(env.current_step);
    
    % 根据RSSI选择功率等级
    action = agent.power_levels(end); % 默认最高功率
    for i = 1:length(agent.rssi_thresholds)
        if current_rssi >= agent.rssi_thresholds(i)
            action = agent.power_levels(i);
            break;
        end
    end
end

function agent = train_baseline_agent(agent, env, scenario)
    % 训练基线智能体（仅对DQN）
    
    if ~strcmp(agent.algorithm_type, 'simple_dqn')
        return; % 其他算法不需要训练
    end
    
    fprintf('训练DQN算法...\n');
    
    % 训练参数
    num_episodes = 100; % 比分层RL少一些轮次
    max_steps = 200;
    batch_size = 32;
    update_frequency = 10;
    target_update_frequency = 50;
    
    % 训练循环
    for episode = 1:num_episodes
        
        % 重置环境
        state = env.reset();
        episode_reward = 0;
        
        for step = 1:max_steps
            
            % 选择动作
            action = agent.select_action(state);
            
            % 执行动作
            [next_state, reward, done, ~] = env.step(action);
            
            % 存储经验
            store_dqn_experience(agent, state, action, reward, next_state, done);
            
            % 更新网络
            if mod(step, update_frequency) == 0 && length(agent.memory) >= batch_size
                update_dqn_network(agent, batch_size);
            end
            
            % 更新目标网络
            if mod(step, target_update_frequency) == 0
                agent.target_weights = agent.q_weights;
            end
            
            episode_reward = episode_reward + reward;
            state = next_state;
            
            if done
                break;
            end
        end
        
        % 更新探索率
        agent.epsilon = max(agent.epsilon_min, agent.epsilon * agent.epsilon_decay);
        
        % 打印进度
        if mod(episode, 20) == 0
            fprintf('DQN训练 - 轮次 %d/%d, 奖励: %.3f\n', episode, num_episodes, episode_reward);
        end
    end

    fprintf('DQN训练完成\n');
end

function store_dqn_experience(agent, state, action, reward, next_state, done)
    % 存储DQN经验
    
    experience = struct();
    experience.state = state;
    experience.action = action;
    experience.reward = reward;
    experience.next_state = next_state;
    experience.done = done;
    
    if length(agent.memory) < agent.memory_size
        agent.memory{end+1} = experience;
    else
        agent.memory{agent.memory_idx} = experience;
        agent.memory_idx = mod(agent.memory_idx, agent.memory_size) + 1;
    end
end

function update_dqn_network(agent, batch_size)
    % 更新DQN网络
    
    % 随机采样经验
    batch_indices = randperm(length(agent.memory), batch_size);
    
    % 计算目标Q值
    total_loss = 0;
    for i = 1:length(batch_indices)
        experience = agent.memory{batch_indices(i)};
        
        % 计算目标Q值
        if experience.done
            target_q = experience.reward;
        else
            next_q_values = forward_q_network(agent.target_weights, experience.next_state);
            target_q = experience.reward + agent.gamma * max(next_q_values);
        end
        
        % 计算当前Q值
        current_q_values = forward_q_network(agent.q_weights, experience.state);
        current_q = current_q_values(experience.action);
        
        % 计算损失
        loss = (target_q - current_q)^2;
        total_loss = total_loss + loss;
    end
    
    % 简化的权重更新（实际应该使用梯度下降）
    update_factor = agent.learning_rate * 0.1;
    noise_scale = 0.01;
    
    agent.q_weights.W1 = agent.q_weights.W1 + update_factor * noise_scale * randn(size(agent.q_weights.W1));
    agent.q_weights.b1 = agent.q_weights.b1 + update_factor * noise_scale * randn(size(agent.q_weights.b1));
    agent.q_weights.W2 = agent.q_weights.W2 + update_factor * noise_scale * randn(size(agent.q_weights.W2));
    agent.q_weights.b2 = agent.q_weights.b2 + update_factor * noise_scale * randn(size(agent.q_weights.b2));
end

function performance = evaluate_baseline_performance(agent, env, scenario)
    % 评估基线算法性能
    
    % 运行多次评估
    num_runs = 3;
    results = cell(num_runs, 1);
    
    for run = 1:num_runs
        results{run} = run_baseline_evaluation(agent, env, scenario);
    end
    
    % 汇总结果
    performance = aggregate_baseline_results(results);
end

function result = run_baseline_evaluation(agent, env, scenario)
    % 运行单次基线评估
    
    % 重置环境
    state = env.reset();
    
    % 初始化记录
    total_reward = 0;
    total_energy = 0;
    total_pdr = 0;
    total_delay = 0;
    step_count = 0;
    
    power_levels = [];
    energy_consumption = [];
    pdr_values = [];
    delay_values = [];
    
    % 禁用探索（如果是DQN）
    if strcmp(agent.algorithm_type, 'simple_dqn')
        original_epsilon = agent.epsilon;
        agent.epsilon = 0;
    end
    
    % 运行评估
    for step = 1:env.max_steps
        
        % 选择动作
        action = agent.select_action(state);
        power_levels = [power_levels, env.power_levels(action)];
        
        % 执行动作
        [next_state, reward, done, info] = env.step(action);
        
        % 记录结果
        total_reward = total_reward + reward;
        total_energy = total_energy + info.energy;
        total_pdr = total_pdr + info.pdr;
        total_delay = total_delay + info.delay;
        step_count = step_count + 1;
        
        energy_consumption = [energy_consumption, info.energy];
        pdr_values = [pdr_values, info.pdr];
        delay_values = [delay_values, info.delay];
        
        state = next_state;
        
        if done
            break;
        end
    end
    
    % 恢复探索率
    if strcmp(agent.algorithm_type, 'simple_dqn')
        agent.epsilon = original_epsilon;
    end
    
    % 计算平均指标
    result = struct();
    result.total_reward = total_reward;
    result.avg_energy = total_energy / step_count;
    result.avg_pdr = total_pdr / step_count;
    result.avg_delay = total_delay / step_count;
    result.total_steps = step_count;
    
    % 详细数据
    result.power_levels = power_levels;
    result.energy_consumption = energy_consumption;
    result.pdr_values = pdr_values;
    result.delay_values = delay_values;
    
    % 计算稳定性指标
    result.power_stability = 1 / (1 + std(power_levels));
    result.energy_variance = var(energy_consumption);
end

function performance = aggregate_baseline_results(results)
    % 汇总基线结果
    
    num_runs = length(results);
    
    % 提取指标
    total_rewards = zeros(num_runs, 1);
    avg_energies = zeros(num_runs, 1);
    avg_pdrs = zeros(num_runs, 1);
    avg_delays = zeros(num_runs, 1);
    power_stabilities = zeros(num_runs, 1);
    energy_variances = zeros(num_runs, 1);
    
    for i = 1:num_runs
        total_rewards(i) = results{i}.total_reward;
        avg_energies(i) = results{i}.avg_energy;
        avg_pdrs(i) = results{i}.avg_pdr;
        avg_delays(i) = results{i}.avg_delay;
        power_stabilities(i) = results{i}.power_stability;
        energy_variances(i) = results{i}.energy_variance;
    end
    
    % 计算统计量
    performance = struct();
    performance.reward = struct('mean', mean(total_rewards), 'std', std(total_rewards));
    performance.energy = struct('mean', mean(avg_energies), 'std', std(avg_energies));
    performance.pdr = struct('mean', mean(avg_pdrs), 'std', std(avg_pdrs));
    performance.delay = struct('mean', mean(avg_delays), 'std', std(avg_delays));
    performance.power_stability = struct('mean', mean(power_stabilities), 'std', std(power_stabilities));
    performance.energy_variance = struct('mean', mean(energy_variances), 'std', std(energy_variances));
    
    % 计算综合评分
    energy_score = 1 / (1 + performance.energy.mean / 100); % 能耗越低越好
    pdr_score = performance.pdr.mean; % PDR越高越好
    delay_score = 1 / (1 + performance.delay.mean / 50); % 延迟越低越好
    
    performance.overall_score = (energy_score + pdr_score + delay_score) / 3;
    
    % 详细结果
    performance.detailed_results = results;
end
