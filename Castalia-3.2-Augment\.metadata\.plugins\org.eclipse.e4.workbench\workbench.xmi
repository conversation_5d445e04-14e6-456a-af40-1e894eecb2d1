<?xml version="1.0" encoding="ASCII"?>
<application:Application xmi:version="2.0" xmlns:xmi="http://www.omg.org/XMI" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:advanced="http://www.eclipse.org/ui/2010/UIModel/application/ui/advanced" xmlns:application="http://www.eclipse.org/ui/2010/UIModel/application" xmlns:basic="http://www.eclipse.org/ui/2010/UIModel/application/ui/basic" xmlns:menu="http://www.eclipse.org/ui/2010/UIModel/application/ui/menu" xmi:id="_RU9MUUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.e4.legacy.ide.application" contributorURI="platform:/plugin/org.eclipse.ui.workbench" selectedElement="_RU9MUkQHEfCsYKyjnvaXEQ" bindingContexts="_RU9MWEQHEfCsYKyjnvaXEQ">
  <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;workbench>&#xD;&#xA;&lt;mruList/>&#xD;&#xA;&lt;/workbench>"/>
  <tags>activeSchemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
  <tags>ModelMigrationProcessor.001</tags>
  <children xsi:type="basic:TrimmedWindow" xmi:id="_RU9MUkQHEfCsYKyjnvaXEQ" elementId="IDEWindow" contributorURI="platform:/plugin/org.eclipse.ui.workbench" selectedElement="_RdO-kkQHEfCsYKyjnvaXEQ" label="%trimmedwindow.label.eclipseSDK" x="64" y="64" width="1024" height="768">
    <persistedState key="coolBarVisible" value="true"/>
    <persistedState key="perspectiveBarVisible" value="true"/>
    <persistedState key="workingSets" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;workingSets/>"/>
    <persistedState key="aggregateWorkingSetId" value="Aggregate for window 1749345964617"/>
    <tags>topLevel</tags>
    <tags>shellMaximized</tags>
    <children xsi:type="basic:PartSashContainer" xmi:id="_RdO-kkQHEfCsYKyjnvaXEQ" selectedElement="_RdO-k0QHEfCsYKyjnvaXEQ" horizontal="true">
      <children xsi:type="advanced:PerspectiveStack" xmi:id="_RdO-k0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.ide.perspectivestack" containerData="7500" selectedElement="_RgWkAEQHEfCsYKyjnvaXEQ">
        <children xsi:type="advanced:Perspective" xmi:id="_RgWkAEQHEfCsYKyjnvaXEQ" elementId="org.omnetpp.main.OmnetppPerspective" selectedElement="_RgWkAUQHEfCsYKyjnvaXEQ" label="Simulation" iconURI="platform:/plugin/org.omnetpp.main/icons/logo16.png">
          <persistedState key="persp.hiddenItems" value="persp.hideToolbarSC:org.eclipse.debug.ui.commands.RunToLine,persp.hideToolbarSC:org.eclipse.ui.edit.text.toggleShowSelectedElementOnly,"/>
          <tags>persp.actionSet:org.eclipse.ui.cheatsheets.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.search.searchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.annotationNavigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo</tags>
          <tags>persp.actionSet:org.eclipse.ui.externaltools.ExternalToolsSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.keyBindings</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.openFiles</tags>
          <tags>persp.actionSet:org.omnetpp.ned.ActionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.NavigateActionSet</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.launchActionSet</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ContentOutline</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.PropertySheet</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ProblemView</tags>
          <tags>persp.viewSC:org.eclipse.ui.navigator.ProjectExplorer</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.TaskList</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ProgressView</tags>
          <tags>persp.viewSC:org.omnetpp.inifile.ModuleParameters</tags>
          <tags>persp.viewSC:org.omnetpp.inifile.ModuleHierarchy</tags>
          <tags>persp.viewSC:org.omnetpp.inifile.NedInheritance</tags>
          <tags>persp.viewSC:org.omnetpp.scave.DatasetView</tags>
          <tags>persp.viewSC:org.omnetpp.scave.VectorBrowserView</tags>
          <tags>persp.viewSC:org.omnetpp.sequencechart.editors.SequenceChartView</tags>
          <tags>persp.viewSC:org.omnetpp.eventlogtable.editors.EventLogTableView</tags>
          <tags>persp.newWizSC:org.omnetpp.main.wizards.NewOmnetppProject</tags>
          <tags>persp.newWizSC:org.omnetpp.cdt.wizards.NewOmnetppCCProject</tags>
          <tags>persp.newWizSC:org.omnetpp.ned.editor.wizards.NewSimulation</tags>
          <tags>persp.newWizSC:org.omnetpp.ned.editor.wizards.NewSimpleModule</tags>
          <tags>persp.newWizSC:org.omnetpp.ned.editor.wizards.NewCompoundModule</tags>
          <tags>persp.newWizSC:org.omnetpp.ned.editor.wizards.NewNetwork</tags>
          <tags>persp.newWizSC:org.omnetpp.ned.editor.wizards.NewNedFile</tags>
          <tags>persp.newWizSC:org.omnetpp.msg.editor.wizards.NewMsgFile</tags>
          <tags>persp.newWizSC:org.omnetpp.inifile.editor.wizards.NewIniFile</tags>
          <tags>persp.newWizSC:org.omnetpp.scave.wizards.NewScaveFile</tags>
          <tags>persp.newWizSC:org.eclipse.ui.wizards.new.folder</tags>
          <tags>persp.newWizSC:org.eclipse.ui.wizards.new.file</tags>
          <tags>persp.newWizSC:org.eclipse.ui.editors.wizards.UntitledTextFileWizard</tags>
          <tags>persp.newWizSC:org.omnetpp.common.wizards.NewWizard</tags>
          <tags>persp.perspSC:org.eclipse.cdt.ui.CPerspective</tags>
          <tags>persp.newWizSC:org.omnetpp.cdt.wizards.NewOmnetppClass</tags>
          <tags>persp.perspSC:org.eclipse.ui.resourcePerspective</tags>
          <tags>persp.perspSC:org.omnetpp.main.OmnetppPerspective</tags>
          <children xsi:type="basic:PartSashContainer" xmi:id="_RgWkAUQHEfCsYKyjnvaXEQ" selectedElement="_RgWkAkQHEfCsYKyjnvaXEQ" horizontal="true">
            <children xsi:type="basic:PartSashContainer" xmi:id="_RgWkAkQHEfCsYKyjnvaXEQ" containerData="2500" selectedElement="_RgWkBkQHEfCsYKyjnvaXEQ">
              <children xsi:type="basic:PartStack" xmi:id="_RgWkA0QHEfCsYKyjnvaXEQ" elementId="left" toBeRendered="false" containerData="5000">
                <tags>newtablook</tags>
                <children xsi:type="advanced:Placeholder" xmi:id="_RgWkBEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.navigator.ProjectExplorer" toBeRendered="false" ref="_RgUu0EQHEfCsYKyjnvaXEQ"/>
                <children xsi:type="advanced:Placeholder" xmi:id="_RgWkBUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.views.ResourceNavigator" toBeRendered="false" ref="_RgVV4EQHEfCsYKyjnvaXEQ"/>
              </children>
              <children xsi:type="basic:PartStack" xmi:id="_RgWkBkQHEfCsYKyjnvaXEQ" elementId="leftbottom" containerData="5000" selectedElement="_RgWkB0QHEfCsYKyjnvaXEQ">
                <tags>newtablook</tags>
                <tags>active</tags>
                <children xsi:type="advanced:Placeholder" xmi:id="_RgWkB0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.views.PropertySheet" ref="_RgVV4UQHEfCsYKyjnvaXEQ"/>
                <children xsi:type="advanced:Placeholder" xmi:id="_RgWkCEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.views.ContentOutline" ref="_RgVV4kQHEfCsYKyjnvaXEQ"/>
              </children>
            </children>
            <children xsi:type="basic:PartSashContainer" xmi:id="_RgWkCUQHEfCsYKyjnvaXEQ" containerData="7500" selectedElement="_RgWkCkQHEfCsYKyjnvaXEQ">
              <children xsi:type="advanced:Placeholder" xmi:id="_RgWkCkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.editorss" containerData="7500" ref="_RgREcEQHEfCsYKyjnvaXEQ"/>
              <children xsi:type="basic:PartStack" xmi:id="_RgWkC0QHEfCsYKyjnvaXEQ" elementId="bottom" containerData="2500" selectedElement="_RgWkDEQHEfCsYKyjnvaXEQ">
                <tags>newtablook</tags>
                <children xsi:type="advanced:Placeholder" xmi:id="_RgWkDEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.views.ProblemView" ref="_RgVV40QHEfCsYKyjnvaXEQ"/>
                <children xsi:type="advanced:Placeholder" xmi:id="_RgWkDUQHEfCsYKyjnvaXEQ" elementId="org.omnetpp.inifile.ModuleHierarchy" ref="_RgV88EQHEfCsYKyjnvaXEQ"/>
                <children xsi:type="advanced:Placeholder" xmi:id="_RgWkDkQHEfCsYKyjnvaXEQ" elementId="org.omnetpp.inifile.ModuleParameters" ref="_RgV88UQHEfCsYKyjnvaXEQ"/>
                <children xsi:type="advanced:Placeholder" xmi:id="_RgWkD0QHEfCsYKyjnvaXEQ" elementId="org.omnetpp.inifile.NedInheritance" ref="_RgV88kQHEfCsYKyjnvaXEQ"/>
                <children xsi:type="advanced:Placeholder" xmi:id="_RgWkEEQHEfCsYKyjnvaXEQ" elementId="org.omnetpp.main.NewVersionView" toBeRendered="false" ref="_RgV880QHEfCsYKyjnvaXEQ"/>
                <children xsi:type="advanced:Placeholder" xmi:id="_RgWkEUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.views.TaskList" toBeRendered="false" ref="_RgV89EQHEfCsYKyjnvaXEQ"/>
                <children xsi:type="advanced:Placeholder" xmi:id="_RgWkEkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.views.BookmarkView" toBeRendered="false" ref="_RgV89UQHEfCsYKyjnvaXEQ"/>
                <children xsi:type="advanced:Placeholder" xmi:id="_RgWkE0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.console.ConsoleView" toBeRendered="false" ref="_RgV89kQHEfCsYKyjnvaXEQ"/>
                <children xsi:type="advanced:Placeholder" xmi:id="_RgWkFEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.views.ProgressView" toBeRendered="false" ref="_RgV890QHEfCsYKyjnvaXEQ"/>
              </children>
            </children>
          </children>
        </children>
      </children>
      <children xsi:type="basic:PartStack" xmi:id="_RdO-lEQHEfCsYKyjnvaXEQ" elementId="stickyFolderRight" containerData="2500" selectedElement="_RdO-lUQHEfCsYKyjnvaXEQ">
        <children xsi:type="advanced:Placeholder" xmi:id="_RdO-lUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.help.ui.HelpView" ref="_RdOXgEQHEfCsYKyjnvaXEQ"/>
        <children xsi:type="advanced:Placeholder" xmi:id="_RdO-lkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.internal.introview" toBeRendered="false" ref="_RdO-kEQHEfCsYKyjnvaXEQ"/>
        <children xsi:type="advanced:Placeholder" xmi:id="_RdO-l0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" toBeRendered="false" ref="_RdO-kUQHEfCsYKyjnvaXEQ"/>
      </children>
    </children>
    <sharedElements xsi:type="basic:Part" xmi:id="_RdOXgEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.help.ui.HelpView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Help" iconURI="platform:/plugin/org.eclipse.help.ui/icons/view16/help_view.gif" closeable="true">
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view pageId=&quot;all-topics-page&quot;/>"/>
      <tags>View</tags>
      <tags>categoryTag:Help</tags>
      <menus xmi:id="_VXvFQEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.help.ui.HelpView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_VXvFQUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.help.ui.HelpView"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_RdO-kEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.internal.introview" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Welcome" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_RvX3cEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.internal.introview">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_RvX3cUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.internal.introview" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_RdO-kUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Cheat Sheets" iconURI="platform:/plugin/org.eclipse.ui.cheatsheets/icons/view16/cheatsheet_view.gif" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:Help</tags>
    </sharedElements>
    <sharedElements xsi:type="advanced:Area" xmi:id="_RgREcEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.editorss" selectedElement="_RgREcUQHEfCsYKyjnvaXEQ">
      <children xsi:type="basic:PartStack" xmi:id="_RgREcUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.e4.primaryDataStack">
        <tags>newtablook</tags>
        <tags>org.eclipse.e4.primaryDataStack</tags>
        <tags>EditorStack</tags>
      </children>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_RgUu0EQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.navigator.ProjectExplorer" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Project Explorer" iconURI="platform:/plugin/org.eclipse.ui.navigator.resources/icons/full/eview16/resource_persp.gif" closeable="true">
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view CommonNavigator.LINKING_ENABLED=&quot;0&quot; org.eclipse.cdt.ui.cview.groupincludes=&quot;false&quot; org.eclipse.cdt.ui.cview.groupmacros=&quot;false&quot; org.eclipse.cdt.ui.editor.CUChildren=&quot;true&quot; org.eclipse.ui.navigator.resources.workingSets.showTopLevelWorkingSets=&quot;0&quot;/>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_Rhi20EQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.navigator.ProjectExplorer">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_Rhi20UQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.navigator.ProjectExplorer" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_RgVV4EQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.views.ResourceNavigator" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Navigator" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/filenav_nav.png" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_RgVV4UQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.views.PropertySheet" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Properties" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/prop_ps.png" closeable="true">
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <tags>active</tags>
      <tags>activeOnClose</tags>
      <menus xmi:id="_Rl054EQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.views.PropertySheet">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_Rl054UQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.views.PropertySheet"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_RgVV4kQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.views.ContentOutline" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Outline" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/outline_co.png" closeable="true">
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_uBr2gEQHEfClj7atFCZhyQ" elementId="org.eclipse.ui.views.ContentOutline">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_uBr2gUQHEfClj7atFCZhyQ" elementId="org.eclipse.ui.views.ContentOutline" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_RgVV40QHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.views.ProblemView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Problems" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" closeable="true">
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view PRIMARY_SORT_FIELD=&quot;org.eclipse.ui.ide.severityAndDescriptionField&quot; categoryGroup=&quot;org.eclipse.ui.ide.severity&quot; markerContentGenerator=&quot;org.eclipse.ui.ide.problemsGenerator&quot; partName=&quot;Problems&quot;>&#xD;&#xA;&lt;columnWidths org.eclipse.ui.ide.locationField=&quot;105&quot; org.eclipse.ui.ide.markerType=&quot;105&quot; org.eclipse.ui.ide.pathField=&quot;140&quot; org.eclipse.ui.ide.resourceField=&quot;105&quot; org.eclipse.ui.ide.severityAndDescriptionField=&quot;350&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.severityAndDescriptionField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.resourceField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.pathField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.locationField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.markerType&quot;/>&#xD;&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_Rmx8IEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.views.ProblemView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_Rmx8IUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.views.ProblemView"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_RgV88EQHEfCsYKyjnvaXEQ" elementId="org.omnetpp.inifile.ModuleHierarchy" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Module Hierarchy" iconURI="platform:/plugin/org.omnetpp.inifile.editor/icons/full/eview16/modulehierarchy.png" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:OMNeT++</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_RgV88UQHEfCsYKyjnvaXEQ" elementId="org.omnetpp.inifile.ModuleParameters" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="NED Parameters" iconURI="platform:/plugin/org.omnetpp.inifile.editor/icons/full/eview16/moduleparameters.png" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:OMNeT++</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_RgV88kQHEfCsYKyjnvaXEQ" elementId="org.omnetpp.inifile.NedInheritance" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="NED Inheritance" iconURI="platform:/plugin/org.omnetpp.inifile.editor/icons/full/eview16/inheritance.png" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:OMNeT++</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_RgV880QHEfCsYKyjnvaXEQ" elementId="org.omnetpp.main.NewVersionView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="New OMNeT++ Version" iconURI="platform:/plugin/org.omnetpp.main/icons/logo16.png" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:OMNeT++</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_RgV89EQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.views.TaskList" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Tasks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/tasks_tsk.png" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_RgV89UQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.views.BookmarkView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Bookmarks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/bkmrk_nav.png" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_RgV89kQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.console.ConsoleView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Console" iconURI="platform:/plugin/org.eclipse.ui.console/icons/full/cview16/console_view.png" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_RgV890QHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.views.ProgressView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Progress" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/pview.png" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <trimBars xmi:id="_RU9MU0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.main.toolbar" contributorURI="platform:/plugin/org.eclipse.ui.workbench">
      <children xsi:type="menu:ToolBar" xmi:id="_ReEFAEQHEfCsYKyjnvaXEQ" elementId="group.file" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_ReEFAUQHEfCsYKyjnvaXEQ" elementId="group.file" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_ReEsEEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.workbench.file">
        <tags>Draggable</tags>
        <children xsi:type="menu:HandledToolItem" xmi:id="_ivbfJEQHEfClj7atFCZhyQ" elementId="print" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/print_edit.png" tooltip="Print" enabled="false" command="_RVQuokQHEfCsYKyjnvaXEQ"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_ReEsEUQHEfCsYKyjnvaXEQ" elementId="additions" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_ReEsEkQHEfCsYKyjnvaXEQ" elementId="additions" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_RgjYUEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.debug.ui.launchActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_Rgpe8EQHEfCsYKyjnvaXEQ" elementId="org.eclipse.search.searchActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_ReEsE0QHEfCsYKyjnvaXEQ" elementId="group.nav" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_ReEsFEQHEfCsYKyjnvaXEQ" elementId="group.nav" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_ReEsFUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.workbench.navigate">
        <tags>Draggable</tags>
        <children xsi:type="menu:HandledToolItem" xmi:id="_ivdUVUQHEfClj7atFCZhyQ" elementId="org.eclipse.ui.window.pinEditor" visible="false" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/pin_editor.png" tooltip="Pin Editor" type="Check" command="_RVOSNkQHEfCsYKyjnvaXEQ"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_ReEsFkQHEfCsYKyjnvaXEQ" elementId="group.editor" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_ReEsF0QHEfCsYKyjnvaXEQ" elementId="group.editor" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_ReEsGEQHEfCsYKyjnvaXEQ" elementId="group.help" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_ReEsGUQHEfCsYKyjnvaXEQ" elementId="group.help" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_ReEsGkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.workbench.help">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_Re06AEQHEfCsYKyjnvaXEQ" elementId="PerspectiveSpacer" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
        <tags>stretch</tags>
        <tags>SHOW_RESTORE_MENU</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_Re1hEEQHEfCsYKyjnvaXEQ" elementId="PerspectiveSwitcher" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.e4.ui.workbench.addons.perspectiveswitcher.PerspectiveSwitcher">
        <tags>Draggable</tags>
        <tags>HIDEABLE</tags>
        <tags>SHOW_RESTORE_MENU</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_RU9MVEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.trim.status" contributorURI="platform:/plugin/org.eclipse.ui.workbench" side="Bottom">
      <children xsi:type="menu:ToolControl" xmi:id="_Re7AoEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.StatusLine" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>stretch</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_RfQ-4EQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.HeapStatus" toBeRendered="false" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_RfXFgEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.ProgressBar" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>Draggable</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_RU9MVUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.trim.vertical1" contributorURI="platform:/plugin/org.eclipse.ui.workbench" toBeRendered="false" side="Left">
      <children xsi:type="menu:ToolControl" xmi:id="_RzErsEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.ide.perspectivestack(minimized)" toBeRendered="false" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.TrimStack">
        <tags>TrimStack</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_RU9MVkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.trim.vertical2" contributorURI="platform:/plugin/org.eclipse.ui.workbench" side="Right"/>
  </children>
  <bindingTables xmi:id="_RU9MV0QHEfCsYKyjnvaXEQ" contributorURI="platform:/plugin/org.eclipse.ui.workbench" bindingContext="_RU9MWEQHEfCsYKyjnvaXEQ">
    <bindings xmi:id="_RVeJsEQHEfCsYKyjnvaXEQ" keySequence="CTRL+INSERT" command="_RVPgY0QHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVew0UQHEfCsYKyjnvaXEQ" keySequence="CTRL+A" command="_RVPgWEQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVgmBkQHEfCsYKyjnvaXEQ" keySequence="CTRL+V" command="_RVO5PUQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVgmB0QHEfCsYKyjnvaXEQ" keySequence="CTRL+X" command="_RVQuVkQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVgmCEQHEfCsYKyjnvaXEQ" keySequence="CTRL+Y" command="_RVQum0QHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVgmC0QHEfCsYKyjnvaXEQ" keySequence="CTRL+Z" command="_RVO5SkQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVibI0QHEfCsYKyjnvaXEQ" keySequence="CTRL+F10" command="_RVPgeUQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVibKUQHEfCsYKyjnvaXEQ" keySequence="ALT+SHIFT+F3" command="_RVRVw0QHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVibLkQHEfCsYKyjnvaXEQ" keySequence="CTRL+1" command="_RVQHfEQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVibL0QHEfCsYKyjnvaXEQ" keySequence="CTRL+SHIFT+L" command="_RVO5LkQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVibO0QHEfCsYKyjnvaXEQ" keySequence="ALT+/" command="_RVQHS0QHEfCsYKyjnvaXEQ">
      <tags>locale:zh</tags>
    </bindings>
    <bindings xmi:id="_RVjCSkQHEfCsYKyjnvaXEQ" keySequence="SHIFT+DEL" command="_RVQuVkQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVjCUkQHEfCsYKyjnvaXEQ" keySequence="CTRL+C" command="_RVPgY0QHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVjpQUQHEfCsYKyjnvaXEQ" keySequence="ALT+PAGE_UP" command="_RVQHQUQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVjpV0QHEfCsYKyjnvaXEQ" keySequence="ALT+PAGE_DOWN" command="_RVQuZEQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVjpXEQHEfCsYKyjnvaXEQ" keySequence="SHIFT+INSERT" command="_RVO5PUQHEfCsYKyjnvaXEQ"/>
  </bindingTables>
  <bindingTables xmi:id="_RVewwEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.contexts.window" bindingContext="_RU9MWUQHEfCsYKyjnvaXEQ">
    <bindings xmi:id="_RVewwUQHEfCsYKyjnvaXEQ" keySequence="CTRL+SHIFT+NUMPAD_MULTIPLY" command="_RVOSG0QHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVewx0QHEfCsYKyjnvaXEQ" keySequence="CTRL+TAB" command="_RVOSEkQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVewzUQHEfCsYKyjnvaXEQ" keySequence="CTRL+E" command="_RVO5NkQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVew0EQHEfCsYKyjnvaXEQ" keySequence="ALT+ARROW_LEFT" command="_RVQHhkQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVew10QHEfCsYKyjnvaXEQ" keySequence="ALT+?" command="_RVRVf0QHEfCsYKyjnvaXEQ">
      <tags>locale:zh</tags>
    </bindings>
    <bindings xmi:id="_RVfX0UQHEfCsYKyjnvaXEQ" keySequence="CTRL+=" command="_RVQHekQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVfX1EQHEfCsYKyjnvaXEQ" keySequence="ALT+SHIFT+Q O" command="_RVRVskQHEfCsYKyjnvaXEQ">
      <parameters xmi:id="_RVfX1UQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.views.ContentOutline"/>
    </bindings>
    <bindings xmi:id="_RVfX1kQHEfCsYKyjnvaXEQ" keySequence="ALT+SHIFT+Q X" command="_RVRVskQHEfCsYKyjnvaXEQ">
      <parameters xmi:id="_RVfX10QHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.views.ProblemView"/>
    </bindings>
    <bindings xmi:id="_RVfX2EQHEfCsYKyjnvaXEQ" keySequence="ALT+SHIFT+Q Q" command="_RVRVskQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVfX2UQHEfCsYKyjnvaXEQ" keySequence="ALT+SHIFT+Q C" command="_RVRVskQHEfCsYKyjnvaXEQ">
      <parameters xmi:id="_RVfX2kQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.console.ConsoleView"/>
    </bindings>
    <bindings xmi:id="_RVfX20QHEfCsYKyjnvaXEQ" keySequence="ALT+SHIFT+Q H" command="_RVRVskQHEfCsYKyjnvaXEQ">
      <parameters xmi:id="_RVfX3EQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.cheatsheets.views.CheatSheetView"/>
    </bindings>
    <bindings xmi:id="_RVf-4EQHEfCsYKyjnvaXEQ" keySequence="ALT+SHIFT+Q B" command="_RVRVskQHEfCsYKyjnvaXEQ">
      <parameters xmi:id="_RVf-4UQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.debug.ui.BreakpointView"/>
    </bindings>
    <bindings xmi:id="_RVf-4kQHEfCsYKyjnvaXEQ" keySequence="ALT+SHIFT+Q V" command="_RVRVskQHEfCsYKyjnvaXEQ">
      <parameters xmi:id="_RVf-40QHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.debug.ui.VariableView"/>
    </bindings>
    <bindings xmi:id="_RVf-5EQHEfCsYKyjnvaXEQ" keySequence="ALT+SHIFT+Q S" command="_RVRVskQHEfCsYKyjnvaXEQ">
      <parameters xmi:id="_RVf-5UQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.search.ui.views.SearchView"/>
    </bindings>
    <bindings xmi:id="_RVf-5kQHEfCsYKyjnvaXEQ" keySequence="ALT+SHIFT+Q Y" command="_RVRVskQHEfCsYKyjnvaXEQ">
      <parameters xmi:id="_RVf-50QHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.team.sync.views.SynchronizeView"/>
    </bindings>
    <bindings xmi:id="_RVf-6EQHEfCsYKyjnvaXEQ" keySequence="ALT+SHIFT+Q Z" command="_RVRVskQHEfCsYKyjnvaXEQ">
      <parameters xmi:id="_RVf-6UQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.team.ui.GenericHistoryView"/>
    </bindings>
    <bindings xmi:id="_RVf-7UQHEfCsYKyjnvaXEQ" keySequence="CTRL+SHIFT+B" command="_RVQufkQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVf-8EQHEfCsYKyjnvaXEQ" keySequence="ALT+CTRL+G" command="_RVO5MkQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVf-8UQHEfCsYKyjnvaXEQ" keySequence="ALT+SHIFT+?" command="_RVRVf0QHEfCsYKyjnvaXEQ">
      <tags>locale:zh</tags>
    </bindings>
    <bindings xmi:id="_RVgl80QHEfCsYKyjnvaXEQ" keySequence="CTRL+SHIFT+E" command="_RVQHXUQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVgl-EQHEfCsYKyjnvaXEQ" keySequence="ALT+ARROW_RIGHT" command="_RVQuo0QHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVgl-UQHEfCsYKyjnvaXEQ" keySequence="CTRL+B" command="_RVOSGUQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVgl_EQHEfCsYKyjnvaXEQ" keySequence="CTRL+SHIFT+F6" command="_RVPggEQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVgl_UQHEfCsYKyjnvaXEQ" keySequence="CTRL+F6" command="_RVO5bUQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVgl_kQHEfCsYKyjnvaXEQ" keySequence="CTRL+M" command="_RVPggUQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVgl_0QHEfCsYKyjnvaXEQ" keySequence="F12" command="_RVQud0QHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVgmAEQHEfCsYKyjnvaXEQ" keySequence="ALT+-" command="_RVQHi0QHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVgmAUQHEfCsYKyjnvaXEQ" keySequence="CTRL+," command="_RVO5PkQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVgmAkQHEfCsYKyjnvaXEQ" keySequence="CTRL+." command="_RVPgR0QHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVgmA0QHEfCsYKyjnvaXEQ" keySequence="DEL" command="_RVO5LUQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVhNAEQHEfCsYKyjnvaXEQ" keySequence="F5" command="_RVO5ZkQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVhNBEQHEfCsYKyjnvaXEQ" keySequence="ALT+CR" command="_RVQuuUQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVhNBUQHEfCsYKyjnvaXEQ" keySequence="CTRL+P" command="_RVQuokQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVhNBkQHEfCsYKyjnvaXEQ" keySequence="CTRL+SHIFT+S" command="_RVPgc0QHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVhNB0QHEfCsYKyjnvaXEQ" keySequence="CTRL+S" command="_RVQHc0QHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVhNCEQHEfCsYKyjnvaXEQ" keySequence="CTRL+SHIFT+F4" command="_RVRVvEQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVhNCUQHEfCsYKyjnvaXEQ" keySequence="CTRL+SHIFT+W" command="_RVRVvEQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVhNCkQHEfCsYKyjnvaXEQ" keySequence="CTRL+F4" command="_RVPgXEQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVhNDUQHEfCsYKyjnvaXEQ" keySequence="CTRL+W" command="_RVPgXEQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVhND0QHEfCsYKyjnvaXEQ" keySequence="CTRL+N" command="_RVPgXkQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVhNEEQHEfCsYKyjnvaXEQ" keySequence="CTRL+-" command="_RVQHnEQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVhNGUQHEfCsYKyjnvaXEQ" keySequence="CTRL+F11" command="_RVQuqEQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVhNGkQHEfCsYKyjnvaXEQ" keySequence="F11" command="_RVO5TEQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVh0KEQHEfCsYKyjnvaXEQ" keySequence="CTRL+SHIFT+F11" command="_RVPgT0QHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVh0KUQHEfCsYKyjnvaXEQ" keySequence="CTRL+SHIFT+N" command="_RVO5ZUQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVh0KkQHEfCsYKyjnvaXEQ" keySequence="F2" command="_RVPgRkQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVibIkQHEfCsYKyjnvaXEQ" keySequence="CTRL+Q" command="_RVQugUQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVibKkQHEfCsYKyjnvaXEQ" keySequence="CTRL+3" command="_RVRVxUQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVibK0QHEfCsYKyjnvaXEQ" keySequence="ALT+SHIFT+N" command="_RVO5RUQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVibLEQHEfCsYKyjnvaXEQ" keySequence="ALT+SHIFT+W" command="_RVPgP0QHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVibLUQHEfCsYKyjnvaXEQ" keySequence="CTRL+SHIFT+R" command="_RVPgbUQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVibMkQHEfCsYKyjnvaXEQ" keySequence="CTRL+SHIFT+NUMPAD_DIVIDE" command="_RVQukEQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVibN0QHEfCsYKyjnvaXEQ" keySequence="CTRL+{" command="_RVQHmUQHEfCsYKyjnvaXEQ">
      <parameters xmi:id="_RVibOEQHEfCsYKyjnvaXEQ" elementId="Splitter.isHorizontal" name="Splitter.isHorizontal" value="false"/>
    </bindings>
    <bindings xmi:id="_RVibOUQHEfCsYKyjnvaXEQ" keySequence="ALT+SHIFT+F7" command="_RVPgbEQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVibOkQHEfCsYKyjnvaXEQ" keySequence="ALT+F7" command="_RVQHg0QHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVjCMEQHEfCsYKyjnvaXEQ" keySequence="CTRL+SHIFT+F8" command="_RVQHZkQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVjCMUQHEfCsYKyjnvaXEQ" keySequence="CTRL+F8" command="_RVRV30QHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVjCMkQHEfCsYKyjnvaXEQ" keySequence="CTRL+SHIFT+F7" command="_RVQHXkQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVjCM0QHEfCsYKyjnvaXEQ" keySequence="CTRL+F7" command="_RVQutkQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVjCNUQHEfCsYKyjnvaXEQ" keySequence="SHIFT+F5" command="_RVO5VEQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVjCSEQHEfCsYKyjnvaXEQ" keySequence="F9" command="_RVPgfkQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVjCSUQHEfCsYKyjnvaXEQ" keySequence="SHIFT+F9" command="_RVRVbUQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVjCTEQHEfCsYKyjnvaXEQ" keySequence="CTRL+F" command="_RVQuWUQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVjpQEQHEfCsYKyjnvaXEQ" keySequence="CTRL+#" command="_RVQua0QHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVjpS0QHEfCsYKyjnvaXEQ" keySequence="CTRL+_" command="_RVQHmUQHEfCsYKyjnvaXEQ">
      <parameters xmi:id="_RVjpTEQHEfCsYKyjnvaXEQ" elementId="Splitter.isHorizontal" name="Splitter.isHorizontal" value="true"/>
    </bindings>
    <bindings xmi:id="_RVjpWkQHEfCsYKyjnvaXEQ" keySequence="CTRL+H" command="_RVQuukQHEfCsYKyjnvaXEQ"/>
  </bindingTables>
  <bindingTables xmi:id="_RVewwkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.textEditorScope" bindingContext="_RVTKm0QHEfCsYKyjnvaXEQ">
    <bindings xmi:id="_RVeww0QHEfCsYKyjnvaXEQ" keySequence="CTRL+SHIFT+NUMPAD_MULTIPLY" command="_RVOSOkQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVewxEQHEfCsYKyjnvaXEQ" keySequence="CTRL+SHIFT+J" command="_RVQudUQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVew00QHEfCsYKyjnvaXEQ" keySequence="CTRL+ARROW_UP" command="_RVQun0QHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVf-8kQHEfCsYKyjnvaXEQ" keySequence="ALT+CTRL+ARROW_UP" command="_RVQuqUQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVf-80QHEfCsYKyjnvaXEQ" keySequence="CTRL+SHIFT+INSERT" command="_RVQuYUQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVf-9EQHEfCsYKyjnvaXEQ" keySequence="CTRL+SHIFT+DEL" command="_RVO5a0QHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVf-9UQHEfCsYKyjnvaXEQ" keySequence="ALT+ARROW_DOWN" command="_RVQuWEQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVgl8kQHEfCsYKyjnvaXEQ" keySequence="ALT+CTRL+ARROW_DOWN" command="_RVQuxEQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVgl9UQHEfCsYKyjnvaXEQ" keySequence="ALT+SHIFT+A" command="_RVQHckQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVgl9kQHEfCsYKyjnvaXEQ" keySequence="CTRL+SHIFT+ARROW_RIGHT" command="_RVRVwUQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVgl-kQHEfCsYKyjnvaXEQ" keySequence="CTRL+ARROW_DOWN" command="_RVQumEQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVh0IUQHEfCsYKyjnvaXEQ" keySequence="ALT+CTRL+/" command="_RVRV4EQHEfCsYKyjnvaXEQ">
      <tags>locale:zh</tags>
    </bindings>
    <bindings xmi:id="_RVh0IkQHEfCsYKyjnvaXEQ" keySequence="CTRL+K" command="_RVRVpEQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVh0K0QHEfCsYKyjnvaXEQ" keySequence="F2" command="_RVO5JUQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVh0LkQHEfCsYKyjnvaXEQ" keySequence="HOME" command="_RVQHbkQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVh0L0QHEfCsYKyjnvaXEQ" keySequence="END" command="_RVRVy0QHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVh0MEQHEfCsYKyjnvaXEQ" keySequence="CTRL+SHIFT+Y" command="_RVQHjEQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVh0MUQHEfCsYKyjnvaXEQ" keySequence="CTRL+SHIFT+X" command="_RVRViEQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVibIEQHEfCsYKyjnvaXEQ" keySequence="CTRL+SHIFT+CR" command="_RVOSI0QHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVibIUQHEfCsYKyjnvaXEQ" keySequence="SHIFT+CR" command="_RVQHQ0QHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVibJEQHEfCsYKyjnvaXEQ" keySequence="CTRL+F10" command="_RVQHfkQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVibJUQHEfCsYKyjnvaXEQ" keySequence="INSERT" command="_RVO5KkQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVibJkQHEfCsYKyjnvaXEQ" keySequence="CTRL+L" command="_RVQuY0QHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVibJ0QHEfCsYKyjnvaXEQ" keySequence="CTRL+J" command="_RVPge0QHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVibKEQHEfCsYKyjnvaXEQ" keySequence="CTRL+SHIFT+K" command="_RVRVsUQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVibMEQHEfCsYKyjnvaXEQ" keySequence="CTRL+NUMPAD_SUBTRACT" command="_RVQuskQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVibMUQHEfCsYKyjnvaXEQ" keySequence="CTRL+NUMPAD_ADD" command="_RVQHa0QHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVibM0QHEfCsYKyjnvaXEQ" keySequence="CTRL+SHIFT+NUMPAD_DIVIDE" command="_RVPghUQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVibNEQHEfCsYKyjnvaXEQ" keySequence="CTRL+NUMPAD_MULTIPLY" command="_RVQHTUQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVibNUQHEfCsYKyjnvaXEQ" keySequence="CTRL+NUMPAD_DIVIDE" command="_RVQukUQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVibNkQHEfCsYKyjnvaXEQ" keySequence="CTRL+SHIFT+Q" command="_RVQuXUQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVjCS0QHEfCsYKyjnvaXEQ" keySequence="SHIFT+HOME" command="_RVQubkQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVjpQkQHEfCsYKyjnvaXEQ" keySequence="CTRL+ARROW_LEFT" command="_RVO5WkQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVjpSUQHEfCsYKyjnvaXEQ" keySequence="SHIFT+END" command="_RVOSH0QHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVjpSkQHEfCsYKyjnvaXEQ" keySequence="CTRL+HOME" command="_RVQuuEQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVjpTUQHEfCsYKyjnvaXEQ" keySequence="CTRL+DEL" command="_RVO5R0QHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVjpTkQHEfCsYKyjnvaXEQ" keySequence="ALT+ARROW_UP" command="_RVQuvUQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVjpWEQHEfCsYKyjnvaXEQ" keySequence="CTRL+D" command="_RVPgd0QHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVjpWUQHEfCsYKyjnvaXEQ" keySequence="CTRL+ARROW_RIGHT" command="_RVRVYUQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVjpXUQHEfCsYKyjnvaXEQ" keySequence="ALT+CTRL+J" command="_RVQunUQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVjpXkQHEfCsYKyjnvaXEQ" keySequence="CTRL+END" command="_RVO5K0QHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVjpX0QHEfCsYKyjnvaXEQ" keySequence="CTRL+BS" command="_RVPgMkQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVjpYkQHEfCsYKyjnvaXEQ" keySequence="CTRL+SHIFT+ARROW_LEFT" command="_RVQHnkQHEfCsYKyjnvaXEQ"/>
  </bindingTables>
  <bindingTables xmi:id="_RVewxUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.ui.cEditorScope" bindingContext="_RVTKnkQHEfCsYKyjnvaXEQ">
    <bindings xmi:id="_RVewxkQHEfCsYKyjnvaXEQ" keySequence="CTRL+TAB" command="_RVRVqkQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVewyEQHEfCsYKyjnvaXEQ" keySequence="CTRL+I" command="_RVO5ZEQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVew0kQHEfCsYKyjnvaXEQ" keySequence="ALT+C" command="_RVO5KUQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVfX0EQHEfCsYKyjnvaXEQ" keySequence="CTRL+=" command="_RVO5Q0QHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVfX0kQHEfCsYKyjnvaXEQ" keySequence="CTRL+SHIFT+F" command="_RVRVikQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVf-6kQHEfCsYKyjnvaXEQ" keySequence="ALT+CTRL+I" command="_RVRV4kQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVf-7kQHEfCsYKyjnvaXEQ" keySequence="ALT+SHIFT+ARROW_LEFT" command="_RVPgaEQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVf-70QHEfCsYKyjnvaXEQ" keySequence="CTRL+SHIFT+ARROW_DOWN" command="_RVQHjUQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVgl9EQHEfCsYKyjnvaXEQ" keySequence="ALT+SHIFT+ARROW_UP" command="_RVRVqEQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVhNFUQHEfCsYKyjnvaXEQ" keySequence="CTRL+T" command="_RVOSEEQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVh0EkQHEfCsYKyjnvaXEQ" keySequence="ALT+SHIFT+Z" command="_RVRVdEQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVh0E0QHEfCsYKyjnvaXEQ" keySequence="CTRL+SHIFT+/" command="_RVQHT0QHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVh0FkQHEfCsYKyjnvaXEQ" keySequence="CTRL+/" command="_RVRVaEQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVh0G0QHEfCsYKyjnvaXEQ" keySequence="ALT+SHIFT+S" command="_RVQus0QHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVh0JUQHEfCsYKyjnvaXEQ" keySequence="CTRL+SHIFT+O" command="_RVPgQEQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVjCNkQHEfCsYKyjnvaXEQ" keySequence="ALT+SHIFT+L" command="_RVOSJEQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVjCN0QHEfCsYKyjnvaXEQ" keySequence="ALT+SHIFT+T" command="_RVPgaUQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVjCOEQHEfCsYKyjnvaXEQ" keySequence="ALT+SHIFT+M" command="_RVO5O0QHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVjCOUQHEfCsYKyjnvaXEQ" keySequence="ALT+SHIFT+R" command="_RVRVv0QHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVjCO0QHEfCsYKyjnvaXEQ" keySequence="ALT+SHIFT+O" command="_RVQuZ0QHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVjCPEQHEfCsYKyjnvaXEQ" keySequence="CTRL+SHIFT+P" command="_RVQHUkQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVjCPUQHEfCsYKyjnvaXEQ" keySequence="CTRL+O" command="_RVQuv0QHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVjCPkQHEfCsYKyjnvaXEQ" keySequence="ALT+CTRL+S" command="_RVQHVUQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVjCP0QHEfCsYKyjnvaXEQ" keySequence="F4" command="_RVQuhUQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVjCQUQHEfCsYKyjnvaXEQ" keySequence="CTRL+SHIFT+T" command="_RVQujkQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVjCREQHEfCsYKyjnvaXEQ" keySequence="F3" command="_RVQuVEQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVjCU0QHEfCsYKyjnvaXEQ" keySequence="CTRL+#" command="_RVO5Q0QHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVjpREQHEfCsYKyjnvaXEQ" keySequence="CTRL+G" command="_RVQunkQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVjpUEQHEfCsYKyjnvaXEQ" keySequence="CTRL+SHIFT+G" command="_RVQubEQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVjpVUQHEfCsYKyjnvaXEQ" keySequence="ALT+SHIFT+ARROW_DOWN" command="_RVRVh0QHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVjpVkQHEfCsYKyjnvaXEQ" keySequence="CTRL+SHIFT+ARROW_UP" command="_RVRVdkQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVjpW0QHEfCsYKyjnvaXEQ" keySequence="SHIFT+TAB" command="_RVRVoUQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVjpYUQHEfCsYKyjnvaXEQ" keySequence="ALT+SHIFT+ARROW_RIGHT" command="_RVQHb0QHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVjpY0QHEfCsYKyjnvaXEQ" keySequence="ALT+CTRL+H" command="_RVQuxUQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVjpZUQHEfCsYKyjnvaXEQ" keySequence="CTRL+SHIFT+H" command="_RVPgPEQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVjpZ0QHEfCsYKyjnvaXEQ" keySequence="CTRL+SHIFT+\" command="_RVQulEQHEfCsYKyjnvaXEQ"/>
  </bindingTables>
  <bindingTables xmi:id="_RVewyUQHEfCsYKyjnvaXEQ" elementId="org.omnetpp.context.msgEditor" bindingContext="_RVTKn0QHEfCsYKyjnvaXEQ">
    <bindings xmi:id="_RVewykQHEfCsYKyjnvaXEQ" keySequence="CTRL+I" command="_RVOSM0QHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVh0GUQHEfCsYKyjnvaXEQ" keySequence="CTRL+/" command="_RVQuw0QHEfCsYKyjnvaXEQ"/>
  </bindingTables>
  <bindingTables xmi:id="_RVewy0QHEfCsYKyjnvaXEQ" elementId="org.omnetpp.context.nedTextEditor" bindingContext="_RVTKp0QHEfCsYKyjnvaXEQ">
    <bindings xmi:id="_RVewzEQHEfCsYKyjnvaXEQ" keySequence="CTRL+I" command="_RVPgdUQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVfX00QHEfCsYKyjnvaXEQ" keySequence="CTRL+SHIFT+F" command="_RVQHe0QHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVh0GkQHEfCsYKyjnvaXEQ" keySequence="CTRL+/" command="_RVO5XUQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVh0J0QHEfCsYKyjnvaXEQ" keySequence="CTRL+SHIFT+O" command="_RVQuWkQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVjCR0QHEfCsYKyjnvaXEQ" keySequence="F3" command="_RVRVZEQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVjpU0QHEfCsYKyjnvaXEQ" keySequence="CTRL+SHIFT+G" command="_RVQuqkQHEfCsYKyjnvaXEQ"/>
  </bindingTables>
  <bindingTables xmi:id="_RVewzkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.ui.macroExpansionHoverScope" bindingContext="_RVTKqEQHEfCsYKyjnvaXEQ">
    <bindings xmi:id="_RVewz0QHEfCsYKyjnvaXEQ" keySequence="ALT+ARROW_LEFT" command="_RVOSLEQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVgl90QHEfCsYKyjnvaXEQ" keySequence="ALT+ARROW_RIGHT" command="_RVQuc0QHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVjCRkQHEfCsYKyjnvaXEQ" keySequence="F3" command="_RVQuVEQHEfCsYKyjnvaXEQ"/>
  </bindingTables>
  <bindingTables xmi:id="_RVew1EQHEfCsYKyjnvaXEQ" elementId="org.omnetpp.context.EventLogTable" bindingContext="_RVTKokQHEfCsYKyjnvaXEQ">
    <bindings xmi:id="_RVew1UQHEfCsYKyjnvaXEQ" keySequence="CTRL+ARROW_UP" command="_RVOSIkQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVew1kQHEfCsYKyjnvaXEQ" keySequence="SHIFT+ARROW_DOWN" command="_RVNrBUQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVgl8EQHEfCsYKyjnvaXEQ" keySequence="ALT+ARROW_DOWN" command="_RVRVhUQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVgl8UQHEfCsYKyjnvaXEQ" keySequence="SHIFT+ARROW_UP" command="_RVQuaEQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVgl-0QHEfCsYKyjnvaXEQ" keySequence="CTRL+ARROW_DOWN" command="_RVRV20QHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVhNAUQHEfCsYKyjnvaXEQ" keySequence="F5" command="_RVPgQ0QHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVh0I0QHEfCsYKyjnvaXEQ" keySequence="CTRL+K" command="_RVRVukQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVjCTUQHEfCsYKyjnvaXEQ" keySequence="CTRL+F" command="_RVO5SEQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVjpR0QHEfCsYKyjnvaXEQ" keySequence="CTRL+G" command="_RVRVY0QHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVjpT0QHEfCsYKyjnvaXEQ" keySequence="ALT+ARROW_UP" command="_RVRVt0QHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVjpUkQHEfCsYKyjnvaXEQ" keySequence="CTRL+SHIFT+G" command="_RVRVwEQHEfCsYKyjnvaXEQ"/>
  </bindingTables>
  <bindingTables xmi:id="_RVf-60QHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.ui.cViewScope" bindingContext="_RVTKlEQHEfCsYKyjnvaXEQ">
    <bindings xmi:id="_RVf-7EQHEfCsYKyjnvaXEQ" keySequence="ALT+CTRL+I" command="_RVRV4kQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVjCOkQHEfCsYKyjnvaXEQ" keySequence="ALT+SHIFT+R" command="_RVRVv0QHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVjCQEQHEfCsYKyjnvaXEQ" keySequence="F4" command="_RVQuhUQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVjCQkQHEfCsYKyjnvaXEQ" keySequence="CTRL+SHIFT+T" command="_RVQujkQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVjCRUQHEfCsYKyjnvaXEQ" keySequence="F3" command="_RVQuVEQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVjpRUQHEfCsYKyjnvaXEQ" keySequence="CTRL+G" command="_RVQunkQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVjpUUQHEfCsYKyjnvaXEQ" keySequence="CTRL+SHIFT+G" command="_RVQubEQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVjpZEQHEfCsYKyjnvaXEQ" keySequence="ALT+CTRL+H" command="_RVQuxUQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVjpZkQHEfCsYKyjnvaXEQ" keySequence="CTRL+SHIFT+H" command="_RVPgPEQHEfCsYKyjnvaXEQ"/>
  </bindingTables>
  <bindingTables xmi:id="_RVgmBEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.RepositoriesView" bindingContext="_RVTKkkQHEfCsYKyjnvaXEQ">
    <bindings xmi:id="_RVgmBUQHEfCsYKyjnvaXEQ" keySequence="CTRL+V" command="_RVO5PEQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVjCT0QHEfCsYKyjnvaXEQ" keySequence="CTRL+C" command="_RVO5P0QHEfCsYKyjnvaXEQ"/>
  </bindingTables>
  <bindingTables xmi:id="_RVgmCUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.debug.ui.console" bindingContext="_RVTKmkQHEfCsYKyjnvaXEQ">
    <bindings xmi:id="_RVgmCkQHEfCsYKyjnvaXEQ" keySequence="CTRL+Z" command="_RVPgh0QHEfCsYKyjnvaXEQ">
      <tags>platform:win32</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_RVgmDEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.debug.ui.debugging" bindingContext="_RVTKlUQHEfCsYKyjnvaXEQ">
    <bindings xmi:id="_RVgmDUQHEfCsYKyjnvaXEQ" keySequence="F5" command="_RVQumkQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVhNGEQHEfCsYKyjnvaXEQ" keySequence="CTRL+R" command="_RVPgZ0QHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVhNG0QHEfCsYKyjnvaXEQ" keySequence="CTRL+F2" command="_RVQHU0QHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVhNHEQHEfCsYKyjnvaXEQ" keySequence="F8" command="_RVRV6UQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVh0EEQHEfCsYKyjnvaXEQ" keySequence="F7" command="_RVRVyUQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVh0EUQHEfCsYKyjnvaXEQ" keySequence="F6" command="_RVOSK0QHEfCsYKyjnvaXEQ"/>
  </bindingTables>
  <bindingTables xmi:id="_RVhNAkQHEfCsYKyjnvaXEQ" elementId="org.omnetpp.context.SequenceChart" bindingContext="_RVTKpEQHEfCsYKyjnvaXEQ">
    <bindings xmi:id="_RVhNA0QHEfCsYKyjnvaXEQ" keySequence="F5" command="_RVRVxkQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVh0JEQHEfCsYKyjnvaXEQ" keySequence="CTRL+K" command="_RVOSJ0QHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVjCTkQHEfCsYKyjnvaXEQ" keySequence="CTRL+F" command="_RVPgfUQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVjpSEQHEfCsYKyjnvaXEQ" keySequence="CTRL+G" command="_RVRVZ0QHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVjpVEQHEfCsYKyjnvaXEQ" keySequence="CTRL+SHIFT+G" command="_RVO5TkQHEfCsYKyjnvaXEQ"/>
  </bindingTables>
  <bindingTables xmi:id="_RVhNC0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.debug.ui.memoryview" bindingContext="_RVTKoUQHEfCsYKyjnvaXEQ">
    <bindings xmi:id="_RVhNDEQHEfCsYKyjnvaXEQ" keySequence="CTRL+W" command="_RVQHYEQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVhNDkQHEfCsYKyjnvaXEQ" keySequence="CTRL+N" command="_RVQHj0QHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVhNFEQHEfCsYKyjnvaXEQ" keySequence="ALT+CTRL+N" command="_RVO5KEQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVhNFkQHEfCsYKyjnvaXEQ" keySequence="CTRL+T" command="_RVQug0QHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVhNF0QHEfCsYKyjnvaXEQ" keySequence="ALT+CTRL+M" command="_RVOSMUQHEfCsYKyjnvaXEQ"/>
  </bindingTables>
  <bindingTables xmi:id="_RVhNEUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.debug.ui.memory.abstractasynctablerendering" bindingContext="_RVTKl0QHEfCsYKyjnvaXEQ">
    <bindings xmi:id="_RVhNEkQHEfCsYKyjnvaXEQ" keySequence="CTRL+SHIFT+," command="_RVRVsEQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVhNE0QHEfCsYKyjnvaXEQ" keySequence="CTRL+SHIFT+." command="_RVNrAEQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVjpRkQHEfCsYKyjnvaXEQ" keySequence="CTRL+G" command="_RVQHV0QHEfCsYKyjnvaXEQ"/>
  </bindingTables>
  <bindingTables xmi:id="_RVh0FEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.make.ui.makefileEditorScope" bindingContext="_RVTKnUQHEfCsYKyjnvaXEQ">
    <bindings xmi:id="_RVh0FUQHEfCsYKyjnvaXEQ" keySequence="CTRL+/" command="_RVRVp0QHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVjCQ0QHEfCsYKyjnvaXEQ" keySequence="F3" command="_RVRV40QHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVjpYEQHEfCsYKyjnvaXEQ" keySequence="CTRL+\" command="_RVQHWEQHEfCsYKyjnvaXEQ"/>
  </bindingTables>
  <bindingTables xmi:id="_RVh0F0QHEfCsYKyjnvaXEQ" elementId="org.omnetpp.context.inifileEditor" bindingContext="_RVTKnEQHEfCsYKyjnvaXEQ">
    <bindings xmi:id="_RVh0GEQHEfCsYKyjnvaXEQ" keySequence="CTRL+/" command="_RVO5T0QHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVh0JkQHEfCsYKyjnvaXEQ" keySequence="CTRL+SHIFT+O" command="_RVQHSkQHEfCsYKyjnvaXEQ"/>
  </bindingTables>
  <bindingTables xmi:id="_RVh0HEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.debug.ui.debugging" bindingContext="_RVTKmUQHEfCsYKyjnvaXEQ">
    <bindings xmi:id="_RVh0HUQHEfCsYKyjnvaXEQ" keySequence="SHIFT+F8" command="_RVQuekQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVh0HkQHEfCsYKyjnvaXEQ" keySequence="SHIFT+F6" command="_RVPgcUQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVh0H0QHEfCsYKyjnvaXEQ" keySequence="SHIFT+F7" command="_RVPgMUQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVh0IEQHEfCsYKyjnvaXEQ" keySequence="CTRL+F5" command="_RVQuk0QHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVjCNEQHEfCsYKyjnvaXEQ" keySequence="SHIFT+F5" command="_RVO5QUQHEfCsYKyjnvaXEQ"/>
  </bindingTables>
  <bindingTables xmi:id="_RVh0LEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.context" bindingContext="_RVTKlkQHEfCsYKyjnvaXEQ">
    <bindings xmi:id="_RVh0LUQHEfCsYKyjnvaXEQ" keySequence="HOME" command="_RVO5aEQHEfCsYKyjnvaXEQ"/>
    <bindings xmi:id="_RVjpQ0QHEfCsYKyjnvaXEQ" keySequence="CTRL+G" command="_RVNrBEQHEfCsYKyjnvaXEQ"/>
  </bindingTables>
  <bindingTables xmi:id="_RVjCUEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.ReflogView" bindingContext="_RVTKk0QHEfCsYKyjnvaXEQ">
    <bindings xmi:id="_RVjCUUQHEfCsYKyjnvaXEQ" keySequence="CTRL+C" command="_RVPgW0QHEfCsYKyjnvaXEQ"/>
  </bindingTables>
  <bindingTables xmi:id="_RgRrgUQHEfCsYKyjnvaXEQ" bindingContext="_RgRrgEQHEfCsYKyjnvaXEQ"/>
  <bindingTables xmi:id="_RgSSkUQHEfCsYKyjnvaXEQ" bindingContext="_RgSSkEQHEfCsYKyjnvaXEQ"/>
  <bindingTables xmi:id="_RgSSk0QHEfCsYKyjnvaXEQ" bindingContext="_RgSSkkQHEfCsYKyjnvaXEQ"/>
  <bindingTables xmi:id="_RgSSlUQHEfCsYKyjnvaXEQ" bindingContext="_RgSSlEQHEfCsYKyjnvaXEQ"/>
  <bindingTables xmi:id="_RgSSl0QHEfCsYKyjnvaXEQ" bindingContext="_RgSSlkQHEfCsYKyjnvaXEQ"/>
  <bindingTables xmi:id="_RgSSmUQHEfCsYKyjnvaXEQ" bindingContext="_RgSSmEQHEfCsYKyjnvaXEQ"/>
  <bindingTables xmi:id="_RgSSm0QHEfCsYKyjnvaXEQ" bindingContext="_RgSSmkQHEfCsYKyjnvaXEQ"/>
  <bindingTables xmi:id="_RgSSnUQHEfCsYKyjnvaXEQ" bindingContext="_RgSSnEQHEfCsYKyjnvaXEQ"/>
  <bindingTables xmi:id="_RgSSn0QHEfCsYKyjnvaXEQ" bindingContext="_RgSSnkQHEfCsYKyjnvaXEQ"/>
  <bindingTables xmi:id="_RgSSoUQHEfCsYKyjnvaXEQ" bindingContext="_RgSSoEQHEfCsYKyjnvaXEQ"/>
  <bindingTables xmi:id="_RgSSo0QHEfCsYKyjnvaXEQ" bindingContext="_RgSSokQHEfCsYKyjnvaXEQ"/>
  <bindingTables xmi:id="_RgSSpUQHEfCsYKyjnvaXEQ" bindingContext="_RgSSpEQHEfCsYKyjnvaXEQ"/>
  <bindingTables xmi:id="_RgS5oUQHEfCsYKyjnvaXEQ" bindingContext="_RgS5oEQHEfCsYKyjnvaXEQ"/>
  <bindingTables xmi:id="_RgS5o0QHEfCsYKyjnvaXEQ" bindingContext="_RgS5okQHEfCsYKyjnvaXEQ"/>
  <bindingTables xmi:id="_RgS5pUQHEfCsYKyjnvaXEQ" bindingContext="_RgS5pEQHEfCsYKyjnvaXEQ"/>
  <bindingTables xmi:id="_RgS5p0QHEfCsYKyjnvaXEQ" bindingContext="_RgS5pkQHEfCsYKyjnvaXEQ"/>
  <bindingTables xmi:id="_RgS5qUQHEfCsYKyjnvaXEQ" bindingContext="_RgS5qEQHEfCsYKyjnvaXEQ"/>
  <bindingTables xmi:id="_RgS5q0QHEfCsYKyjnvaXEQ" bindingContext="_RgS5qkQHEfCsYKyjnvaXEQ"/>
  <bindingTables xmi:id="_RgS5rUQHEfCsYKyjnvaXEQ" bindingContext="_RgS5rEQHEfCsYKyjnvaXEQ"/>
  <bindingTables xmi:id="_RgS5r0QHEfCsYKyjnvaXEQ" bindingContext="_RgS5rkQHEfCsYKyjnvaXEQ"/>
  <bindingTables xmi:id="_RgS5sUQHEfCsYKyjnvaXEQ" bindingContext="_RgS5sEQHEfCsYKyjnvaXEQ"/>
  <bindingTables xmi:id="_RgS5s0QHEfCsYKyjnvaXEQ" bindingContext="_RgS5skQHEfCsYKyjnvaXEQ"/>
  <bindingTables xmi:id="_RgTgsUQHEfCsYKyjnvaXEQ" bindingContext="_RgTgsEQHEfCsYKyjnvaXEQ"/>
  <bindingTables xmi:id="_RgTgs0QHEfCsYKyjnvaXEQ" bindingContext="_RgTgskQHEfCsYKyjnvaXEQ"/>
  <bindingTables xmi:id="_RgTgtUQHEfCsYKyjnvaXEQ" bindingContext="_RgTgtEQHEfCsYKyjnvaXEQ"/>
  <bindingTables xmi:id="_RgTgt0QHEfCsYKyjnvaXEQ" bindingContext="_RgTgtkQHEfCsYKyjnvaXEQ"/>
  <bindingTables xmi:id="_RgTguUQHEfCsYKyjnvaXEQ" bindingContext="_RgTguEQHEfCsYKyjnvaXEQ"/>
  <bindingTables xmi:id="_RgTgu0QHEfCsYKyjnvaXEQ" bindingContext="_RgTgukQHEfCsYKyjnvaXEQ"/>
  <bindingTables xmi:id="_RgTgvUQHEfCsYKyjnvaXEQ" bindingContext="_RgTgvEQHEfCsYKyjnvaXEQ"/>
  <bindingTables xmi:id="_RgTgv0QHEfCsYKyjnvaXEQ" bindingContext="_RgTgvkQHEfCsYKyjnvaXEQ"/>
  <bindingTables xmi:id="_RgTgwUQHEfCsYKyjnvaXEQ" bindingContext="_RgTgwEQHEfCsYKyjnvaXEQ"/>
  <bindingTables xmi:id="_RgTgw0QHEfCsYKyjnvaXEQ" bindingContext="_RgTgwkQHEfCsYKyjnvaXEQ"/>
  <bindingTables xmi:id="_RgUHwEQHEfCsYKyjnvaXEQ" bindingContext="_RgTgxEQHEfCsYKyjnvaXEQ"/>
  <bindingTables xmi:id="_RgUHwkQHEfCsYKyjnvaXEQ" bindingContext="_RgUHwUQHEfCsYKyjnvaXEQ"/>
  <rootContext xmi:id="_RU9MWEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.contexts.dialogAndWindow" contributorURI="platform:/plugin/org.eclipse.ui.workbench" name="In Dialogs and Windows" description="Either a dialog or a window is open">
    <children xmi:id="_RU9MWUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.contexts.window" contributorURI="platform:/plugin/org.eclipse.ui.workbench" name="In Windows" description="A window is open">
      <children xmi:id="_RU9MWkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.e4.ui.contexts.views" contributorURI="platform:/plugin/org.eclipse.ui.workbench" name="%bindingcontext.name.bindingView"/>
      <children xmi:id="_RVTKkEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.compare.compareEditorScope" name="Comparing in an Editor" description="Comparing in an Editor"/>
      <children xmi:id="_RVTKkkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.RepositoriesView" name="In Git Repositories View"/>
      <children xmi:id="_RVTKk0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.ReflogView" name="In Git Reflog View"/>
      <children xmi:id="_RVTKlEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.ui.cViewScope" name="In C/C++ Views" description="In C/C++ Views"/>
      <children xmi:id="_RVTKlUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.debug.ui.debugging" name="Debugging" description="Debugging programs">
        <children xmi:id="_RVTKlkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.context" name="In Disassembly" description="When debugging in assembly mode"/>
        <children xmi:id="_RVTKl0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.debug.ui.memory.abstractasynctablerendering" name="In Table Memory Rendering" description="In Table Memory Rendering"/>
        <children xmi:id="_RVTKmUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.debug.ui.debugging" name="Debugging C/C++" description="Debugging C/C++ Programs"/>
      </children>
      <children xmi:id="_RVTKmEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.debug.ui.BreakpointView" name="In Breakpoints View" description="The breakpoints view context"/>
      <children xmi:id="_RVTKmkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.debug.ui.console" name="In I/O Console" description="In I/O console"/>
      <children xmi:id="_RVTKm0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.textEditorScope" name="Editing Text" description="Editing Text Context">
        <children xmi:id="_RVTKnEQHEfCsYKyjnvaXEQ" elementId="org.omnetpp.context.inifileEditor" name="Editing INI File" description="OMNeT++ INI File Editor"/>
        <children xmi:id="_RVTKnUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.make.ui.makefileEditorScope" name="Makefile Editor" description="Editor for makefiles"/>
        <children xmi:id="_RVTKnkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.ui.cEditorScope" name="C/C++ Editor" description="Editor for C/C++ Source Files"/>
        <children xmi:id="_RVTKn0QHEfCsYKyjnvaXEQ" elementId="org.omnetpp.context.msgEditor" name="Editing MSG Source" description="OMNeT++ MSG File Editor"/>
        <children xmi:id="_RVTKpUQHEfCsYKyjnvaXEQ" elementId="org.omnetpp.context.nedEditor" name="Editing NED File" description="OMNeT++ NED Editor">
          <children xmi:id="_RVTKpkQHEfCsYKyjnvaXEQ" elementId="org.omnetpp.context.nedGraphEditor" name="Editing NED Graphically" description="OMNeT++ NED Graphical Editor"/>
          <children xmi:id="_RVTKp0QHEfCsYKyjnvaXEQ" elementId="org.omnetpp.context.nedTextEditor" name="Editing NED Source" description="OMNeT++ NED Source Editor"/>
        </children>
      </children>
      <children xmi:id="_RVTKoUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.debug.ui.memoryview" name="In Memory View" description="In memory view"/>
      <children xmi:id="_RVTKokQHEfCsYKyjnvaXEQ" elementId="org.omnetpp.context.EventLogTable" name="EventLogTableContext" description="OMNeT++ Eventlog Table"/>
      <children xmi:id="_RVTKo0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.console.ConsoleView" name="In Console View" description="In Console View"/>
      <children xmi:id="_RVTKpEQHEfCsYKyjnvaXEQ" elementId="org.omnetpp.context.SequenceChart" name="Viewing a Sequence Chart" description="OMNeT++ Sequence Chart"/>
    </children>
    <children xmi:id="_RU9MW0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.contexts.dialog" contributorURI="platform:/plugin/org.eclipse.ui.workbench" name="In Dialogs" description="A dialog is open"/>
    <children xmi:id="_RVTKqEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.ui.macroExpansionHoverScope" name="In Macro Expansion Hover" description="In Macro Expansion Hover"/>
  </rootContext>
  <rootContext xmi:id="_RVTKkUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.contexts.workbenchMenu" name="Workbench Menu" description="When no Workbench windows are active"/>
  <rootContext xmi:id="_RVTKoEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.contexts.actionSet" name="Action Set" description="Parent context for action sets"/>
  <rootContext xmi:id="_RgRrgEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.debug.ui.debugActionSet" name="Auto::org.eclipse.cdt.debug.ui.debugActionSet"/>
  <rootContext xmi:id="_RgSSkEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.debug.ui.reverseDebuggingActionSet" name="Auto::org.eclipse.cdt.debug.ui.reverseDebuggingActionSet"/>
  <rootContext xmi:id="_RgSSkkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.debug.ui.tracepointActionSet" name="Auto::org.eclipse.cdt.debug.ui.tracepointActionSet"/>
  <rootContext xmi:id="_RgSSlEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.debug.ui.debugViewLayoutActionSet" name="Auto::org.eclipse.cdt.debug.ui.debugViewLayoutActionSet"/>
  <rootContext xmi:id="_RgSSlkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.dsf.debug.ui.updateModes" name="Auto::org.eclipse.cdt.dsf.debug.ui.updateModes"/>
  <rootContext xmi:id="_RgSSmEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.make.ui.updateActionSet" name="Auto::org.eclipse.cdt.make.ui.updateActionSet"/>
  <rootContext xmi:id="_RgSSmkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.make.ui.makeTargetActionSet" name="Auto::org.eclipse.cdt.make.ui.makeTargetActionSet"/>
  <rootContext xmi:id="_RgSSnEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.ui.CodingActionSet" name="Auto::org.eclipse.cdt.ui.CodingActionSet"/>
  <rootContext xmi:id="_RgSSnkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.ui.SearchActionSet" name="Auto::org.eclipse.cdt.ui.SearchActionSet"/>
  <rootContext xmi:id="_RgSSoEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.ui.NavigationActionSet" name="Auto::org.eclipse.cdt.ui.NavigationActionSet"/>
  <rootContext xmi:id="_RgSSokQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.ui.OpenActionSet" name="Auto::org.eclipse.cdt.ui.OpenActionSet"/>
  <rootContext xmi:id="_RgSSpEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.ui.buildConfigActionSet" name="Auto::org.eclipse.cdt.ui.buildConfigActionSet"/>
  <rootContext xmi:id="_RgS5oEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.ui.CElementCreationActionSet" name="Auto::org.eclipse.cdt.ui.CElementCreationActionSet"/>
  <rootContext xmi:id="_RgS5okQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.ui.text.c.actionSet.presentation" name="Auto::org.eclipse.cdt.ui.text.c.actionSet.presentation"/>
  <rootContext xmi:id="_RgS5pEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.debug.ui.breakpointActionSet" name="Auto::org.eclipse.debug.ui.breakpointActionSet"/>
  <rootContext xmi:id="_RgS5pkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.debug.ui.debugActionSet" name="Auto::org.eclipse.debug.ui.debugActionSet"/>
  <rootContext xmi:id="_RgS5qEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.debug.ui.launchActionSet" name="Auto::org.eclipse.debug.ui.launchActionSet"/>
  <rootContext xmi:id="_RgS5qkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.debug.ui.profileActionSet" name="Auto::org.eclipse.debug.ui.profileActionSet"/>
  <rootContext xmi:id="_RgS5rEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.gitaction" name="Auto::org.eclipse.egit.ui.gitaction"/>
  <rootContext xmi:id="_RgS5rkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.navigation" name="Auto::org.eclipse.egit.ui.navigation"/>
  <rootContext xmi:id="_RgS5sEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.cheatsheets.actionSet" name="Auto::org.eclipse.ui.cheatsheets.actionSet"/>
  <rootContext xmi:id="_RgS5skQHEfCsYKyjnvaXEQ" elementId="org.eclipse.search.searchActionSet" name="Auto::org.eclipse.search.searchActionSet"/>
  <rootContext xmi:id="_RgTgsEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.team.ui.actionSet" name="Auto::org.eclipse.team.ui.actionSet"/>
  <rootContext xmi:id="_RgTgskQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.edit.text.actionSet.annotationNavigation" name="Auto::org.eclipse.ui.edit.text.actionSet.annotationNavigation"/>
  <rootContext xmi:id="_RgTgtEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.edit.text.actionSet.navigation" name="Auto::org.eclipse.ui.edit.text.actionSet.navigation"/>
  <rootContext xmi:id="_RgTgtkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo" name="Auto::org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo"/>
  <rootContext xmi:id="_RgTguEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.externaltools.ExternalToolsSet" name="Auto::org.eclipse.ui.externaltools.ExternalToolsSet"/>
  <rootContext xmi:id="_RgTgukQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.NavigateActionSet" name="Auto::org.eclipse.ui.NavigateActionSet"/>
  <rootContext xmi:id="_RgTgvEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.actionSet.keyBindings" name="Auto::org.eclipse.ui.actionSet.keyBindings"/>
  <rootContext xmi:id="_RgTgvkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.WorkingSetModificationActionSet" name="Auto::org.eclipse.ui.WorkingSetModificationActionSet"/>
  <rootContext xmi:id="_RgTgwEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.WorkingSetActionSet" name="Auto::org.eclipse.ui.WorkingSetActionSet"/>
  <rootContext xmi:id="_RgTgwkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.actionSet.openFiles" name="Auto::org.eclipse.ui.actionSet.openFiles"/>
  <rootContext xmi:id="_RgTgxEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.edit.text.actionSet.presentation" name="Auto::org.eclipse.ui.edit.text.actionSet.presentation"/>
  <rootContext xmi:id="_RgUHwUQHEfCsYKyjnvaXEQ" elementId="org.omnetpp.ned.ActionSet" name="Auto::org.omnetpp.ned.ActionSet"/>
  <descriptors xmi:id="_RWpOYEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.e4.ui.compatibility.editor" allowMultiple="true" category="org.eclipse.e4.primaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor">
    <tags>Editor</tags>
  </descriptors>
  <descriptors xmi:id="_RWqcgEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.codan.internal.ui.views.ProblemDetails" label="Problem Details" iconURI="platform:/plugin/org.eclipse.cdt.codan.ui/icons/edit_bug.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:&amp;C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_RWut8EQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.debug.ui.executablesView" label="Executables" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui/icons/obj16/exec_view_obj.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_RWvVAEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.debug.ui.SignalsView" label="Signals" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui/icons/view16/signals_view.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_RWv8EEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.dsf.gdb.ui.tracecontrol.view" label="Trace Control" iconURI="platform:/plugin/org.eclipse.cdt.dsf.gdb.ui/icons/full/view16/tracecontrol_view.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_RWwjIEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.dsf.gdb.ui.osresources.view" label="OS Resources" iconURI="platform:/plugin/org.eclipse.cdt.dsf.gdb.ui/icons/full/view16/osresources_view.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_RWwjIUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.view" label="Disassembly" iconURI="platform:/plugin/org.eclipse.cdt.dsf.ui/icons/disassembly.gif" allowMultiple="true" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_RWxKMEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.make.ui.views.MakeView" label="Make Target" iconURI="platform:/plugin/org.eclipse.cdt.make.ui/icons/view16/make_target.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Make</tags>
  </descriptors>
  <descriptors xmi:id="_RWxxQEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.ui.CView" label="C/C++ Projects" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/cview.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:&amp;C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_RWy_YEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.ui.IndexView" label="C/C++ Index" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/types.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:&amp;C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_RWy_YUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.ui.includeBrowser" label="Include Browser" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/includeBrowser.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:&amp;C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_RWzmcEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.ui.callHierarchy" label="Call Hierarchy" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/call_hierarchy.gif" allowMultiple="true" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:&amp;C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_RWzmcUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.ui.typeHierarchy" label="Type Hierarchy" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/class_hi.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:&amp;C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_RW0NgEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.texteditor.TemplatesView" label="Templates" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/templates.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_RW0NgUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.debug.ui.DebugView" label="Debug" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/debug_view.png" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_RW00kEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.debug.ui.BreakpointView" label="Breakpoints" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/breakpoint_view.png" allowMultiple="true" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_RW3Q0EQHEfCsYKyjnvaXEQ" elementId="org.eclipse.debug.ui.VariableView" label="Variables" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/variable_view.png" allowMultiple="true" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_RW3Q0UQHEfCsYKyjnvaXEQ" elementId="org.eclipse.debug.ui.ExpressionView" label="Expressions" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/watchlist_view.png" allowMultiple="true" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_RW334EQHEfCsYKyjnvaXEQ" elementId="org.eclipse.debug.ui.RegisterView" label="Registers" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/register_view.png" allowMultiple="true" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_RW4e8EQHEfCsYKyjnvaXEQ" elementId="org.eclipse.debug.ui.ModuleView" label="Modules" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/module_view.png" allowMultiple="true" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_RW4e8UQHEfCsYKyjnvaXEQ" elementId="org.eclipse.debug.ui.MemoryView" label="Memory" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/memory_view.png" allowMultiple="true" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_RW5GAEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.RepositoriesView" label="Git Repositories" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/repo_rep.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_RW5tEEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.StagingView" label="Git Staging" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/staging.png" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_RW6UIEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.InteractiveRebaseView" label="Git Interactive Rebase" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/rebase_interactive.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_RW6UIUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.CompareTreeView" label="Git Tree Compare" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/obj16/gitrepository.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_RW6UIkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.ReflogView" label="Git Reflog" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/reflog.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_RW67MEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.gef.ui.palette_view" label="Palette" iconURI="platform:/plugin/org.eclipse.gef/icons/palette_view.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_RW7iQEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.help.ui.HelpView" label="Help" iconURI="platform:/plugin/org.eclipse.help.ui/icons/view16/help_view.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Help</tags>
  </descriptors>
  <descriptors xmi:id="_RW8JUEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.linuxtools.dataviewers.charts.view" label="Chart View" iconURI="platform:/plugin/org.eclipse.linuxtools.dataviewers.charts/icons/chart_icon.png" allowMultiple="true" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Charts</tags>
  </descriptors>
  <descriptors xmi:id="_RW8JUUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.linuxtools.valgrind.ui.valgrindview" label="Valgrind" iconURI="platform:/plugin/org.eclipse.linuxtools.valgrind.ui/icons/valgrind-icon.png" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Profiling</tags>
  </descriptors>
  <descriptors xmi:id="_RW8wYEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.search.SearchResultView" label="Classic Search" iconURI="platform:/plugin/org.eclipse.search/icons/full/eview16/searchres.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_RW9XcEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.search.ui.views.SearchView" label="Search" iconURI="platform:/plugin/org.eclipse.search/icons/full/eview16/searchres.gif" allowMultiple="true" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_RW9-gEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.team.sync.views.SynchronizeView" label="Synchronize" iconURI="platform:/plugin/org.eclipse.team.ui/icons/full/eview16/synch_synch.gif" allowMultiple="true" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Team</tags>
  </descriptors>
  <descriptors xmi:id="_RW_MoEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.team.ui.GenericHistoryView" label="History" iconURI="platform:/plugin/org.eclipse.team.ui/icons/full/eview16/history_view.gif" allowMultiple="true" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Team</tags>
  </descriptors>
  <descriptors xmi:id="_RW_MoUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.internal.introview" label="Welcome" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_RW_zsEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.browser.view" label="Internal Web Browser" iconURI="platform:/plugin/org.eclipse.ui.browser/icons/obj16/internal_browser.gif" allowMultiple="true" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_RXAawEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" label="Cheat Sheets" iconURI="platform:/plugin/org.eclipse.ui.cheatsheets/icons/view16/cheatsheet_view.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Help</tags>
  </descriptors>
  <descriptors xmi:id="_RXBB0EQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.console.ConsoleView" label="Console" iconURI="platform:/plugin/org.eclipse.ui.console/icons/full/cview16/console_view.png" allowMultiple="true" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_RXBB0UQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.views.ProgressView" label="Progress" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/pview.png" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_RXBo4EQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.views.ResourceNavigator" label="Navigator" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/filenav_nav.png" category="org.eclipse.e4.primaryNavigationStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_RXBo4UQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.views.BookmarkView" label="Bookmarks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/bkmrk_nav.png" allowMultiple="true" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_RXCP8EQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.views.TaskList" label="Tasks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/tasks_tsk.png" allowMultiple="true" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_RXCP8UQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.views.ProblemView" label="Problems" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" allowMultiple="true" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_RXC3AEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.views.AllMarkersView" label="Markers" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" allowMultiple="true" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_RXC3AUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.navigator.ProjectExplorer" label="Project Explorer" iconURI="platform:/plugin/org.eclipse.ui.navigator.resources/icons/full/eview16/resource_persp.gif" category="org.eclipse.e4.primaryNavigationStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_RXDeEEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.views.PropertySheet" label="Properties" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/prop_ps.png" allowMultiple="true" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_RXEFIEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.views.ContentOutline" label="Outline" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/outline_co.png" category="org.eclipse.e4.secondaryNavigationStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_RXEFIUQHEfCsYKyjnvaXEQ" elementId="org.omnetpp.eventlogtable.editors.EventLogTableView" label="Event Log" iconURI="platform:/plugin/org.omnetpp.eventlogtable/icons/eventlog.png" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:OMNeT++</tags>
  </descriptors>
  <descriptors xmi:id="_RXEsMEQHEfCsYKyjnvaXEQ" elementId="org.omnetpp.inifile.ModuleHierarchy" label="Module Hierarchy" iconURI="platform:/plugin/org.omnetpp.inifile.editor/icons/full/eview16/modulehierarchy.png" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:OMNeT++</tags>
  </descriptors>
  <descriptors xmi:id="_RXFTQEQHEfCsYKyjnvaXEQ" elementId="org.omnetpp.inifile.ModuleParameters" label="NED Parameters" iconURI="platform:/plugin/org.omnetpp.inifile.editor/icons/full/eview16/moduleparameters.png" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:OMNeT++</tags>
  </descriptors>
  <descriptors xmi:id="_RXFTQUQHEfCsYKyjnvaXEQ" elementId="org.omnetpp.inifile.NedInheritance" label="NED Inheritance" iconURI="platform:/plugin/org.omnetpp.inifile.editor/icons/full/eview16/inheritance.png" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:OMNeT++</tags>
  </descriptors>
  <descriptors xmi:id="_RXF6UEQHEfCsYKyjnvaXEQ" elementId="org.omnetpp.main.NewVersionView" label="New OMNeT++ Version" iconURI="platform:/plugin/org.omnetpp.main/icons/logo16.png" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:OMNeT++</tags>
  </descriptors>
  <descriptors xmi:id="_RXF6UUQHEfCsYKyjnvaXEQ" elementId="org.omnetpp.scave.VectorBrowserView" label="Output Vector" iconURI="platform:/plugin/org.omnetpp.scave/icons/full/eview16/outvector.png" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:OMNeT++</tags>
  </descriptors>
  <descriptors xmi:id="_RXGhYEQHEfCsYKyjnvaXEQ" elementId="org.omnetpp.scave.DatasetView" label="Dataset" iconURI="platform:/plugin/org.omnetpp.scave/icons/full/eview16/dataset.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:OMNeT++</tags>
  </descriptors>
  <descriptors xmi:id="_RXGhYUQHEfCsYKyjnvaXEQ" elementId="org.omnetpp.sequencechart.editors.SequenceChartView" label="Sequence Chart" iconURI="platform:/plugin/org.omnetpp.sequencechart/icons/full/eview16/sequencechart.png" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:OMNeT++</tags>
  </descriptors>
  <descriptors xmi:id="_RXHIcEQHEfCsYKyjnvaXEQ" elementId="com.swtworkbench.community.xswt.editor.views.XSWTPreview" label="XSWT Preview" iconURI="platform:/plugin/org.swtworkbench.xswt/icons/xswt.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:XSWT</tags>
  </descriptors>
  <trimContributions xmi:id="_2r10UF9tEeO-yojH_y4TJA" elementId="org.eclipse.ui.ide.application.trimcontribution.QuickAccess" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" toBeRendered="false" parentId="org.eclipse.ui.main.toolbar" positionInParent="last">
    <children xsi:type="menu:ToolControl" xmi:id="_76uUAF9tEeO-yojH_y4TJA" elementId="Spacer Glue" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
      <tags>glue</tags>
      <tags>move_after:PerspectiveSpacer</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
    <children xsi:type="menu:ToolControl" xmi:id="_8tJPcF9tEeO-yojH_y4TJA" elementId="SearchField" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.quickaccess.SearchField">
      <tags>move_after:Spacer Glue</tags>
      <tags>HIDEABLE</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
    <children xsi:type="menu:ToolControl" xmi:id="_9LgmcF9tEeO-yojH_y4TJA" elementId="Search-PS Glue" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
      <tags>glue</tags>
      <tags>move_after:SearchField</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
  </trimContributions>
  <commands xmi:id="_RVNrAEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.debug.ui.command.nextpage" commandName="Next Page of Memory" description="Load next page of memory" category="_RVND-UQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVNrAUQHEfCsYKyjnvaXEQ" elementId="org.omnetpp.main.commands.reportBug" commandName="Report Bug or Enhancement..." description="Report a bug or enhancement in the OMNeT++ bugtracker" category="_RVNEB0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVNrAkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.ui.search.findrefs.project" commandName="References in Project" description="Search for references to the selected element in the enclosing project" category="_RVNEA0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVNrA0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.edit.text.removeTrailingWhitespace" commandName="Remove Trailing Whitespace" description="Removes the trailing whitespace of each line" category="_RVND-EQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVNrBEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.commands.gotoAddress" commandName="Go to Address..." description="Navigate to address" category="_RVND-UQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVNrBUQHEfCsYKyjnvaXEQ" elementId="org.omnetpp.eventlogtable.gotoNextModuleEvent" commandName="Go to Next Module Event" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVNrBkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.edit.text.showChangeRulerInformation" commandName="Show Quick Diff Ruler Tooltip" description="Displays quick diff or revision information for the caret line in a focused hover" category="_RVND_kQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVNrB0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.RepositoriesViewClone" commandName="Clone a Git Repository" category="_RVND-0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVOSEEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.ui.edit.open.quick.type.hierarchy" commandName="Quick Type Hierarchy" description="Shows quick type hierarchy" category="_RVNEA0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVOSEUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.edit.text.select.textStart" commandName="Select Text Start" description="Select to the beginning of the text" category="_RVND_kQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVOSEkQHEfCsYKyjnvaXEQ" elementId="org.omnetpp.cdt.gotoCppDefinitionForNedType" commandName="Open C++ Definition" description="Opens the C++ definition for a NED type (simple module or channel)" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVOSE0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.debug.ui.command.groupDebugContexts" commandName="Group" description="Groups the selected debug contexts" category="_RVND_UQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVOSFEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.RepositoriesViewRefresh" commandName="Refresh" category="_RVND-0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVOSFUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.cheatsheets.openCheatSheetURL" commandName="Open Cheat Sheet from URL" description="Open a Cheat Sheet from file at a specified URL." category="_RVNEB0QHEfCsYKyjnvaXEQ">
    <parameters xmi:id="_RVOSFkQHEfCsYKyjnvaXEQ" elementId="cheatSheetId" name="Identifier" optional="false"/>
    <parameters xmi:id="_RVOSF0QHEfCsYKyjnvaXEQ" elementId="name" name="Name" optional="false"/>
    <parameters xmi:id="_RVOSGEQHEfCsYKyjnvaXEQ" elementId="url" name="URL" optional="false"/>
  </commands>
  <commands xmi:id="_RVOSGUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.project.buildAll" commandName="Build All" description="Build all projects" category="_RVND9EQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVOSGkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.window.lockToolBar" commandName="Lock the Toolbars" description="Lock the Toolbars" category="_RVND8UQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVOSG0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.navigate.expandAll" commandName="Expand All" description="Expand the current tree" category="_RVNEC0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVOSHEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.dsf.gdb.ui.command.selectNextTraceRecord" commandName="Next Trace Record" description="Select Next Trace Record" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVOSHUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.file.import" commandName="Import" description="Import" category="_RVND-EQHEfCsYKyjnvaXEQ">
    <parameters xmi:id="_RVOSHkQHEfCsYKyjnvaXEQ" elementId="importWizardId" name="Import Wizard"/>
  </commands>
  <commands xmi:id="_RVOSH0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.edit.text.select.lineEnd" commandName="Select Line End" description="Select to the end of the line of text" category="_RVND_kQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVOSIEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.help.helpSearch" commandName="Help Search" description="Open the help search" category="_RVNEB0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVOSIUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.team.CompareWithHead" commandName="Compare with HEAD Revision" category="_RVND-0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVOSIkQHEfCsYKyjnvaXEQ" elementId="org.omnetpp.eventlogtable.gotoEventCause" commandName="Go to Event Cause" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVOSI0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.edit.text.smartEnterInverse" commandName="Insert Line Above Current Line" description="Adds a new line above the current line" category="_RVND_kQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVOSJEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.ui.refactor.extract.local.variable" commandName="Extract Local Variable - Refactoring " description="Extract a local variable for the selected expression" category="_RVND80QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVOSJUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.externalTools.commands.OpenExternalToolsConfigurations" commandName="External Tools..." description="Open external tools launch configuration dialog" category="_RVND-UQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVOSJkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.edit.text.goto.windowEnd" commandName="Window End" description="Go to the end of the window" category="_RVND_kQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVOSJ0QHEfCsYKyjnvaXEQ" elementId="org.omnetpp.sequencechart.findNext" commandName="Find Next" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVOSKEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.ui.edit.text.c.add.include" commandName="Add Include" description="Create include statement on selection" category="_RVNEA0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVOSKUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.team.ui.TeamSynchronizingPerspective" commandName="Team Synchronizing" description="Open the Team Synchronizing Perspective" category="_RVND8EQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVOSKkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.help.ui.indexcommand" commandName="Index" description="Show Keyword Index" category="_RVNEB0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVOSK0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.debug.ui.commands.StepOver" commandName="Step Over" description="Step over" category="_RVND-UQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVOSLEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.ui.hover.backwardMacroExpansion" commandName="Back" description="Step backward in macro expansions" category="_RVNEA0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVOSLUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.RepositoriesViewOpenInEditor" commandName="Open in Editor" category="_RVND-0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVOSLkQHEfCsYKyjnvaXEQ" elementId="org.omnetpp.ned.cleanupNedFiles" commandName="Clean Up NED Files..." description="Clean up and organize all import statements in NED files" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVOSL0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.history.RenameBranch" commandName="Rename Branch..." category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVOSMEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.linuxtools.valgrind.launch.exportCommand" commandName="Export Valgrind Log Files" description="Exports Valgrind log output to a directory" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVOSMUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.debug.ui.commands.addMemoryMonitor" commandName="Add Memory Block" description="Add memory block" category="_RVND-UQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVOSMkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.RepositoriesViewRebase" commandName="Rebase on" category="_RVND-0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVOSM0QHEfCsYKyjnvaXEQ" elementId="org.omnetpp.msg.editor.CorrectIndentation" commandName="Correct Indentation" description="Reindent selected lines of the MSG source" category="_RVND_kQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVOSNEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.edit.revertToSaved" commandName="Revert to Saved" description="Revert to the last saved state" category="_RVNEAEQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVOSNUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.team.Rebase" commandName="Rebase on" category="_RVND-0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVOSNkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.window.pinEditor" commandName="Pin Editor" description="Pin the current editor" category="_RVND8UQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVOSN0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.commit.Reword" commandName="Reword Commit" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVOSOEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.team.Tag" commandName="Tag" category="_RVND-0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVOSOUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.team.ShowRepositoriesView" commandName="Show Git Repositories View" category="_RVND-0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVOSOkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.edit.text.folding.restore" commandName="Reset Structure" description="Resets the folding structure" category="_RVND_kQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVOSO0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.file.restartWorkbench" commandName="Restart" description="Restart the workbench" category="_RVND-EQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVOSPEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.RepositoriesViewConfigurePush" commandName="Configure Push..." category="_RVND-0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVOSPUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.file.export" commandName="Export" description="Export" category="_RVND-EQHEfCsYKyjnvaXEQ">
    <parameters xmi:id="_RVOSPkQHEfCsYKyjnvaXEQ" elementId="exportWizardId" name="Export Wizard"/>
  </commands>
  <commands xmi:id="_RVOSP0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.debug.command.breakpointProperties" commandName="C/C++ Breakpoint Properties" description="View and edit properties for a given C/C++ breakpoint" category="_RVND-UQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVOSQEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.edit.text.select.lineDown" commandName="Select Line Down" description="Extend the selection to the next line of text" category="_RVND_kQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVOSQUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.PushHeadToGerrit" commandName="Push Current Head to Gerrit" category="_RVND-0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVOSQkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ltk.ui.refactoring.commands.renameResource" commandName="Rename Resource" description="Rename the selected resource and notify LTK participants." category="_RVNECkQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVO5IEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.window.resetPerspective" commandName="Reset Perspective" description="Reset the current perspective to its default state" category="_RVND8UQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVO5IUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.window.showContextMenu" commandName="Show Context Menu" description="Show the context menu" category="_RVND8UQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVO5IkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.team.stash.drop" commandName="Delete Stashed Commit..." category="_RVND-0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVO5I0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.ConfigureUpstreamPush" commandName="Configure Upstream Push" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVO5JEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.window.savePerspective" commandName="Save Perspective As" description="Save the current perspective" category="_RVND8UQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVO5JUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.edit.text.showInformation" commandName="Show Tooltip Description" description="Displays information for the current caret location in a focused hover" category="_RVND_kQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVO5JkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.ide.markCompleted" commandName="Mark Completed" description="Mark the selected tasks as completed" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVO5J0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.team.ConfigureFetch" commandName="Configure Upstream Fetch" category="_RVND-0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVO5KEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.debug.ui.commands.nextMemoryBlock" commandName="Next Memory Monitor" description="Show renderings from next memory monitor." category="_RVND-UQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVO5KUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.ui.refactor.extract.constant" commandName="Extract Constant - Refactoring " description="Extract a constant for the selected expression" category="_RVND80QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVO5KkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.edit.text.toggleOverwrite" commandName="Toggle Overwrite" description="Toggle overwrite mode" category="_RVND_kQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVO5K0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.edit.text.goto.textEnd" commandName="Text End" description="Go to the end of the text" category="_RVND_kQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVO5LEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.history.Squash" commandName="Squash Commits" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVO5LUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.edit.delete" commandName="Delete" description="Delete the selection" category="_RVNEAEQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVO5LkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.window.showKeyAssist" commandName="Show Key Assist" description="Show the key assist dialog" category="_RVND8UQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVO5L0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.file.saveAs" commandName="Save As" description="Save the current contents to another location" category="_RVND-EQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVO5MEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.RepositoriesViewChangeCredentials" commandName="Change Credentials" category="_RVND-0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVO5MUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.ide.showInSystemExplorer" commandName="Show In (System Explorer)" description="Show in system's explorer (file manager)" category="_RVNEC0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVO5MkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.search.ui.performTextSearchWorkspace" commandName="Find Text in Workspace" description="Searches the files in the workspace for specific text." category="_RVND_0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVO5M0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.ui.refactor.getters.and.setters" commandName="Generate Getters and Setters..." description="Generates getters and setters for a selected field" category="_RVNEA0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVO5NEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.ui.refactor.hide.method" commandName="Hide Member Function..." category="_RVND80QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVO5NUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.history.CreateTag" commandName="Create Tag..." category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVO5NkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.window.openEditorDropDown" commandName="Quick Switch Editor" description="Open the editor drop down list" category="_RVND8UQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVO5N0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.debug.ui.commands.ProfileLast" commandName="Profile" description="Launch in profile mode" category="_RVND-UQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVO5OEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.ui.specific_content_assist.command" commandName="C/C++ Content Assist" description="A parameterizable command that invokes content assist with a single completion proposal category" category="_RVNEAEQHEfCsYKyjnvaXEQ">
    <parameters xmi:id="_RVO5OUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.ui.specific_content_assist.category_id" name="type" optional="false"/>
  </commands>
  <commands xmi:id="_RVO5OkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.search.ui.performTextSearchFile" commandName="Find Text in File" description="Searches the files in the file for specific text." category="_RVND_0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVO5O0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.ui.refactor.extract.function" commandName="Extract Function - Refactoring " description="Extract a function for the selected list of expressions or statements" category="_RVND80QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVO5PEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.RepositoriesViewPaste" commandName="Paste Repository Path or URI" category="_RVND-0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVO5PUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.edit.paste" commandName="Paste" description="Paste from the clipboard" category="_RVNEAEQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVO5PkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.navigate.previous" commandName="Previous" description="Navigate to the previous item" category="_RVNEC0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVO5P0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.RepositoriesViewCopyPath" commandName="Copy Path to Clipboard" category="_RVND-0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVO5QEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.team.Ignore" commandName="Ignore" category="_RVND-0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVO5QUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.debug.ui.command.reverseStepInto" commandName="Reverse Step Into" description="Perform Reverse Step Into" category="_RVNEAkQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVO5QkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.team.RemoveFromIndex" commandName="Remove from Git Index" category="_RVND-0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVO5Q0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.ui.edit.open.quick.macro.explorer" commandName="Explore Macro Expansion" description="Opens a quick view for macro expansion exploration" category="_RVNEA0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVO5REQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.edit.text.goto.lineUp" commandName="Line Up" description="Go up one line of text" category="_RVND_kQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVO5RUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.file.newQuickMenu" commandName="New menu" description="Open the New menu" category="_RVND-EQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVO5RkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.edit.text.deleteNext" commandName="Delete Next" description="Delete the next character" category="_RVND_kQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVO5R0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.edit.text.deleteNextWord" commandName="Delete Next Word" description="Delete the next word" category="_RVND_kQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVO5SEQHEfCsYKyjnvaXEQ" elementId="org.omnetpp.eventlogtable.findText" commandName="Find..." category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVO5SUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.debug.ui.command.castToType" commandName="Cast To Type..." category="_RVND-kQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVO5SkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.edit.undo" commandName="Undo" description="Undo the last operation" category="_RVNEAEQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVO5S0QHEfCsYKyjnvaXEQ" elementId="org.omnetpp.ned.editor.text.DistributeAllGateLabels" commandName="Distribute Gate Labels" description="Distribute gate labels to connected gates" category="_RVND_kQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVO5TEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.debug.ui.commands.DebugLast" commandName="Debug" description="Launch in debug mode" category="_RVND-UQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVO5TUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.navigate.back" commandName="Back" description="Navigate back" category="_RVNEC0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVO5TkQHEfCsYKyjnvaXEQ" elementId="org.omnetpp.sequencechart.gotoSimulationTime" commandName="Go to Simulation Time..." category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVO5T0QHEfCsYKyjnvaXEQ" elementId="org.omnetpp.inifile.editor.text.ToggleComment" commandName="Toggle Comment" description="Comment/Uncomment the selected lines" category="_RVND_kQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVO5UEQHEfCsYKyjnvaXEQ" elementId="org.omnetpp.cdt.projectFeaturesCommand" commandName="Project &amp;Features..." category="_RVND9EQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVO5UUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.ui.excludeCommand" commandName="Exclude from Build" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVO5UkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.debug.ui.commands.ToggleLineBreakpoint" commandName="Toggle Line Breakpoint" description="Creates or removes a line breakpoint" category="_RVND-UQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVO5U0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.editors.lineNumberToggle" commandName="Show Line Numbers" description="Toggle display of line numbers" category="_RVND_kQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVO5VEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.debug.ui.commands.ToggleStepFilters" commandName="Use Step Filters" description="Toggles enablement of debug step filters" category="_RVND-UQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVO5VUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.RepositoriesLinkWithSelection" commandName="Link with Selection" category="_RVND-0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVO5VkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.org.eclipse.egit.ui.AbortRebase" commandName="Abort Rebase" category="_RVND-0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVO5V0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.team.Fetch" commandName="Fetch" category="_RVND-0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVO5WEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.window.newEditor" commandName="New Editor" description="Open another editor on the active editor's input" category="_RVND8UQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVO5WUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.history.SetQuickdiffBaseline" commandName="Set quickdiff baseline" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVO5WkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.edit.text.goto.wordPrevious" commandName="Previous Word" description="Go to the previous word" category="_RVND_kQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVO5W0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.RepositoriesViewDelete" commandName="Delete Repository" category="_RVND-0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVO5XEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.history.ShowBlame" commandName="Show Annotations" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVO5XUQHEfCsYKyjnvaXEQ" elementId="org.omnetpp.ned.editor.text.ToggleComment" commandName="Toggle Comment" description="Comment/Uncomment the selected lines" category="_RVND_kQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVO5XkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.edit.text.recenter" commandName="Recenter" description="Scroll cursor line to center, top and bottom" category="_RVND_kQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVO5X0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.team.Pull" commandName="Pull" category="_RVND-0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVO5YEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.help.installationDialog" commandName="Installation Information" description="Open the installation dialog" category="_RVNEB0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVO5YUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.debug.ui.command.restoreDefaultType" commandName="Restore Original Type" description="View and edit properties for a given C/C++ breakpoint" category="_RVND-kQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVO5YkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.team.CompareWithIndex" commandName="Compare with Git Index" category="_RVND-0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVO5Y0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.edit.text.shiftRight" commandName="Shift Right" description="Shift a block of text to the right" category="_RVNEAEQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVO5ZEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.ui.edit.text.c.indent" commandName="Indent Line" description="Indents the current line" category="_RVNEA0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVO5ZUQHEfCsYKyjnvaXEQ" elementId="org.omnetpp.ned.openNedType" commandName="Open NED Type" description="Open NED Type" category="_RVNEC0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVO5ZkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.file.refresh" commandName="Refresh" description="Refresh the selected items" category="_RVND-EQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVO5Z0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.gef.ui.palette_view" commandName="Palette" category="_RVND9UQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVO5aEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.commands.gotoPC" commandName="Go to Program Counter" description="Navigate to current program counter" category="_RVND-UQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVO5aUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.history.Merge" commandName="Merge" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVO5akQHEfCsYKyjnvaXEQ" elementId="org.eclipse.search.ui.performTextSearchWorkingSet" commandName="Find Text in Working Set" description="Searches the files in the working set for specific text." category="_RVND_0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVO5a0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.edit.text.delete.line.to.end" commandName="Delete to End of Line" description="Delete to the end of a line of text" category="_RVND_kQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVO5bEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.history.CreateBranch" commandName="Create Branch" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVO5bUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.window.nextEditor" commandName="Next Editor" description="Switch to the next editor" category="_RVND8UQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVO5bkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.team.ShowBlame" commandName="Show Annotations" category="_RVND-0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVO5b0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.navigate.forward" commandName="Forward" description="Navigate forward" category="_RVNEC0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVO5cEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.edit.text.goto.pageUp" commandName="Page Up" description="Go up one page" category="_RVND_kQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVO5cUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.file.closeAllSaved" commandName="Close All Saved" description="Close all saved editors" category="_RVND-EQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVO5ckQHEfCsYKyjnvaXEQ" elementId="org.omnetpp.common.installSimulationModels" commandName="Install Simulation Models..." description="Install Simulation Models" category="_RVNEB0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVO5c0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.debug.ui.commands.OpenRunConfigurations" commandName="Run..." description="Open run launch configuration dialog" category="_RVND-UQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVPgMEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.team.AssumeUnchanged" commandName="Assume Unchanged" category="_RVND-0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVPgMUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.debug.ui.command.uncall" commandName="Uncall" description="Perform Uncall" category="_RVNEAkQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVPgMkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.edit.text.deletePreviousWord" commandName="Delete Previous Word" description="Delete the previous word" category="_RVND_kQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVPgM0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.edit.text.deletePrevious" commandName="Delete Previous" description="Delete the previous character" category="_RVND_kQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVPgNEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.debug.ui.commands.SkipAllBreakpoints" commandName="Skip All Breakpoints" description="Sets whether or not any breakpoint should suspend execution" category="_RVND-UQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVPgNUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.edit.text.set.mark" commandName="Set Mark" description="Set the mark" category="_RVND_kQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVPgNkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.team.OpenCommit" commandName="Open Git Commit" category="_RVND-0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVPgN0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.team.RenameBranch" commandName="Rename Branch" category="_RVND-0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVPgOEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.ui.menu.rebuildIndex" commandName="Rebuild Index" category="_RVND9EQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVPgOUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.team.AddToIndex" commandName="Add to Git Index" category="_RVND-0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVPgOkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.epp.mpc.ui.command.showMarketplaceWizard" commandName="Eclipse Marketplace" description="Show the Eclipse Marketplace wizard" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVPgO0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.file.exit" commandName="Exit" description="Exit the application" category="_RVND-EQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVPgPEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.ui.navigate.open.type.in.hierarchy" commandName="Open Type in Hierarchy" description="Open a type in the type hierarchy view" category="_RVNEC0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVPgPUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.team.ConfigurePush" commandName="Configure Upstream Push" category="_RVND-0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVPgPkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.window.customizePerspective" commandName="Customize Perspective" description="Customize the current perspective" category="_RVND8UQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVPgP0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.navigate.showInQuickMenu" commandName="Show In..." description="Open the Show In menu" category="_RVNEC0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVPgQEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.ui.edit.text.c.organize.includes" commandName="Organize Includes" description="Evaluates all required includes and replaces the current includes" category="_RVNEA0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVPgQUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.ide.deleteCompleted" commandName="Delete Completed Tasks" description="Delete the tasks marked as completed" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVPgQkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.debug.ui.DebugPerspective" commandName="Debug" description="Open the debug perspective" category="_RVND8EQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVPgQ0QHEfCsYKyjnvaXEQ" elementId="org.omnetpp.eventlogtable.refresh" commandName="Refresh" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVPgREQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.CheckoutCommand" commandName="Checkout" category="_RVND-0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVPgRUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ltk.ui.refactoring.commands.deleteResources" commandName="Delete Resources" description="Delete the selected resources and notify LTK participants." category="_RVNECkQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVPgRkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.edit.rename" commandName="Rename" description="Rename the selected item" category="_RVND-EQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVPgR0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.navigate.next" commandName="Next" description="Navigate to the next item" category="_RVNEC0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVPgSEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.project.buildAutomatically" commandName="Build Automatically" description="Toggle the workspace build automatically function" category="_RVND9EQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVPgSUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.internal.reflog.OpenInCommitViewerCommand" commandName="Open in Commit Viewer" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVPgSkQHEfCsYKyjnvaXEQ" elementId="org.omnetpp.cdt.cleanSelectedProjects" commandName="Clean Local" description="Clean the selected project(s) without cleaning referred projects." category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVPgS0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.edit.text.select.lineUp" commandName="Select Line Up" description="Extend the selection to the previous line of text" category="_RVND_kQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVPgTEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.team.ShowHistory" commandName="Show in History" category="_RVND-0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVPgTUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.compare.compareWithOther" commandName="Compare With Other Resource" description="Compare resources, clipboard contents or editors" category="_RVNECEQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVPgTkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.ui.edit.text.c.goto.next.bookmark" commandName="Next Bookmark" description="Goto next bookmark of the selected file" category="_RVNEA0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVPgT0QHEfCsYKyjnvaXEQ" elementId="org.omnetpp.common.handlers.fullScreen" commandName="Full Screen" category="_RVND9EQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVPgUEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.browser.openBundleResource" commandName="Open Resource in Browser" description="Opens a bundle resource in the default web browser." category="_RVND8UQHEfCsYKyjnvaXEQ">
    <parameters xmi:id="_RVPgUUQHEfCsYKyjnvaXEQ" elementId="plugin" name="Plugin"/>
    <parameters xmi:id="_RVPgUkQHEfCsYKyjnvaXEQ" elementId="path" name="Path"/>
  </commands>
  <commands xmi:id="_RVPgU0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.team.stash.apply" commandName="Apply Stashed Changes" category="_RVND-0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVPgVEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.ContinueRebase" commandName="Continue Rebase" category="_RVND-0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVPgVUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.team.Push" commandName="Push" category="_RVND-0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVPgVkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.compare.copyAllLeftToRight" commandName="Copy All from Left to Right" description="Copy All Changes from Left to Right" category="_RVNECEQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVPgV0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.edit.text.toggleShowWhitespaceCharacters" commandName="Show Whitespace Characters" description="Shows whitespace characters in current text editor" category="_RVND_kQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVPgWEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.edit.selectAll" commandName="Select All" description="Select all" category="_RVNEAEQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVPgWUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.window.preferences" commandName="Preferences" description="Open the preferences dialog" category="_RVND8UQHEfCsYKyjnvaXEQ">
    <parameters xmi:id="_RVPgWkQHEfCsYKyjnvaXEQ" elementId="preferencePageId" name="Preference Page"/>
  </commands>
  <commands xmi:id="_RVPgW0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.internal.reflog.CopyCommand" commandName="Copy SHA-1" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVPgXEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.file.close" commandName="Close" description="Close the active editor" category="_RVND-EQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVPgXUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.ui.edit.text.c.comment" commandName="Comment" description="Turn the selected lines into // style comments" category="_RVNEA0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVPgXkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.newWizard" commandName="New" description="Open the New item wizard" category="_RVND-EQHEfCsYKyjnvaXEQ">
    <parameters xmi:id="_RVPgX0QHEfCsYKyjnvaXEQ" elementId="newWizardId" name="New Wizard"/>
  </commands>
  <commands xmi:id="_RVPgYEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.help.ui.ignoreMissingPlaceholders" commandName="Do not warn of missing documentation" description="Sets the help preferences to no longer report a warning about the current set of missing documents." category="_RVNEB0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVPgYUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.help.tipsAndTricksAction" commandName="Tips and Tricks" description="Open the tips and tricks help page" category="_RVNEB0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVPgYkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.ui.edit.text.c.find.word" commandName="Find Word" description="Select a word and find the next occurrence" category="_RVNEA0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVPgY0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.edit.copy" commandName="Copy" description="Copy the selection to the clipboard" category="_RVNEAEQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVPgZEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.ui.deleteConfigsCommand" commandName="Reset to Default" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVPgZUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.history.Revert" commandName="Revert Commit" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVPgZkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.RepositoriesViewCreateBranch" commandName="Create Branch..." category="_RVND-0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVPgZ0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.debug.ui.commands.RunToLine" commandName="Run to Line" description="Resume and break when execution reaches the current line" category="_RVND-UQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVPgaEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.ui.edit.text.c.select.previous" commandName="Select Previous C/C++ Element" description="Expand the selection to enclosing C/C++ element" category="_RVNEAEQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVPgaUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.ui.refactor.toggle.function" commandName="Toggle Function - Refactoring " description="Toggles the implementation between header and implementation file" category="_RVND80QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVPgakQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.ide.copyConfigCommand" commandName="Copy Configuration Data To Clipboard" description="Copies the configuration data (system properties, installed bundles, etc) to the clipboard." category="_RVNEAEQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVPga0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.debug.ui.commands.Restart" commandName="Restart" description="Restart a process or debug target without terminating and re-launching" category="_RVND-UQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVPgbEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.part.previousPage" commandName="Previous Page" description="Switch to the previous page" category="_RVNEC0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVPgbUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.navigate.openResource" commandName="Open Resource" description="Open an editor on a particular resource" category="_RVNEC0QHEfCsYKyjnvaXEQ">
    <parameters xmi:id="_RVPgbkQHEfCsYKyjnvaXEQ" elementId="filePath" name="File Path" typeId="org.eclipse.ui.ide.resourcePath"/>
  </commands>
  <commands xmi:id="_RVPgb0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.team.Synchronize" commandName="Synchronize" category="_RVND-0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVPgcEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.ui.edit.opencview" commandName="Show in C/C++ Project view" description="Show the selected resource in the C/C++ Project view" category="_RVNEA0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVPgcUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.debug.ui.command.reverseStepOver" commandName="Reverse Step Over" description="Perform Reverse Step Over" category="_RVNEAkQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVPgckQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.help.helpContents" commandName="Help Contents" description="Open the help contents" category="_RVNEB0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVPgc0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.file.saveAll" commandName="Save All" description="Save all current contents" category="_RVND-EQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVPgdEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.team.GarbageCollect" commandName="Collect Garbage" category="_RVND-0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVPgdUQHEfCsYKyjnvaXEQ" elementId="org.omnetpp.ned.editor.text.CorrectIndentation" commandName="Correct Indentation" description="Reindent selected lines of the NED source" category="_RVND_kQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVPgdkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.edit.text.select.textEnd" commandName="Select Text End" description="Select to the end of the text" category="_RVND_kQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVPgd0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.edit.text.delete.line" commandName="Delete Line" description="Delete a line of text" category="_RVND_kQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVPgeEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.team.PushBranch" commandName="Push Branch..." category="_RVND-0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVPgeUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.window.showViewMenu" commandName="Show View Menu" description="Show the view menu" category="_RVND8UQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVPgekQHEfCsYKyjnvaXEQ" elementId="org.eclipse.linuxtools.valgrind.launch.clearMarkersCommand" commandName="Remove Valgrind Markers" description="Removes all Valgrind markers" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVPge0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.edit.findIncremental" commandName="Incremental Find" description="Incremental find" category="_RVNEAEQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVPgfEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.search.ui.performTextSearchProject" commandName="Find Text in Project" description="Searches the files in the project for specific text." category="_RVND_0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVPgfUQHEfCsYKyjnvaXEQ" elementId="org.omnetpp.sequencechart.findText" commandName="Find..." category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVPgfkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.make.ui.targetBuildLastCommand" commandName="Rebuild Last Target" description="Rebuild the last make target for the selected container or project." category="_RVND9EQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVPgf0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.history.DeleteBranch" commandName="Delete Branch..." category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVPggEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.window.previousEditor" commandName="Previous Editor" description="Switch to the previous editor" category="_RVND8UQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVPggUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.window.maximizePart" commandName="Maximize Active View or Editor" description="Toggles maximize/restore state of active view or editor" category="_RVND8UQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVPggkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.ide.configureColumns" commandName="Configure Columns..." description="Configure the columns in the markers view" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVPgg0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.debug.ui.command.debugNewExecutable" commandName="Debug New Executable" description="Debug a new executable" category="_RVND_UQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVPghEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.editors.revisions.id.toggle" commandName="Toggle Revision Id Display" description="Toggles the display of the revision id" category="_RVND_kQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVPghUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.edit.text.folding.collapse_all" commandName="Collapse All" description="Collapses all folded regions" category="_RVND_kQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVPghkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.commit.CherryPick" commandName="Cherry Pick" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVPgh0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.debug.ui.commands.eof" commandName="EOF" description="Send end of file" category="_RVND-UQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVPgiEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.perspectives.showPerspective" commandName="Show Perspective" description="Show a particular perspective" category="_RVND8EQHEfCsYKyjnvaXEQ">
    <parameters xmi:id="_RVPgiUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.perspectives.showPerspective.perspectiveId" name="Parameter"/>
    <parameters xmi:id="_RVQHQEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.perspectives.showPerspective.newWindow" name="In New Window"/>
  </commands>
  <commands xmi:id="_RVQHQUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.navigate.previousSubTab" commandName="Previous Sub-Tab" description="Switch to the previous sub-tab" category="_RVNEC0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQHQkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.editors.quickdiff.revert" commandName="Revert Lines" description="Revert the current selection, block or deleted lines" category="_RVND_kQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQHQ0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.edit.text.smartEnter" commandName="Insert Line Below Current Line" description="Adds a new line below the current line" category="_RVND_kQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQHREQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.edit.text.goto.pageDown" commandName="Page Down" description="Go down one page" category="_RVND_kQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQHRUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.team.Reset" commandName="Reset..." category="_RVND-0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQHRkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.edit.text.goto.windowStart" commandName="Window Start" description="Go to the start of the window" category="_RVND_kQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQHR0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.debug.ui.command.stopTracing" commandName="Stop Tracing " description="Stop Tracing Experiment" category="_RVNECUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQHSEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.navigate.goInto" commandName="Go Into" description="Navigate into the selected item" category="_RVNEC0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQHSUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.debug.ui.command.ungroupDebugContexts" commandName="Ungroup" description="Ungroups the selected debug contexts" category="_RVND_UQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQHSkQHEfCsYKyjnvaXEQ" elementId="org.omnetpp.inifile.editor.text.AddMissingKeys" commandName="Add Missing Keys" description="Add missing keys to the INI file" category="_RVND_kQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQHS0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.edit.text.contentAssist.proposals" commandName="Content Assist" description="Content Assist" category="_RVNEAEQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQHTEQHEfCsYKyjnvaXEQ" elementId="org.omnetpp.ned.editor.text.InferAllGateLabels" commandName="Infer Gate Labels" description="Infer gate labels from connected gates" category="_RVND_kQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQHTUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.edit.text.folding.expand_all" commandName="Expand All" description="Expands all folded regions" category="_RVND_kQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQHTkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.navigate.nextTab" commandName="Next Tab" description="Switch to the next tab" category="_RVNEC0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQHT0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.ui.edit.text.c.add.block.comment" commandName="Add Block Comment" description="Enclose the selection with a block comment" category="_RVNEA0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQHUEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.team.Discard" commandName="Replace with File in Git Index" category="_RVND-0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQHUUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.ide.OpenMarkersView" commandName="Open Another" description="Open another view" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQHUkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.ui.edit.text.c.goto.matching.bracket" commandName="Go to Matching Bracket" description="Moves the cursor to the matching bracket" category="_RVNEA0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQHU0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.debug.ui.commands.Terminate" commandName="Terminate" description="Terminate" category="_RVND-UQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQHVEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.team.NoAssumeUnchanged" commandName="No Assume Unchanged" category="_RVND-0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQHVUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.ui.edit.text.c.sort.lines" commandName="Sort Lines" description="Sort selected lines alphabetically" category="_RVNEA0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQHVkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.edit.text.select.windowEnd" commandName="Select Window End" description="Select to the end of the window" category="_RVND_kQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQHV0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.debug.ui.command.gotoaddress" commandName="Go to Address" description="Go to Address" category="_RVND-UQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQHWEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.make.ui.edit.text.makefile.uncomment" commandName="Uncomment" description="Uncomment the selected # style comment lines" category="_RVND90QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQHWUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.command.shareProject" commandName="Share with Git" description="Share the project using Git" category="_RVNEAUQHEfCsYKyjnvaXEQ">
    <parameters xmi:id="_RVQHWkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.command.projectNameParameter" name="Project" optional="false"/>
  </commands>
  <commands xmi:id="_RVQHW0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.debug.ui.commands.Suspend" commandName="Suspend" description="Suspend" category="_RVND-UQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQHXEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.ui.search.findrefs.workingset" commandName="References in Working Set" description="Search for references to the selected element in a working set" category="_RVNEA0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQHXUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.window.switchToEditor" commandName="Switch to Editor" description="Switch to an editor" category="_RVND8UQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQHXkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.window.previousView" commandName="Previous View" description="Switch to the previous view" category="_RVND8UQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQHX0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.ui.navigate.open.element.in.call.hierarchy" commandName="Open Element in Call Hierarchy" description="Open an element in the call hierarchy view" category="_RVNEC0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQHYEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.debug.ui.commands.closeRendering" commandName="Close Rendering" description="Close the selected rendering." category="_RVND-UQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQHYUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.navigate.linkWithEditor" commandName="Toggle Link with Editor " description="Toggles linking of a view's selection with the active editor's selection" category="_RVNEC0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQHYkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.team.ui.synchronizeAll" commandName="Synchronize..." description="Synchronize resources in the workspace with another location" category="_RVNEBkQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQHY0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.help.displayHelp" commandName="Display Help" description="Display a Help topic" category="_RVNEB0QHEfCsYKyjnvaXEQ">
    <parameters xmi:id="_RVQHZEQHEfCsYKyjnvaXEQ" elementId="href" name="Help topic href"/>
  </commands>
  <commands xmi:id="_RVQHZUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.team.Branch" commandName="Branch" category="_RVND-0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQHZkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.window.previousPerspective" commandName="Previous Perspective" description="Switch to the previous perspective" category="_RVND8UQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQHZ0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.project.closeProject" commandName="Close Project" description="Close the selected project" category="_RVND9EQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQHaEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.team.Disconnect" commandName="Disconnect" category="_RVND-0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQHaUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.ui.refactoring.command.ExtractConstant" commandName="Extract Constant..." category="_RVND80QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQHakQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.FetchGerritChange" commandName="Fetch From Gerrit" category="_RVND-0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQHa0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.edit.text.folding.expand" commandName="Expand" description="Expands the folded region at the current selection" category="_RVND_kQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQHbEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.RepositoriesViewRemove" commandName="Remove Repository" category="_RVND-0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQHbUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.team.PushTags" commandName="Push Tags..." category="_RVND-0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQHbkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.edit.text.goto.lineStart" commandName="Line Start" description="Go to the start of the line of text" category="_RVND_kQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQHb0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.ui.edit.text.c.select.next" commandName="Select Next C/C++ Element" description="Expand the selection to next C/C++ element" category="_RVNEAEQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQHcEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.help.ui.closeTray" commandName="Close User Assistance Tray" description="Close the user assistance tray containing context help information and cheat sheets." category="_RVNEB0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQHcUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.project.properties" commandName="Properties" description="Display the properties of the selected item's project " category="_RVND9EQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQHckQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.edit.text.toggleBlockSelectionMode" commandName="Toggle Block Selection" description="Toggle block / column selection in the current text editor" category="_RVNEAEQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQHc0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.file.save" commandName="Save" description="Save the current contents" category="_RVND-EQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQHdEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.history.CompareWithWorkingTree" commandName="Compare with Working Directory" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQHdUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.debug.ui.commands.ToggleWatchpoint" commandName="Toggle Watchpoint" description="Creates or removes a watchpoint" category="_RVND-UQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQHdkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.file.closePart" commandName="Close Part" description="Close the active workbench part" category="_RVND8UQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQHd0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.RepositoriesViewConfigureBranch" commandName="Configure Branch" category="_RVND-0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQHeEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.commit.Squash" commandName="Squash Commits" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQHeUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.history.OpenInCommitViewerCommand" commandName="Open in Commit Viewer" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQHekQHEfCsYKyjnvaXEQ" elementId="org.eclipse.gef.zoom_in" commandName="Zoom In" description="Zoom In" category="_RVNEBUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQHe0QHEfCsYKyjnvaXEQ" elementId="org.omnetpp.ned.editor.text.FormatSource" commandName="Format NED Source" description="Format the NED source file" category="_RVND_kQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQHfEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.jdt.ui.edit.text.java.correction.assist.proposals" commandName="Quick Fix" description="Suggest possible fixes for a problem" category="_RVNEAEQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQHfUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.editors.revisions.rendering.cycle" commandName="Cycle Revision Coloring Mode" description="Cycles through the available coloring modes for revisions" category="_RVND_kQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQHfkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.edit.text.showRulerContextMenu" commandName="Show Ruler Context Menu" description="Show the context menu for the ruler" category="_RVND8UQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQHf0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.debug.ui.commands.ToggleMethodBreakpoint" commandName="Toggle Method Breakpoint" description="Creates or removes a method breakpoint" category="_RVND-UQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQHgEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.window.minimizePart" commandName="Minimize Active View or Editor" description="Minimizes the active view or editor" category="_RVND8UQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQHgUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.team.Untrack" commandName="Untrack" category="_RVND-0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQHgkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.ui.menu.updateUnresolvedIncludes" commandName="Re-resolve Unresolved Includes" category="_RVND9EQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQHg0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.part.nextPage" commandName="Next Page" description="Switch to the next page" category="_RVNEC0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQHhEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.team.ReplaceWithRef" commandName="Replace with branch, tag, or reference" category="_RVND-0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQHhUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.edit.text.delete.line.to.beginning" commandName="Delete to Beginning of Line" description="Delete to the beginning of a line of text" category="_RVND_kQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQHhkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.navigate.backwardHistory" commandName="Backward History" description="Move backward in the editor navigation history" category="_RVNEC0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQHh0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.edit.text.swap.mark" commandName="Swap Mark" description="Swap the mark with the cursor position" category="_RVND_kQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQHiEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.ui.refactoring.command.ExtractLocalVariable" commandName="Extract Local Variable..." category="_RVND80QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQHiUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.project.buildProject" commandName="Build Project" description="Build the selected project" category="_RVND9EQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQHikQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.debug.ui.command.saveTraceData" commandName="Save Trace Data " description="Save Trace Data to File" category="_RVNECUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQHi0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.window.showSystemMenu" commandName="Show System Menu" description="Show the system menu" category="_RVND8UQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQHjEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.edit.text.lowerCase" commandName="To Lower Case" description="Changes the selection to lower case" category="_RVND_kQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQHjUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.ui.edit.text.c.goto.next.member" commandName="Go to Next Member" description="Move the caret to the next member of the translation unit" category="_RVNEA0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQHjkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.edit.text.select.pageDown" commandName="Select Page Down" description="Select to the bottom of the page" category="_RVND_kQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQHj0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.debug.ui.commands.newRendering" commandName="New Rendering" description="Add a new rendering." category="_RVND-UQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQHkEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.ui.menu.freshenAllFiles" commandName="Freshen All Files in Index" category="_RVND9EQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQHkUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.equinox.p2.ui.discovery.commands.ShowBundleCatalog" commandName="Show Bundle Catalog" category="_RVNEAUQHEfCsYKyjnvaXEQ">
    <parameters xmi:id="_RVQHkkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.equinox.p2.ui.discovery.commands.DirectoryParameter" name="Directory URL"/>
    <parameters xmi:id="_RVQHk0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.equinox.p2.ui.discovery.commands.TagsParameter" name="Tags"/>
  </commands>
  <commands xmi:id="_RVQHlEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.team.SimplePush" commandName="Push to Upstream" category="_RVND-0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQHlUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.team.MergeTool" commandName="Merge Tool" category="_RVND-0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQHlkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.edit.text.open.hyperlink" commandName="Open Hyperlink" description="Opens the hyperlink at the caret location or opens a chooser if more than one hyperlink is available" category="_RVND_kQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQHl0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.history.OpenInTextEditorCommand" commandName="Open in Text Editor" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQHmEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.edit.text.openLocalFile" commandName="Open File..." description="Open a file" category="_RVND-EQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQHmUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.window.splitEditor" commandName="Toggle Split Editor" description="Split or join the currently active editor." category="_RVND8UQHEfCsYKyjnvaXEQ">
    <parameters xmi:id="_RVQHmkQHEfCsYKyjnvaXEQ" elementId="Splitter.isHorizontal" name="Orientation" optional="false"/>
  </commands>
  <commands xmi:id="_RVQHm0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.compare.selectPreviousChange" commandName="Select Previous Change" description="Select Previous Change" category="_RVNECEQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQHnEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.gef.zoom_out" commandName="Zoom Out" description="Zoom Out" category="_RVNEBUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQHnUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.edit.text.toggleShowSelectedElementOnly" commandName="Show Selected Element Only" description="Show Selected Element Only" category="_RVND8UQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQHnkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.edit.text.select.wordPrevious" commandName="Select Previous Word" description="Select the previous word" category="_RVND_kQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQHn0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.history.Reset" commandName="Reset..." category="_RVNEAUQHEfCsYKyjnvaXEQ">
    <parameters xmi:id="_RVQHoEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.history.ResetMode" name="Reset mode" optional="false"/>
  </commands>
  <commands xmi:id="_RVQuUEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.ToggleCoolbarAction" commandName="Toggle Toolbar Visibility" description="Toggles the visibility of the window toolbar" category="_RVND8UQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQuUUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.RebaseInteractiveCurrent" commandName="%RebaseInteractiveCurrentHandler.name" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQuUkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.debug.ui.command.reverseToggle" commandName="Reverse Toggle" description="Toggle Reverse Debugging" category="_RVNEAkQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQuU0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.history.CompareVersionsInTree" commandName="Compare in Tree" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQuVEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.ui.edit.opendecl" commandName="Open Declaration" description="Open an editor on the selected element's declaration(s)" category="_RVNEA0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQuVUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.project.openProject" commandName="Open Project" description="Open a project" category="_RVND9EQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQuVkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.edit.cut" commandName="Cut" description="Cut the selection to the clipboard" category="_RVNEAEQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQuV0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.commit.Edit" commandName="Edit Commit" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQuWEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.edit.text.moveLineDown" commandName="Move Lines Down" description="Moves the selected lines down" category="_RVND_kQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQuWUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.edit.findReplace" commandName="Find and Replace" description="Find and replace text" category="_RVNEAEQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQuWkQHEfCsYKyjnvaXEQ" elementId="org.omnetpp.ned.editor.text.OrganizeImports" commandName="Organize Imports" description="Organizes the import statements in a NED source file" category="_RVND_kQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQuW0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.compare.copyLeftToRight" commandName="Copy from Left to Right" description="Copy Current Change from Left to Right" category="_RVNECEQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQuXEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.team.ui.applyPatch" commandName="Apply Patch..." description="Apply a patch to one or more workspace projects." category="_RVNEBkQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQuXUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.quickdiff.toggle" commandName="Quick Diff Toggle" description="Toggles quick diff information display on the line number ruler" category="_RVNEAEQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQuXkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.debug.ui.command.startTracing" commandName="Start Tracing " description="Start Tracing Experiment" category="_RVNECUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQuX0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.ltk.ui.refactoring.commands.moveResources" commandName="Move Resources" description="Move the selected resources and notify LTK participants." category="_RVNECkQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQuYEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.RepositoriesViewRenameBranch" commandName="Rename Branch..." category="_RVND-0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQuYUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.edit.text.toggleInsertMode" commandName="Toggle Insert Mode" description="Toggle insert mode" category="_RVNEAEQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQuYkQHEfCsYKyjnvaXEQ" elementId="org.omnetpp.ned.editor.text.ConvertToNewFormat" commandName="Convert to 4.x Format" description="Convert the NED source to the OMNeT++ 4.x format" category="_RVND_kQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQuY0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.edit.text.goto.line" commandName="Go to Line" description="Go to a specified line of text" category="_RVNEC0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQuZEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.navigate.nextSubTab" commandName="Next Sub-Tab" description="Switch to the next sub-tab" category="_RVNEC0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQuZUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.team.stash.create" commandName="Stash Changes" category="_RVND-0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQuZkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.RepositoriesViewRemoveRemote" commandName="Delete Remote" category="_RVND-0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQuZ0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.ui.edit.text.c.toggleMarkOccurrences" commandName="Toggle Mark Occurrences" description="Toggles mark occurrences in C/C++ editors" category="_RVNEA0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQuaEQHEfCsYKyjnvaXEQ" elementId="org.omnetpp.eventlogtable.gotoPreviousModuleEvent" commandName="Go to Previous Module Event" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQuaUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.edit.text.goto.columnPrevious" commandName="Previous Column" description="Go to the previous column" category="_RVND_kQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQuakQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.externaltools.ExternalToolMenuDelegateToolbar" commandName="Run Last Launched External Tool" description="Runs the last launched external Tool" category="_RVND-UQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQua0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.team.Commit" commandName="Commit..." category="_RVND-0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQubEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.ui.search.findrefs" commandName="References" description="Search for references to the selected element in the workspace" category="_RVNEA0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQubUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.edit.addBookmark" commandName="Add Bookmark" description="Add a bookmark" category="_RVNEAEQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQubkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.edit.text.select.lineStart" commandName="Select Line Start" description="Select to the beginning of the line of text" category="_RVND_kQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQub0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.history.CheckoutCommand" commandName="Checkout" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQucEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.edit.text.cut.line.to.end" commandName="Cut to End of Line" description="Cut to the end of a line of text" category="_RVND_kQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQucUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.ui.search.finddecl.project" commandName="Declaration in Project" description="Search for declarations of the selected element in the enclosing project" category="_RVNEA0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQuckQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.RepositoriesViewAddRepository" commandName="Add a Git Repository" category="_RVND-0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQuc0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.ui.hover.forwardMacroExpansion" commandName="Forward" description="Step forward in macro expansions" category="_RVNEA0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQudEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.equinox.p2.ui.sdk.update" commandName="Check for Updates" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQudUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.edit.findIncrementalReverse" commandName="Incremental Find Reverse" description="Incremental find reverse" category="_RVNEAEQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQudkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.project.rebuildAll" commandName="Rebuild All" description="Rebuild all projects" category="_RVND9EQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQud0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.window.activateEditor" commandName="Activate Editor" description="Activate the editor" category="_RVND8UQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQueEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.compare.copyAllRightToLeft" commandName="Copy All from Right to Left" description="Copy All Changes from Right to Left" category="_RVNECEQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQueUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.history.PushCommit" commandName="Push Commit..." category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQuekQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.debug.ui.command.reverseResume" commandName="Reverse Resume" description="Perform Reverse Resume" category="_RVNEAkQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQue0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.project.closeUnrelatedProjects" commandName="Close Unrelated Projects" description="Close unrelated projects" category="_RVND9EQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQufEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.edit.text.goto.lineDown" commandName="Line Down" description="Go down one line of text" category="_RVND_kQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQufUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.RepositoriesViewConfigureGerritRemote" commandName="Gerrit Configuration..." category="_RVND-0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQufkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.debug.ui.commands.ToggleBreakpoint" commandName="Toggle Breakpoint" description="Creates or removes a breakpoint" category="_RVND-UQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQuf0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.compare.ignoreWhiteSpace" commandName="Ignore White Space" description="Ignore white space where applicable" category="_RVNECEQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQugEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.navigate.previousTab" commandName="Previous Tab" description="Switch to the previous tab" category="_RVNEC0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQugUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.edit.text.gotoLastEditPosition" commandName="Last Edit Location" description="Last edit location" category="_RVNEC0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQugkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.SkipRebase" commandName="Skip commit and continue" category="_RVND-0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQug0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.debug.ui.commands.toggleMemoryMonitorsPane" commandName="Toggle Memory Monitors Pane" description="Toggle visibility of the Memory Monitors Pane" category="_RVND-UQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQuhEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.RebaseCurrent" commandName="Rebase" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQuhUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.ui.edit.open.type.hierarchy" commandName="Open Type Hierarchy" description="Open a type hierarchy on the selected element" category="_RVNEC0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQuhkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.ide.copyBuildIdCommand" commandName="Copy Build Id To Clipboard" description="Copies the build id to the clipboard." category="_RVNEAEQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQuh0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.dialogs.openInputDialog" commandName="Open Input Dialog" description="Open an Input Dialog" category="_RVND_EQHEfCsYKyjnvaXEQ">
    <parameters xmi:id="_RVQuiEQHEfCsYKyjnvaXEQ" elementId="title" name="Title"/>
    <parameters xmi:id="_RVQuiUQHEfCsYKyjnvaXEQ" elementId="message" name="Message"/>
    <parameters xmi:id="_RVQuikQHEfCsYKyjnvaXEQ" elementId="initialValue" name="Initial Value"/>
    <parameters xmi:id="_RVQui0QHEfCsYKyjnvaXEQ" elementId="cancelReturns" name="Return Value on Cancel"/>
  </commands>
  <commands xmi:id="_RVQujEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.history.ShowVersions" commandName="Open" category="_RVNEAUQHEfCsYKyjnvaXEQ">
    <parameters xmi:id="_RVQujUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.history.CompareMode" name="Compare mode"/>
  </commands>
  <commands xmi:id="_RVQujkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.ui.navigate.opentype" commandName="Open Element" description="Open an element in an Editor" category="_RVNEA0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQuj0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.views.properties.NewPropertySheetCommand" commandName="Properties" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQukEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.navigate.collapseAll" commandName="Collapse All" description="Collapse the current tree" category="_RVNEC0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQukUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.edit.text.folding.toggle" commandName="Toggle Folding" description="Toggles folding in the current editor" category="_RVND_kQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQukkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.team.submodule.update" commandName="Update Submodule" category="_RVND-0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQuk0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.debug.ui.command.StepIntoSelection" commandName="Step Into Selection" description="Step into the current selected statement" category="_RVND-UQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQulEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.ui.edit.text.c.remove.block.comment" commandName="Remove Block Comment" description="Remove the block comment enclosing the selection" category="_RVNEA0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQulUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.commit.Revert" commandName="Revert Commit" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQulkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.RepositoriesViewCreateRepository" commandName="Create a Repository" category="_RVND-0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQul0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.file.revert" commandName="Revert" description="Revert to the last saved state" category="_RVND-EQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQumEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.edit.text.scroll.lineDown" commandName="Scroll Line Down" description="Scroll down one line of text" category="_RVND_kQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQumUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.team.SimpleFetch" commandName="Fetch from Upstream" category="_RVND-0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQumkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.debug.ui.commands.StepInto" commandName="Step Into" description="Step into" category="_RVND-UQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQum0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.edit.redo" commandName="Redo" description="Redo the last operation" category="_RVNEAEQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQunEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.commit.Checkout" commandName="Checkout" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQunUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.edit.text.join.lines" commandName="Join Lines" description="Join lines of text" category="_RVND_kQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQunkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.ui.search.finddecl" commandName="Declaration" description="Search for declarations of the selected element in the workspace" category="_RVNEA0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQun0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.edit.text.scroll.lineUp" commandName="Scroll Line Up" description="Scroll up one line of text" category="_RVND_kQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQuoEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.debug.ui.commands.TerminateAndRelaunch" commandName="Terminate and Relaunch" description="Terminate and Relaunch" category="_RVND-UQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQuoUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.equinox.p2.ui.sdk.installationDetails" commandName="Installation Details" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQuokQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.file.print" commandName="Print" description="Print" category="_RVND-EQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQuo0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.navigate.forwardHistory" commandName="Forward History" description="Move forward in the editor navigation history" category="_RVNEC0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQupEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.edit.text.select.pageUp" commandName="Select Page Up" description="Select to the top of the page" category="_RVND_kQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQupUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.team.CompareWithPrevious" commandName="Compare with Previous Revision" category="_RVND-0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQupkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.ui.menu.findUnresolvedIncludes" commandName="Search for Unresolved Includes" category="_RVND9EQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQup0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.help.dynamicHelp" commandName="Dynamic Help" description="Open the dynamic help" category="_RVNEB0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQuqEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.debug.ui.commands.RunLast" commandName="Run" description="Launch in run mode" category="_RVND-UQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQuqUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.edit.text.copyLineUp" commandName="Duplicate Lines" description="Duplicates the selected lines and leaves the selection unchanged" category="_RVND_kQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQuqkQHEfCsYKyjnvaXEQ" elementId="org.omnetpp.ned.editor.text.performTextSearchWorkspace" commandName="Search Text in NED Files" description="Perform text search in workspace NED files" category="_RVND_kQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQuq0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.commit.CreateBranch" commandName="Create Branch..." category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQurEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.navigate.removeFromWorkingSet" commandName="Remove From Working Set" description="Removes the selected object from a working set." category="_RVNEAEQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQurUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.team.ReplaceWithCommit" commandName="Replace with commit" category="_RVND-0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQurkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.team.DeleteBranch" commandName="Delete Branch" category="_RVND-0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQur0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.RepositoriesToggleBranchHierarchy" commandName="Toggle Branch Representation" category="_RVND-0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQusEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.team.ApplyPatch" commandName="Apply Patch" category="_RVND-0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQusUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.edit.text.showRulerAnnotationInformation" commandName="Show Ruler Annotation Tooltip" description="Displays annotation information for the caret line in a focused hover" category="_RVND_kQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQuskQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.edit.text.folding.collapse" commandName="Collapse" description="Collapses the folded region at the current selection" category="_RVND_kQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQus0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.ui.edit.text.c.source.quickMenu" commandName="Show Source Quick Menu" description="Shows the source quick menu" category="_RVNEA0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQutEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.commit.ShowInHistory" commandName="Show in History" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQutUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.equinox.p2.ui.sdk.install" commandName="Install New Software..." category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQutkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.window.nextView" commandName="Next View" description="Switch to the next view" category="_RVND8UQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQut0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.project.buildLast" commandName="Repeat Working Set Build" description="Repeat the last working set build" category="_RVND9EQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQuuEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.edit.text.goto.textStart" commandName="Text Start" description="Go to the beginning of the text" category="_RVND_kQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQuuUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.file.properties" commandName="Properties" description="Display the properties of the selected item" category="_RVND-EQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQuukQHEfCsYKyjnvaXEQ" elementId="org.eclipse.search.ui.openSearchDialog" commandName="Open Search Dialog" description="Open the Search dialog" category="_RVND_0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQuu0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.debug.ui.actions.WatchCommand" commandName="Watch" description="Create a watch expression from the current selection and add it to the Expressions view" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQuvEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.file.openWorkspace" commandName="Switch Workspace" description="Open the workspace selection dialog" category="_RVND-EQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQuvUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.edit.text.moveLineUp" commandName="Move Lines Up" description="Moves the selected lines up" category="_RVND_kQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQuvkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.ide.configureFilters" commandName="Configure Contents..." description="Configure the filters to apply to the markers view" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQuv0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.ui.edit.open.outline" commandName="Show outline" description="Shows outline" category="_RVNEA0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQuwEQHEfCsYKyjnvaXEQ" elementId="org.omnetpp.common.openWebBrowser" commandName="Open Web Page From Plugin" description="Open Web Page From Plugin" category="_RVNEB0QHEfCsYKyjnvaXEQ">
    <parameters xmi:id="_RVQuwUQHEfCsYKyjnvaXEQ" elementId="pluginId" name="pluginId" optional="false"/>
    <parameters xmi:id="_RVQuwkQHEfCsYKyjnvaXEQ" elementId="path" name="path" optional="false"/>
  </commands>
  <commands xmi:id="_RVQuw0QHEfCsYKyjnvaXEQ" elementId="org.omnetpp.msg.editor.ToggleComment" commandName="Toggle Comment" description="Comment/Uncomment the selected lines" category="_RVND_kQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQuxEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.edit.text.copyLineDown" commandName="Copy Lines" description="Duplicates the selected lines and moves the selection to the copy" category="_RVND_kQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQuxUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.ui.edit.open.call.hierarchy" commandName="Open Call Hierarchy" description="Open the call hierarchy for the selected element" category="_RVNEC0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVQuxkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.ui.menu.updateWithModifiedFiles" commandName="Update Index with Modified Files" category="_RVND9EQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRVYEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.debug.ui.command.connect" commandName="Connect" description="Connect to a process" category="_RVND_UQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRVYUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.edit.text.goto.wordNext" commandName="Next Word" description="Go to the next word" category="_RVND_kQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRVYkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.history.CherryPick" commandName="Cherry Pick" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRVY0QHEfCsYKyjnvaXEQ" elementId="org.omnetpp.eventlogtable.gotoEvent" commandName="Go to Event..." category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRVZEQHEfCsYKyjnvaXEQ" elementId="org.omnetpp.ned.editor.text.GotoDeclaration" commandName="Go to Declaration" description="Go to declaration of NED element" category="_RVND_kQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRVZUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.ConfigureUpstreamFetch" commandName="Configure Upstream Fetch" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRVZkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.team.Merge" commandName="Merge" category="_RVND-0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRVZ0QHEfCsYKyjnvaXEQ" elementId="org.omnetpp.sequencechart.gotoEvent" commandName="Go to Event..." category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRVaEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.ui.edit.text.c.toggle.comment" commandName="Comment/Uncomment" description="Comment/Uncomment the selected lines" category="_RVNEA0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRVaUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.navigate.up" commandName="Up" description="Navigate up one level" category="_RVNEC0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRVakQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.history.ResetQuickdiffBaseline" commandName="Reset quickdiff baseline" category="_RVNEAUQHEfCsYKyjnvaXEQ">
    <parameters xmi:id="_RVRVa0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.history.ResetQuickdiffBaselineTarget" name="Reset target (HEAD, HEAD^1)" optional="false"/>
  </commands>
  <commands xmi:id="_RVRVbEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.team.submodule.add" commandName="Add Submodule" category="_RVND-0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRVbUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.make.ui.targetBuildCommand" commandName="Make Target Build" description="Invoke a make target build for the selected container." category="_RVND9EQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRVbkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.window.closePerspective" commandName="Close Perspective" description="Close the current perspective" category="_RVND8UQHEfCsYKyjnvaXEQ">
    <parameters xmi:id="_RVRVb0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.window.closePerspective.perspectiveId" name="Perspective Id"/>
  </commands>
  <commands xmi:id="_RVRVcEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.window.hideShowEditors" commandName="Toggle Editor Area Visibility" description="Toggles the visibility of the editor area" category="_RVND8UQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRVcUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.project.rebuildProject" commandName="Rebuild Project" description="Rebuild the selected projects" category="_RVND9EQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRVckQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.history.Edit" commandName="Edit Commit" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRVc0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.edit.move" commandName="Move..." description="Move the selected item" category="_RVND-EQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRVdEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.ui.edit.text.c.surround.with.quickMenu" commandName="Surround With Quick Menu" description="Shows the Surround With quick menu" category="_RVNEA0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRVdUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.edit.text.clear.mark" commandName="Clear Mark" description="Clear the mark" category="_RVND_kQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRVdkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.ui.edit.text.c.goto.prev.member" commandName="Go to Previous Member" description="Move the caret to the previous member of the translation unit" category="_RVNEA0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRVd0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.team.ReplaceWithHead" commandName="Replace with HEAD revision" category="_RVND-0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRVeEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.debug.ui.commands.OpenProfileConfigurations" commandName="Profile..." description="Open profile launch configuration dialog" category="_RVND-UQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRVeUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.cheatsheets.openCheatSheet" commandName="Open Cheat Sheet" description="Open a Cheat Sheet." category="_RVNEB0QHEfCsYKyjnvaXEQ">
    <parameters xmi:id="_RVRVekQHEfCsYKyjnvaXEQ" elementId="cheatSheetId" name="Identifier"/>
  </commands>
  <commands xmi:id="_RVRVe0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.RepositoriesViewOpen" commandName="Open" category="_RVND-0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRVfEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.RepositoriesToggleBranchCommit" commandName="Toggle Latest Branch Commit" category="_RVND-0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRVfUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.debug.ui.command.castToArray" commandName="Cast To Type..." category="_RVND-kQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRVfkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.history.CompareVersions" commandName="Compare with each other" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRVf0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.edit.text.contentAssist.contextInformation" commandName="Context Information" description="Show Context Information" category="_RVNEAEQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRVgEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.browser.openBrowser" commandName="Open Browser" description="Opens the default web browser." category="_RVND8UQHEfCsYKyjnvaXEQ">
    <parameters xmi:id="_RVRVgUQHEfCsYKyjnvaXEQ" elementId="url" name="URL"/>
    <parameters xmi:id="_RVRVgkQHEfCsYKyjnvaXEQ" elementId="browserId" name="Browser Id"/>
    <parameters xmi:id="_RVRVg0QHEfCsYKyjnvaXEQ" elementId="name" name="Browser Name"/>
    <parameters xmi:id="_RVRVhEQHEfCsYKyjnvaXEQ" elementId="tooltip" name="Browser Tooltip"/>
  </commands>
  <commands xmi:id="_RVRVhUQHEfCsYKyjnvaXEQ" elementId="org.omnetpp.eventlogtable.gotoNextEvent" commandName="Go to Next Event" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRVhkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.edit.text.select.columnNext" commandName="Select Next Column" description="Select the next column" category="_RVND_kQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRVh0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.ui.edit.text.c.select.last" commandName="Restore Last C/C++ Selection" description="Restore last selection in C/C++ editor" category="_RVNEAEQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRViEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.edit.text.upperCase" commandName="To Upper Case" description="Changes the selection to upper case" category="_RVND_kQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRViUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.debug.ui.commands.DropToFrame" commandName="Drop to Frame" description="Drop to Frame" category="_RVND-UQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRVikQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.ui.edit.text.c.format" commandName="Format" description="Format Source Code" category="_RVNEA0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRVi0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.RepositoriesViewNewRemote" commandName="Create Remote..." category="_RVND-0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRVjEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.search.ui.openFileSearchPage" commandName="File Search" description="Open the Search dialog's file search page" category="_RVND_0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRVjUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.debug.ui.command.resumeWithoutSignal" commandName="Resume Without Signal" description="Resume Without Signal" category="_RVNEBEQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRVjkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.team.ui.synchronizeLast" commandName="Repeat last synchronization" description="Repeat the last synchronization" category="_RVNEBkQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRVj0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.history.CreatePatch" commandName="Create Patch" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRVkEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.team.clean" commandName="Clean..." category="_RVND-0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRVkUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.edit.text.goto.columnNext" commandName="Next Column" description="Go to the next column" category="_RVND_kQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRVkkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.navigate.selectWorkingSets" commandName="Select Working Sets" description="Select the working sets that are applicable for this window." category="_RVND8UQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRVk0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.help.aboutAction" commandName="About" description="Open the about dialog" category="_RVNEB0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRVlEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.compare.copyRightToLeft" commandName="Copy from Right to Left" description="Copy Current Change from Right to Left" category="_RVNECEQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRVlUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.ui.edit.text.c.uncomment" commandName="Uncomment" description="Uncomment the selected // style comment lines" category="_RVNEA0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRVlkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.command.configureTrace" commandName="Configure Git Debug Trace" category="_RVND-0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRVl0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.compare.selectNextChange" commandName="Select Next Change" description="Select Next Change" category="_RVNECEQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRVmEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.commands.rulerToggleBreakpoint" commandName="Toggle Breakpoint" description="Toggle breakpoint in disassembly ruler" category="_RVND-UQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRVmUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.ui.search.finddecl.workingset" commandName="Declaration in Working Set" description="Search for declarations of the selected element in a working set" category="_RVNEA0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRVmkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.debug.ui.commands.RemoveAllBreakpoints" commandName="Remove All Breakpoints" description="Removes all breakpoints" category="_RVND-UQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRVm0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.window.newWindow" commandName="New Window" description="Open another window" category="_RVND8UQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRVnEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.navigate.showResourceByPath" commandName="Show Resource in Navigator" description="Show a resource in the Navigator given its path" category="_RVNEC0QHEfCsYKyjnvaXEQ">
    <parameters xmi:id="_RVRVnUQHEfCsYKyjnvaXEQ" elementId="resourcePath" name="Resource Path" typeId="org.eclipse.ui.ide.resourcePath" optional="false"/>
  </commands>
  <commands xmi:id="_RVRVnkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.ui.refactor.implement.method" commandName="Implement Method - Source Generation " description="Implements a method for a selected method declaration" category="_RVNEA0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRVn0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.make.ui.targetCreateCommand" commandName="Create Make Target" description="Create a new make build target for the selected container." category="_RVND9EQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRVoEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.dsf.gdb.ui.command.selectPreviousTraceRecord" commandName="Previous Trace Record" description="Select Previous Trace Record" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRVoUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.edit.text.shiftLeft" commandName="Shift Left" description="Shift a block of text to the left" category="_RVNEAEQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRVokQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.project.cleanAction" commandName="Build Clean" description="Discard old built state" category="_RVND9EQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRVo0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.activeContextInfo" commandName="Show activeContext Info" category="_RVND8UQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRVpEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.edit.findNext" commandName="Find Next" description="Find next item" category="_RVNEAEQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRVpUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.debug.ui.commands.Disconnect" commandName="Disconnect" description="Disconnect" category="_RVND-UQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRVpkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.commit.CreateTag" commandName="Create Tag..." category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRVp0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.make.ui.edit.text.makefile.comment" commandName="Comment" description="Turn the selected lines into # style comments" category="_RVND90QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRVqEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.ui.edit.text.c.select.enclosing" commandName="Select Enclosing C/C++ Element" description="Expand the selection to enclosing C/C++ element" category="_RVNEAEQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRVqUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.edit.text.select.windowStart" commandName="Select Window Start" description="Select to the start of the window" category="_RVND_kQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRVqkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.ui.edit.text.c.toggle.source.header" commandName="Toggle Source/Header" description="Toggles between corresponding source and header files" category="_RVNEA0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRVq0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.internal.reflog.CheckoutCommand" commandName="Checkout" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRVrEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.help.quickStartAction" commandName="Welcome" description="Show help for beginning users" category="_RVNEB0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRVrUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.edit.addTask" commandName="Add Task..." description="Add a task" category="_RVNEAEQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRVrkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.window.closeAllPerspectives" commandName="Close All Perspectives" description="Close all open perspectives" category="_RVND8UQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRVr0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.editors.quickdiff.revertLine" commandName="Revert Line" description="Revert the current line" category="_RVND_kQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRVsEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.debug.ui.command.prevpage" commandName="Previous Page of Memory" description="Load previous page of memory" category="_RVND-UQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRVsUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.edit.findPrevious" commandName="Find Previous" description="Find previous item" category="_RVNEAEQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRVskQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.views.showView" commandName="Show View" description="Shows a particular view" category="_RVND9UQHEfCsYKyjnvaXEQ">
    <parameters xmi:id="_RVRVs0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.views.showView.viewId" name="View"/>
    <parameters xmi:id="_RVRVtEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.views.showView.secondaryId" name="Secondary Id"/>
    <parameters xmi:id="_RVRVtUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.views.showView.makeFast" name="As FastView"/>
  </commands>
  <commands xmi:id="_RVRVtkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.debug.ui.commands.viewMemory" commandName="View Memory" description="View variable in memory view" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRVt0QHEfCsYKyjnvaXEQ" elementId="org.omnetpp.eventlogtable.gotoPreviousEvent" commandName="Go to Previous Event" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRVuEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.edit.text.delimiter.windows" commandName="Convert Line Delimiters to Windows (CRLF, \r\n, 0D0A, &#xa4;&#xb6;)" description="Converts the line delimiters to Windows (CRLF, \r\n, 0D0A, &#xa4;&#xb6;)" category="_RVND-EQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRVuUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.edit.text.cut.line" commandName="Cut Line" description="Cut a line of text" category="_RVND_kQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRVukQHEfCsYKyjnvaXEQ" elementId="org.omnetpp.eventlogtable.findNext" commandName="Find Next" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRVu0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.edit.text.select.columnPrevious" commandName="Select Previous Column" description="Select the previous column" category="_RVND_kQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRVvEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.file.closeAll" commandName="Close All" description="Close all editors" category="_RVND-EQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRVvUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.edit.text.cut.line.to.beginning" commandName="Cut to Beginning of Line" description="Cut to the beginning of a line of text" category="_RVND_kQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRVvkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.codan.commands.runCodanCommand" commandName="Run Code Analysis" category="_RVND8kQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRVv0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.ui.edit.text.rename.element" commandName="Rename - Refactoring " description="Rename the selected element" category="_RVND80QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRVwEQHEfCsYKyjnvaXEQ" elementId="org.omnetpp.eventlogtable.gotoSimulationTime" commandName="Go to Simulation Time..." category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRVwUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.edit.text.select.wordNext" commandName="Select Next Word" description="Select the next word" category="_RVND_kQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRVwkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.navigate.goToResource" commandName="Go to" description="Go to a particular resource in the active view" category="_RVNEC0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRVw0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.window.spy" commandName="Show Contributing Plug-in" description="Shows contribution information for the currently selected element" category="_RVND8UQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRVxEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.team.submodule.sync" commandName="Sync Submodule" category="_RVND-0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRVxUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.window.quickAccess" commandName="Quick Access" description="Quickly access UI elements" category="_RVND8UQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRVxkQHEfCsYKyjnvaXEQ" elementId="org.omnetpp.sequencechart.refresh" commandName="Refresh" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRVx0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.dsf.debug.ui.refreshAll" commandName="Refresh Debug Views" description="Refresh all data in debug views" category="_RVND-UQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRVyEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.team.CompareIndexWithHead" commandName="Compare File in Git Index with HEAD Revision" category="_RVND-0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRVyUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.debug.ui.commands.StepReturn" commandName="Step Return" description="Step return" category="_RVND-UQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRVykQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.navigate.addToWorkingSet" commandName="Add to Working Set" description="Adds the selected object to a working set." category="_RVNEAEQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRVy0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.edit.text.goto.lineEnd" commandName="Line End" description="Go to the end of the line of text" category="_RVND_kQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRVzEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.team.CreatePatch" commandName="Create Patch" category="_RVND-0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRVzUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.RepositoriesViewImportProjects" commandName="Import Projects..." description="Import or create in local Git repository" category="_RVND-0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRVzkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.navigate.showIn" commandName="Show In" category="_RVNEC0QHEfCsYKyjnvaXEQ">
    <parameters xmi:id="_RVRVz0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.navigate.showIn.targetId" name="Show In Target Id" optional="false"/>
  </commands>
  <commands xmi:id="_RVRV0EQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.edit.text.delimiter.unix" commandName="Convert Line Delimiters to Unix (LF, \n, 0A, &#xb6;)" description="Converts the line delimiters to Unix (LF, \n, 0A, &#xb6;)" category="_RVND-EQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRV0UQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.dialogs.openMessageDialog" commandName="Open Message Dialog" description="Open a Message Dialog" category="_RVND_EQHEfCsYKyjnvaXEQ">
    <parameters xmi:id="_RVRV0kQHEfCsYKyjnvaXEQ" elementId="title" name="Title"/>
    <parameters xmi:id="_RVRV00QHEfCsYKyjnvaXEQ" elementId="message" name="Message"/>
    <parameters xmi:id="_RVRV1EQHEfCsYKyjnvaXEQ" elementId="imageType" name="Image Type Constant" typeId="org.eclipse.ui.dialogs.Integer"/>
    <parameters xmi:id="_RVRV1UQHEfCsYKyjnvaXEQ" elementId="defaultIndex" name="Default Button Index" typeId="org.eclipse.ui.dialogs.Integer"/>
    <parameters xmi:id="_RVRV1kQHEfCsYKyjnvaXEQ" elementId="buttonLabel0" name="First Button Label"/>
    <parameters xmi:id="_RVRV10QHEfCsYKyjnvaXEQ" elementId="buttonLabel1" name="Second Button Label"/>
    <parameters xmi:id="_RVRV2EQHEfCsYKyjnvaXEQ" elementId="buttonLabel2" name="Third Button Label"/>
    <parameters xmi:id="_RVRV2UQHEfCsYKyjnvaXEQ" elementId="buttonLabel3" name="Fourth Button Label"/>
    <parameters xmi:id="_RVRV2kQHEfCsYKyjnvaXEQ" elementId="cancelReturns" name="Return Value on Cancel"/>
  </commands>
  <commands xmi:id="_RVRV20QHEfCsYKyjnvaXEQ" elementId="org.omnetpp.eventlogtable.gotoMessageArrival" commandName="Go to Message Arrival" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRV3EQHEfCsYKyjnvaXEQ" elementId="org.eclipse.equinox.p2.ui.discovery.commands.ShowRepositoryCatalog" commandName="Show Repository Catalog" category="_RVNEAUQHEfCsYKyjnvaXEQ">
    <parameters xmi:id="_RVRV3UQHEfCsYKyjnvaXEQ" elementId="org.eclipse.equinox.p2.ui.discovery.commands.RepositoryParameter" name="P2 Repository URI"/>
  </commands>
  <commands xmi:id="_RVRV3kQHEfCsYKyjnvaXEQ" elementId="org.eclipse.debug.ui.commands.OpenDebugConfigurations" commandName="Debug..." description="Open debug launch configuration dialog" category="_RVND-UQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRV30QHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.window.nextPerspective" commandName="Next Perspective" description="Switch to the next perspective" category="_RVND8UQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRV4EQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.edit.text.hippieCompletion" commandName="Word Completion" description="Context insensitive completion" category="_RVNEAEQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRV4UQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.RepositoriesViewClearCredentials" commandName="Clear Credentials" category="_RVND-0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRV4kQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.ui.edit.open.include.browser" commandName="Open Include Browser" description="Open an include browser on the selected element" category="_RVNEC0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRV40QHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.make.ui.edit.text.makefile.opendecl" commandName="Open declaration" description="Follow to the directive definition" category="_RVND90QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRV5EQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.file.closeOthers" commandName="Close Others" description="Close all editors except the one that is active" category="_RVND-EQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRV5UQHEfCsYKyjnvaXEQ" elementId="org.omnetpp.neddoc.commands.neddocCommand" commandName="Generate NED Documentation..." category="_RVND9EQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRV5kQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.editors.revisions.author.toggle" commandName="Toggle Revision Author Display" description="Toggles the display of the revision author" category="_RVND_kQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRV50QHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.history.Reword" commandName="Reword Commit" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRV6EQHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.RepositoriesViewConfigureFetch" commandName="Configure Fetch..." category="_RVND-0QHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRV6UQHEfCsYKyjnvaXEQ" elementId="org.eclipse.debug.ui.commands.Resume" commandName="Resume" description="Resume" category="_RVND-UQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RVRV6kQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.ui.menu.createParserLog" commandName="Create Parser Log File" category="_RVND9EQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RcZ4IEQHEfCsYKyjnvaXEQ" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.debugActionSet/org.eclipse.cdt.debug.ui.actions.ResumeAtLine" commandName="Resume At Line (C/C++)" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RcafMEQHEfCsYKyjnvaXEQ" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.debugActionSet/org.eclipse.cdt.debug.ui.actions.MoveToLine" commandName="Move to Line (C/C++)" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RcafMUQHEfCsYKyjnvaXEQ" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.debugActionSet/org.eclipse.cdt.debug.ui.actions.ToggleInstructionStepMode" commandName="Instruction Stepping Mode" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RcafMkQHEfCsYKyjnvaXEQ" elementId="AUTOGEN:::org.eclipse.cdt.make.ui.updateActionSet/org.eclipse.cdt.make.ui.UpdateMakeAction" commandName="Update Old Make Project..." description="Update Old Make Project" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RcbtUEQHEfCsYKyjnvaXEQ" elementId="AUTOGEN:::org.eclipse.cdt.make.ui.makeTargetActionSet/org.eclipse.cdt.make.ui.actions.buildLastTargetAction" commandName="Rebuild Last Target" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RcbtUUQHEfCsYKyjnvaXEQ" elementId="AUTOGEN:::org.eclipse.cdt.make.ui.makeTargetActionSet/org.eclipse.cdt.make.ui.makeTargetAction" commandName="Build..." category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RcbtUkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ltk.ui.refactor.show.refactoring.history" commandName="History..." category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RcbtU0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.ltk.ui.refactor.create.refactoring.script" commandName="Create Script..." category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RcbtVEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ltk.ui.refactor.apply.refactoring.script" commandName="Apply Script..." category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RcbtVUQHEfCsYKyjnvaXEQ" elementId="AUTOGEN:::org.eclipse.cdt.ui.SearchActionSet/org.eclipse.cdt.ui.actions.OpenCSearchPage" commandName="C/C++..." category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RcbtVkQHEfCsYKyjnvaXEQ" elementId="AUTOGEN:::org.eclipse.cdt.ui.buildConfigActionSet/org.eclipse.cdt.ui.buildActiveConfigToolbarAction" commandName="Build Active Configuration" description="Build the active configurations of selected projects" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RcbtV0QHEfCsYKyjnvaXEQ" elementId="AUTOGEN:::org.eclipse.cdt.ui.buildConfigActionSet/org.eclipse.cdt.ui.buildConfigToolbarAction" commandName="Active Build Configuration" description="Manage configurations for the current project" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RcbtWEQHEfCsYKyjnvaXEQ" elementId="AUTOGEN:::org.eclipse.cdt.ui.buildConfigActionSet/org.eclipse.cdt.ui.manageConfigsAction2" commandName="Manage..." category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RccUYEQHEfCsYKyjnvaXEQ" elementId="AUTOGEN:::org.eclipse.cdt.ui.buildConfigActionSet/org.eclipse.cdt.ui.buildConfigMenuAction" commandName="Set Active" description="Change active build configuration for project(s)" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RccUYUQHEfCsYKyjnvaXEQ" elementId="AUTOGEN:::org.eclipse.cdt.ui.buildConfigActionSet/org.eclipse.cdt.ui.wsselection" commandName="Manage Working Sets..." category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RccUYkQHEfCsYKyjnvaXEQ" elementId="AUTOGEN:::org.eclipse.cdt.ui.CElementCreationActionSet/org.eclipse.cdt.ui.actions.NewTypeDropDown" commandName="Class..." description="New C++ Class" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RccUY0QHEfCsYKyjnvaXEQ" elementId="AUTOGEN:::org.eclipse.cdt.ui.CElementCreationActionSet/org.eclipse.cdt.ui.actions.NewFileDropDown" commandName="Source File..." description="New C/C++ Source File" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RccUZEQHEfCsYKyjnvaXEQ" elementId="AUTOGEN:::org.eclipse.cdt.ui.CElementCreationActionSet/org.eclipse.cdt.ui.actions.NewFolderDropDown" commandName="Source Folder..." description="New C/C++ Source Folder" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RccUZUQHEfCsYKyjnvaXEQ" elementId="AUTOGEN:::org.eclipse.cdt.ui.CElementCreationActionSet/org.eclipse.cdt.ui.actions.NewProjectDropDown" commandName="Project..." description="New C/C++ Project" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_Rcc7cEQHEfCsYKyjnvaXEQ" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunWithConfigurationAction" commandName="Run As" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_Rcc7cUQHEfCsYKyjnvaXEQ" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunHistoryMenuAction" commandName="Run History" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_Rcc7ckQHEfCsYKyjnvaXEQ" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunDropDownAction" commandName="Run" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_Rcc7c0QHEfCsYKyjnvaXEQ" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugWithConfigurationAction" commandName="Debug As" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_Rcc7dEQHEfCsYKyjnvaXEQ" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugHistoryMenuAction" commandName="Debug History" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_Rcc7dUQHEfCsYKyjnvaXEQ" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugDropDownAction" commandName="Debug" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_Rcc7dkQHEfCsYKyjnvaXEQ" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileDropDownAction" commandName="Profile" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_Rcc7d0QHEfCsYKyjnvaXEQ" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileWithConfigurationAction" commandName="Profile As" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_Rcc7eEQHEfCsYKyjnvaXEQ" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileHistoryMenuAction" commandName="Profile History" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RcdigEQHEfCsYKyjnvaXEQ" elementId="AUTOGEN:::org.eclipse.ui.cheatsheets.actionSet/org.eclipse.ui.cheatsheets.actions.CheatSheetHelpMenuAction" commandName="Cheat Sheets..." category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RcdigUQHEfCsYKyjnvaXEQ" elementId="AUTOGEN:::org.eclipse.search.searchActionSet/org.eclipse.search.OpenSearchDialogPage" commandName="Search..." description="Search" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RcdigkQHEfCsYKyjnvaXEQ" elementId="AUTOGEN:::org.eclipse.team.ui.actionSet/org.eclipse.team.ui.synchronizeAll" commandName="Synchronize..." description="Synchronize..." category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_Rcdig0QHEfCsYKyjnvaXEQ" elementId="AUTOGEN:::org.eclipse.team.ui.actionSet/org.eclipse.team.ui.ConfigureProject" commandName="Share Project..." description="Share the project with others using a version and configuration management system." category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RcdihEQHEfCsYKyjnvaXEQ" elementId="AUTOGEN:::org.eclipse.ui.externaltools.ExternalToolsSet/org.eclipse.ui.externaltools.ExternalToolMenuDelegateMenu" commandName="External Tools" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RcdihUQHEfCsYKyjnvaXEQ" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.CEditor.BreakpointRulerActions/org.eclipse.cdt.debug.ui.CEditor.RulerTobbleBreakpointAction" commandName="%Dummy.label" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RceJkEQHEfCsYKyjnvaXEQ" elementId="AUTOGEN:::org.eclipse.cdt.ui.editor.asm.AsmEditor.BreakpointRulerActions/org.eclipse.cdt.debug.ui.CEditor.RulerTobbleBreakpointAction" commandName="%Dummy.label" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RceJkUQHEfCsYKyjnvaXEQ" elementId="AUTOGEN:::org.eclipse.ui.texteditor.ruler.actions/org.eclipse.ui.texteditor.BookmarkRulerAction" commandName="dummy" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RceJkkQHEfCsYKyjnvaXEQ" elementId="AUTOGEN:::org.eclipse.ui.texteditor.ruler.actions/org.eclipse.cdt.internal.ui.text.correction.CSelectRulerAction" commandName="dummy" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RceJk0QHEfCsYKyjnvaXEQ" elementId="AUTOGEN:::org.eclipse.ui.texteditor.ruler.actions/org.eclipse.ui.texteditor.SelectRulerAction" commandName="Text Editor Ruler Single-Click" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RceJlEQHEfCsYKyjnvaXEQ" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.debugview.toolbar/org.eclipse.cdt.debug.internal.ui.actions.ToggleInstructionStepModeActionDelegate" commandName="Instruction Stepping Mode" description="Instruction Stepping Mode" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RceJlUQHEfCsYKyjnvaXEQ" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.debugView.menu/org.eclipse.cdt.debug.internal.ui.actions.ShowFullPathsAction" commandName="Show Full Paths" description="Show Full Paths" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RceJlkQHEfCsYKyjnvaXEQ" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.breakpointView.menu/org.eclipse.cdt.debug.internal.ui.actions.ShowFullPathsAction" commandName="Show Full Paths" description="Show Full Paths" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RceJl0QHEfCsYKyjnvaXEQ" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.breakpointView.menu/org.eclipse.cdt.debug.ui.addWatchpoint" commandName="Add Watchpoint (C/C++)..." description="Add Watchpoint (C/C++)" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RceJmEQHEfCsYKyjnvaXEQ" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.breakpointView.menu/org.eclipse.cdt.debug.internal.ui.actions.AddEventBreakpointActionDelegate" commandName="Add Event Breakpoint (C/C++)..." description="Add Event Breakpoint (C/C++)" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RceJmUQHEfCsYKyjnvaXEQ" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.breakpointView.menu/org.eclipse.cdt.debug.ui.addFunctionBreakpoint" commandName="Add Function Breakpoint (C/C++)..." description="Add Function Breakpoint (C/C++)" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RceJmkQHEfCsYKyjnvaXEQ" elementId="AUTOGEN:::org.eclipse.debug.ui.variablesView.toolbar/org.eclipse.cdt.debug.internal.ui.actions.RemoveAllGlobalsActionDelegate" commandName="Remove All Global Variables" description="Remove All Global Variables" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RceJm0QHEfCsYKyjnvaXEQ" elementId="AUTOGEN:::org.eclipse.debug.ui.variablesView.toolbar/org.eclipse.cdt.debug.internal.ui.actions.RemoveGlobalsActionDelegate" commandName="Remove Global Variables" description="Remove Selected Global Variables" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RcewoEQHEfCsYKyjnvaXEQ" elementId="AUTOGEN:::org.eclipse.debug.ui.variablesView.toolbar/org.eclipse.cdt.debug.internal.ui.actions.AddGlobalsActionDelegate" commandName="Add Global Variables..." description="Add Global Variables" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RcewoUQHEfCsYKyjnvaXEQ" elementId="AUTOGEN:::org.eclipse.debug.ui.modulesView.toolbar/org.eclipse.cdt.debug.ui.LoadSymbolsForAllAction" commandName="Load Symbols For All" description="Load Symbols For All Modules" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RcewokQHEfCsYKyjnvaXEQ" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.expression.toolbar/org.eclipse.pinclone.expression.pinDebugContext" commandName="Pin to Debug Context" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_Rcewo0QHEfCsYKyjnvaXEQ" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.expression.toolbar/org.eclipse.pinclone.expression.clone" commandName="Open New View" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RcewpEQHEfCsYKyjnvaXEQ" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.variable.toolbar/org.eclipse.pinclone.variable.pinDebugContext" commandName="Pin to Debug Context" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RcewpUQHEfCsYKyjnvaXEQ" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.variable.toolbar/org.eclipse.pinclone.variable.clone" commandName="Open New View" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RcewpkQHEfCsYKyjnvaXEQ" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.register.toolbar/org.eclipse.pinclone.register.pinDebugContext" commandName="Pin to Debug Context" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_Rcewp0QHEfCsYKyjnvaXEQ" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.register.toolbar/org.eclipse.pinclone.register.clone" commandName="Open New View" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RcewqEQHEfCsYKyjnvaXEQ" elementId="AUTOGEN:::org.eclipse.cdt.dsf.debug.ui.viewmodel.variables.update.Refresh/org.eclipse.cdt.dsf.debug.ui.variables.viewmodel.update.actions.refresh" commandName="Refresh" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RcewqUQHEfCsYKyjnvaXEQ" elementId="AUTOGEN:::org.eclipse.cdt.dsf.debug.ui.viewmodel.registers.update.Refresh/org.eclipse.cdt.dsf.debug.ui.registers.viewmodel.update.actions.refresh" commandName="Refresh" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RcewqkQHEfCsYKyjnvaXEQ" elementId="AUTOGEN:::org.eclipse.cdt.dsf.debug.ui.viewmodel.expressions.update.Refresh/org.eclipse.cdt.dsf.debug.ui.expressions.viewmodel.update.actions.refresh" commandName="Refresh" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_Rcewq0QHEfCsYKyjnvaXEQ" elementId="AUTOGEN:::org.eclipse.cdt.dsf.debug.ui.viewmodel.debugview.update.Refresh/org.eclipse.cdt.dsf.debug.ui.debugview.viewmodel.update.actions.refresh" commandName="Refresh" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RcewrEQHEfCsYKyjnvaXEQ" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.disassembly.toolbar/org.eclipse.pinclone.disassembly.pinDebugContext" commandName="Pin to Debug Context" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RcewrUQHEfCsYKyjnvaXEQ" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.disassembly.toolbar/org.eclipse.pinclone.disassembly.clone" commandName="Open New View" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RcewrkQHEfCsYKyjnvaXEQ" elementId="AUTOGEN:::org.eclipse.debug.ui.PulldownActions/org.eclipse.debug.ui.debugview.pulldown.ViewManagementAction" commandName="View Management..." category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RcfXsEQHEfCsYKyjnvaXEQ" elementId="AUTOGEN:::org.eclipse.debug.ui.debugview.toolbar/org.eclipse.debug.ui.debugview.toolbar.removeAllTerminated" commandName="Remove All Terminated" description="Remove All Terminated Launches" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RcfXsUQHEfCsYKyjnvaXEQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.removeAll" commandName="Remove All" description="Remove All Breakpoints" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RcfXskQHEfCsYKyjnvaXEQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.linkWithDebugView" commandName="Link with Debug View" description="Link with Debug View" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RcfXs0QHEfCsYKyjnvaXEQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.workingSets" commandName="Working Sets..." description="Manage Working Sets" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RcfXtEQHEfCsYKyjnvaXEQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.clearDefaultBreakpointGroup" commandName="Deselect Default Working Set" description="Deselect Default Working Set" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RcfXtUQHEfCsYKyjnvaXEQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.setDefaultBreakpointGroup" commandName="Select Default Working Set..." description="Select Default Working Set" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RcfXtkQHEfCsYKyjnvaXEQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.groupByAction" commandName="Group By" description="Show" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RcfXt0QHEfCsYKyjnvaXEQ" elementId="AUTOGEN:::org.eclipse.debug.ui.expressionsView.toolbar/org.eclipse.debug.ui.expresssionsView.toolbar.removeAll" commandName="Remove All" description="Remove All Expressions" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RcfXuEQHEfCsYKyjnvaXEQ" elementId="AUTOGEN:::org.eclipse.debug.ui.expressionsView.toolbar/org.eclipse.debug.ui.expresssionsView.toolbar.AddWatchExpression" commandName="Add Watch Expression..." description="Create a new watch expression" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RcfXuUQHEfCsYKyjnvaXEQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.PinMemoryBlockAction" commandName="Pin Memory Monitor" description="Pin Memory Monitor" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RcfXukQHEfCsYKyjnvaXEQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.NewMemoryViewAction" commandName="New Memory View" description="New Memory View" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RcfXu0QHEfCsYKyjnvaXEQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.togglemonitors" commandName="Toggle Memory Monitors Pane" description="Toggle Memory Monitors Pane" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_RcfXvEQHEfCsYKyjnvaXEQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.linkrenderingpanes" commandName="Link Memory Rendering Panes" description="Link Memory Rendering Panes" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_Rcf-wEQHEfCsYKyjnvaXEQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.tablerendering.preferencesaction" commandName="Table Renderings Preferences..." description="&amp;Table Renderings Preferences..." category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_Rcf-wUQHEfCsYKyjnvaXEQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.togglesplitpane" commandName="Toggle Split Pane" description="Toggle Split Pane" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_Rcf-wkQHEfCsYKyjnvaXEQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.switchMemoryBlock" commandName="Switch Memory Monitor" description="Switch Memory Monitor" category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <commands xmi:id="_Rcf-w0QHEfCsYKyjnvaXEQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.memoryViewPreferencesAction" commandName="Preferences..." description="&amp;Preferences..." category="_RVNEAUQHEfCsYKyjnvaXEQ"/>
  <addons xmi:id="_RU9MXEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.e4.core.commands.service" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.core.commands/org.eclipse.e4.core.commands.CommandServiceAddon"/>
  <addons xmi:id="_RU9MXUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.e4.ui.contexts.service" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.services/org.eclipse.e4.ui.services.ContextServiceAddon"/>
  <addons xmi:id="_RU9MXkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.e4.ui.bindings.service" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.bindings/org.eclipse.e4.ui.bindings.BindingServiceAddon"/>
  <addons xmi:id="_RU9MX0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.e4.ui.workbench.commands.model" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.CommandProcessingAddon"/>
  <addons xmi:id="_RU9MYEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.e4.ui.workbench.contexts.model" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.ContextProcessingAddon"/>
  <addons xmi:id="_RU9MYUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.e4.ui.workbench.bindings.model" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.swt/org.eclipse.e4.ui.workbench.swt.util.BindingProcessingAddon"/>
  <addons xmi:id="_RU9zYEQHEfCsYKyjnvaXEQ" elementId="Cleanup Addon" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.cleanupaddon.CleanupAddon"/>
  <addons xmi:id="_RU9zYUQHEfCsYKyjnvaXEQ" elementId="DnD Addon" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.dndaddon.DnDAddon"/>
  <addons xmi:id="_RU9zYkQHEfCsYKyjnvaXEQ" elementId="MinMax Addon" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.MinMaxAddon"/>
  <addons xmi:id="_RU9zY0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.workbench.addon.0" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.HandlerProcessingAddon"/>
  <addons xmi:id="_RVmFgEQHEfCsYKyjnvaXEQ" elementId="SplitterAddon" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.splitteraddon.SplitterAddon"/>
  <categories xmi:id="_RVND8EQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.category.perspectives" name="Perspectives" description="Commands for opening perspectives"/>
  <categories xmi:id="_RVND8UQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.category.window" name="Window"/>
  <categories xmi:id="_RVND8kQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.codan.ui.commands.category" name="Code Analysis"/>
  <categories xmi:id="_RVND80QHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.ui.category.refactoring" name="Refactor - C++" description="C/C++ Refactorings"/>
  <categories xmi:id="_RVND9EQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.category.project" name="Project"/>
  <categories xmi:id="_RVND9UQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.category.views" name="Views" description="Commands for opening views"/>
  <categories xmi:id="_RVND9kQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.ide.markerContents" name="Contents" description="The category for menu contents"/>
  <categories xmi:id="_RVND90QHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.make.ui.category.source" name="Makefile Source" description="Makefile Source Actions"/>
  <categories xmi:id="_RVND-EQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.category.file" name="File"/>
  <categories xmi:id="_RVND-UQHEfCsYKyjnvaXEQ" elementId="org.eclipse.debug.ui.category.run" name="Run/Debug" description="Run/Debug command category"/>
  <categories xmi:id="_RVND-kQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.debug.ui.category.casting" name="Cast to Type or Array" description="Set of commands for displaying variables and expressions as other types or arrays."/>
  <categories xmi:id="_RVND-0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.egit.ui.commandCategory" name="Git"/>
  <categories xmi:id="_RVND_EQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.category.dialogs" name="Dialogs" description="Commands for opening dialogs"/>
  <categories xmi:id="_RVND_UQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.debug.ui.category.debugViewLayout" name="Debug View Layout Commands" description="Set of commands for controlling the Debug View Layout"/>
  <categories xmi:id="_RVND_kQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.category.textEditor" name="Text Editing" description="Text Editing Commands"/>
  <categories xmi:id="_RVND_0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.search.ui.category.search" name="Search" description="Search command category"/>
  <categories xmi:id="_RVNEAEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.category.edit" name="Edit"/>
  <categories xmi:id="_RVNEAUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.core.commands.categories.autogenerated" name="Uncategorized" description="Commands that were either auto-generated or have no category"/>
  <categories xmi:id="_RVNEAkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.debug.ui.category.reverseDebugging" name="Reverse Debugging Commands" description="Set of commands for Reverse Debugging"/>
  <categories xmi:id="_RVNEA0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.ui.category.source" name="C/C++ Source" description="C/C++ Source Actions"/>
  <categories xmi:id="_RVNEBEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.debug.ui.category.runControl" name="Run Control Commands" description="Set of commands for Run Control"/>
  <categories xmi:id="_RVNEBUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.gef.category.view" name="View" description="View"/>
  <categories xmi:id="_RVNEBkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.team.ui.category.team" name="Team" description="Actions that apply when working with a Team"/>
  <categories xmi:id="_RVNEB0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.category.help" name="Help"/>
  <categories xmi:id="_RVNECEQHEfCsYKyjnvaXEQ" elementId="org.eclipse.compare.ui.category.compare" name="Compare" description="Compare command category"/>
  <categories xmi:id="_RVNECUQHEfCsYKyjnvaXEQ" elementId="org.eclipse.cdt.debug.ui.category.tracing" name="Tracing Commands" description="Category for Tracing Commands"/>
  <categories xmi:id="_RVNECkQHEfCsYKyjnvaXEQ" elementId="org.eclipse.ltk.ui.category.refactoring" name="Refactoring"/>
  <categories xmi:id="_RVNEC0QHEfCsYKyjnvaXEQ" elementId="org.eclipse.ui.category.navigate" name="Navigate"/>
</application:Application>
