//
// Generated file, do not edit! Created by nedtool 4.6 from src/node/communication/mac/tunableMac/TunableMacPacket.msg.
//

#ifndef _TUNABLEMACPACKET_M_H_
#define _TUNABLEMACPACKET_M_H_

#include <omnetpp.h>

// nedtool version check
#define MSGC_VERSION 0x0406
#if (MSGC_VERSION!=OMNETPP_VERSION)
#    error Version mismatch! Probably this file was generated by an earlier version of nedtool: 'make clean' should help.
#endif



// cplusplus {{
#include "MacPacket_m.h"
// }}

/**
 * Enum generated from <tt>src/node/communication/mac/tunableMac/TunableMacPacket.msg:19</tt> by nedtool.
 * <pre>
 * enum TunableMacFrameTypeDef
 * {
 * 
 *     DATA_FRAME = 1;
 *     BEACON_FRAME = 2;
 * }
 * </pre>
 */
enum TunableMacFrameTypeDef {
    DATA_FRAME = 1,
    BEACON_FRAME = 2
};

/**
 * Class generated from <tt>src/node/communication/mac/tunableMac/TunableMacPacket.msg:24</tt> by nedtool.
 * <pre>
 * packet TunableMacPacket extends MacPacket
 * {
 *     int frameType @enum(TunableMacFrameTypeDef);
 * }
 * </pre>
 */
class TunableMacPacket : public ::MacPacket
{
  protected:
    int frameType_var;

  private:
    void copy(const TunableMacPacket& other);

  protected:
    // protected and unimplemented operator==(), to prevent accidental usage
    bool operator==(const TunableMacPacket&);

  public:
    TunableMacPacket(const char *name=NULL, int kind=0);
    TunableMacPacket(const TunableMacPacket& other);
    virtual ~TunableMacPacket();
    TunableMacPacket& operator=(const TunableMacPacket& other);
    virtual TunableMacPacket *dup() const {return new TunableMacPacket(*this);}
    virtual void parsimPack(cCommBuffer *b);
    virtual void parsimUnpack(cCommBuffer *b);

    // field getter/setter methods
    virtual int getFrameType() const;
    virtual void setFrameType(int frameType);
};

inline void doPacking(cCommBuffer *b, TunableMacPacket& obj) {obj.parsimPack(b);}
inline void doUnpacking(cCommBuffer *b, TunableMacPacket& obj) {obj.parsimUnpack(b);}


#endif // ifndef _TUNABLEMACPACKET_M_H_

