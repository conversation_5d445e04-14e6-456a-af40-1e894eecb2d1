# ****************************************************************************
# *  Copyright: National ICT Australia,  2007 - 2010                         *
# *  Developed at the ATP lab, Networked Systems research theme              *
# *  Author(s): <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>                        *
# *  This file is distributed under the terms in the attached LICENSE file.  *
# *  If you do not find this file, copies can be found by writing to:        *
# *                                                                          *
# *      NICTA, Locked Bag 9013, Alexandria, NSW 1435, Australia             *
# *      Attention:  License Inquiry.                                        *
# *                                                                          *
# ***************************************************************************/

# ===================================================
# Setting the right  parameter values for TunableMAC 
# will create a simple CSMA  behavior
# ===================================================

SN.node[*].communication.MACProtocolName = "TunableMAC"
SN.node[*].communication.MAC.dutyCycle = 1.0
SN.node[*].communication.MAC.randomTxOffset = 0
SN.node[*].communication.MAC.backoffType = 2 		
