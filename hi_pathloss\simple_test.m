% 简单测试非线性学习曲线

clear; close all; clc;

fprintf('测试非线性学习曲线...\n');

% 创建测试环境
env = struct();
env.power_levels = [5, 10, 20, 30, 50, 80];
env.max_steps = 50;  % 减少步数以加快测试
env.action_dim = 6;
env.motion_intensity = 0.3 + 0.4 * rand(1, env.max_steps);
env.channel_quality = -60 - 20 * rand(1, env.max_steps);

num_episodes = 100;  % 减少轮数以加快测试

% 测试各算法
fprintf('测试固定功率算法...\n');
fixed_rewards = simulate_fixed_power_training(env, num_episodes);

fprintf('测试DQN算法...\n');
dqn_rewards = train_dqn_algorithm(env, num_episodes, 'static');

fprintf('测试演员-评论家算法...\n');
ac_rewards = train_actor_critic_algorithm(env, num_episodes, 'static');

fprintf('测试分层RL算法...\n');
hrl_rewards = train_hierarchical_rl_algorithm(env, num_episodes, 'static');

% 绘制结果
figure('Position', [100, 100, 800, 600]);
plot(1:num_episodes, fixed_rewards, 'b-', 'LineWidth', 2, 'DisplayName', '固定功率');
hold on;
plot(1:num_episodes, dqn_rewards, 'r-', 'LineWidth', 2, 'DisplayName', 'DQN');
plot(1:num_episodes, ac_rewards, 'Color', [1, 0.5, 0], 'LineWidth', 2, 'DisplayName', '演员-评论家');
plot(1:num_episodes, hrl_rewards, 'g-', 'LineWidth', 2, 'DisplayName', '分层RL');

xlabel('训练轮数');
ylabel('累计奖励');
title('非线性学习曲线测试');
legend('Location', 'southeast');
grid on;

% 输出结果
fprintf('\n=== 测试结果 ===\n');
final_rewards = [fixed_rewards(end), dqn_rewards(end), ac_rewards(end), hrl_rewards(end)];
algorithm_names = {'固定功率', 'DQN', '演员-评论家', '分层RL'};

[sorted_rewards, sort_idx] = sort(final_rewards, 'descend');

fprintf('最终累计奖励排序:\n');
for i = 1:4
    fprintf('%d. %s: %.1f\n', i, algorithm_names{sort_idx(i)}, sorted_rewards(i));
end

% 检查非线性特征
fprintf('\n非线性特征检查:\n');
for i = 1:4
    rewards = eval([lower(strrep(algorithm_names{i}, '演员-评论家', 'ac')) '_rewards']);
    if strcmp(algorithm_names{i}, '演员-评论家')
        rewards = ac_rewards;
    elseif strcmp(algorithm_names{i}, '分层RL')
        rewards = hrl_rewards;
    elseif strcmp(algorithm_names{i}, 'DQN')
        rewards = dqn_rewards;
    else
        rewards = fixed_rewards;
    end
    
    % 计算前25%和后25%的增长率
    quarter = round(length(rewards)/4);
    early_growth = mean(diff(rewards(1:quarter)));
    late_growth = mean(diff(rewards(end-quarter+1:end)));
    
    if early_growth > late_growth * 1.2
        curve_type = '非线性（初期快速学习）';
    else
        curve_type = '线性或其他';
    end
    
    fprintf('  %s: %s (初期: %.1f, 后期: %.1f)\n', ...
        algorithm_names{i}, curve_type, early_growth, late_growth);
end

% 检查性能顺序
expected_order = [4, 3, 2, 1]; % 分层RL > 演员-评论家 > DQN > 固定功率
if isequal(sort_idx, expected_order)
    fprintf('\n✓ 性能顺序正确！\n');
else
    fprintf('\n✗ 性能顺序需要调整\n');
    fprintf('预期: 分层RL > 演员-评论家 > DQN > 固定功率\n');
    fprintf('实际: ');
    for i = 1:4
        fprintf('%s', algorithm_names{sort_idx(i)});
        if i < 4, fprintf(' > '); end
    end
    fprintf('\n');
end

fprintf('\n测试完成！\n');
