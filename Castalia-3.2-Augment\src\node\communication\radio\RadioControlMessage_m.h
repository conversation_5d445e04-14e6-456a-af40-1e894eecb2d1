//
// Generated file, do not edit! Created by nedtool 4.6 from src/node/communication/radio/RadioControlMessage.msg.
//

#ifndef _RADIOCONTROLMESSAGE_M_H_
#define _RADIOCONTROLMESSAGE_M_H_

#include <omnetpp.h>

// nedtool version check
#define MSGC_VERSION 0x0406
#if (MSGC_VERSION!=OMNETPP_VERSION)
#    error Version mismatch! Probably this file was generated by an earlier version of nedtool: 'make clean' should help.
#endif



/**
 * Enum generated from <tt>src/node/communication/radio/RadioControlMessage.msg:14</tt> by nedtool.
 * <pre>
 * // These messages will be initiated by the radio and sent to upper layer
 * enum RadioControlMessage_type
 * {
 * 
 *     CARRIER_SENSE_INTERRUPT = 1;
 *     RADIO_BUFFER_FULL = 2;
 * }
 * </pre>
 */
enum RadioControlMessage_type {
    CARRIER_SENSE_INTERRUPT = 1,
    RADIO_BUFFER_FULL = 2
};

/**
 * Class generated from <tt>src/node/communication/radio/RadioControlMessage.msg:19</tt> by nedtool.
 * <pre>
 * message RadioControlMessage
 * {
 *     int radioControlMessageKind @enum(RadioControlMessage_type);
 * }
 * </pre>
 */
class RadioControlMessage : public ::cMessage
{
  protected:
    int radioControlMessageKind_var;

  private:
    void copy(const RadioControlMessage& other);

  protected:
    // protected and unimplemented operator==(), to prevent accidental usage
    bool operator==(const RadioControlMessage&);

  public:
    RadioControlMessage(const char *name=NULL, int kind=0);
    RadioControlMessage(const RadioControlMessage& other);
    virtual ~RadioControlMessage();
    RadioControlMessage& operator=(const RadioControlMessage& other);
    virtual RadioControlMessage *dup() const {return new RadioControlMessage(*this);}
    virtual void parsimPack(cCommBuffer *b);
    virtual void parsimUnpack(cCommBuffer *b);

    // field getter/setter methods
    virtual int getRadioControlMessageKind() const;
    virtual void setRadioControlMessageKind(int radioControlMessageKind);
};

inline void doPacking(cCommBuffer *b, RadioControlMessage& obj) {obj.parsimPack(b);}
inline void doUnpacking(cCommBuffer *b, RadioControlMessage& obj) {obj.parsimUnpack(b);}

/**
 * Enum generated from <tt>src/node/communication/radio/RadioControlMessage.msg:23</tt> by nedtool.
 * <pre>
 * enum BasicState_type
 * {
 * 
 *     RX = 0;
 *     TX = 1;
 *     SLEEP = 2;
 * }
 * </pre>
 */
enum BasicState_type {
    RX = 0,
    TX = 1,
    SLEEP = 2
};

/**
 * Enum generated from <tt>src/node/communication/radio/RadioControlMessage.msg:30</tt> by nedtool.
 * <pre>
 * // These commands will be received by the radio from upper layers
 * enum RadioControlCommand_type
 * {
 * 
 *     SET_STATE = 0;
 *     SET_MODE = 1;
 *     SET_TX_OUTPUT = 2;
 *     SET_SLEEP_LEVEL = 3;
 *     SET_CARRIER_FREQ = 4;
 *     SET_CCA_THRESHOLD = 5;
 *     SET_CS_INTERRUPT_ON = 6;
 *     SET_CS_INTERRUPT_OFF = 7;
 *     SET_ENCODING = 8;
 * }
 * </pre>
 */
enum RadioControlCommand_type {
    SET_STATE = 0,
    SET_MODE = 1,
    SET_TX_OUTPUT = 2,
    SET_SLEEP_LEVEL = 3,
    SET_CARRIER_FREQ = 4,
    SET_CCA_THRESHOLD = 5,
    SET_CS_INTERRUPT_ON = 6,
    SET_CS_INTERRUPT_OFF = 7,
    SET_ENCODING = 8
};

/**
 * Class generated from <tt>src/node/communication/radio/RadioControlMessage.msg:42</tt> by nedtool.
 * <pre>
 * message RadioControlCommand
 * {
 *     int radioControlCommandKind @enum(RadioControlCommand_type);
 *     int state @enum(BasicState_type) = RX;	//to be used with SET_STATE
 *     double parameter = 0.0;	//to be used with SET_TX_OUTPUT, SET_CARRIER_FREQ, SET_CCA_THRESHOLD
 *     string name = "";	//to be used with SET_MODE, SET_SLEEP_LEVEL and SET_ENCODING
 * }
 * </pre>
 */
class RadioControlCommand : public ::cMessage
{
  protected:
    int radioControlCommandKind_var;
    int state_var;
    double parameter_var;
    opp_string name_var;

  private:
    void copy(const RadioControlCommand& other);

  protected:
    // protected and unimplemented operator==(), to prevent accidental usage
    bool operator==(const RadioControlCommand&);

  public:
    RadioControlCommand(const char *name=NULL, int kind=0);
    RadioControlCommand(const RadioControlCommand& other);
    virtual ~RadioControlCommand();
    RadioControlCommand& operator=(const RadioControlCommand& other);
    virtual RadioControlCommand *dup() const {return new RadioControlCommand(*this);}
    virtual void parsimPack(cCommBuffer *b);
    virtual void parsimUnpack(cCommBuffer *b);

    // field getter/setter methods
    virtual int getRadioControlCommandKind() const;
    virtual void setRadioControlCommandKind(int radioControlCommandKind);
    virtual int getState() const;
    virtual void setState(int state);
    virtual double getParameter() const;
    virtual void setParameter(double parameter);
    virtual const char * getName() const;
    virtual void setName(const char * name);
};

inline void doPacking(cCommBuffer *b, RadioControlCommand& obj) {obj.parsimPack(b);}
inline void doUnpacking(cCommBuffer *b, RadioControlCommand& obj) {obj.parsimUnpack(b);}


#endif // ifndef _RADIOCONTROLMESSAGE_M_H_

