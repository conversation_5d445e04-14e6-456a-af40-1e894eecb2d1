% validate_bvh_rssi.m
% 验证 BVH→RSSI (路径损耗) 映射模型
% 读取某场景测量路径损耗 txt，与默认 env 生成的 RSSI 数据比较
% 计算 RMSE, 相关系数并绘图

clear; clc;
addpath(genpath('.'));

% ---------- 用户可配置 ----------
meas_file = '../13_04_pl.txt';   % 测量路径损耗 txt
scenario_name = '13_04 场景';
plot_len = 1000;                 % 取前 1000 点示例绘图
% ---------------------------------

% 1. 读取测量数据 (取第二列)
meas_data = readmatrix(meas_file);
meas_pl = meas_data(:,2);    % 假设第二列为路径损耗 (dB)

% 2. 构造默认环境，获取模拟 RSSI
env = rl_environment();
% env.time_vector 已初始化到 max_steps
sim_rssi = env.rssi_data(:);          % dBm, 负值
sim_pl   = -sim_rssi;                 % 转换为 dB 路径损耗

% 3. 对齐长度
len = min(length(meas_pl), length(sim_pl));
meas_pl = meas_pl(1:len);
sim_pl  = sim_pl(1:len);

% 4. 计算误差指标
rmse = sqrt(mean((sim_pl - meas_pl).^2));
R = corrcoef(meas_pl, sim_pl);
if numel(R)==4, corr_val = R(1,2); else, corr_val = 0; end

fprintf('场景 %s\n数据长度: %d 样本\nRMSE: %.2f dB\n相关系数: %.3f\n', ...
        scenario_name, len, rmse, corr_val);

% 5. 绘图
idx = 1:min(plot_len,len);
figure('Name','Measured vs Simulated Pathloss');
plot(idx, meas_pl(idx), 'b-', 'DisplayName', 'Measured'); hold on;
plot(idx, sim_pl(idx), 'r--', 'DisplayName', 'Simulated');
legend; xlabel('Sample'); ylabel('Pathloss (dB)');
 title(sprintf('Pathloss Comparison (%s) RMSE=%.2f dB', scenario_name, rmse)); 