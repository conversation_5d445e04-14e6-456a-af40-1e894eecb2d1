% ablation_experiments.m
% 运行三组消融实验并输出结果到控制台
clear; clc;
addpath(genpath('.'));

% 场景示例 (静态)
scenario = struct('name','静态测试','type','static');

% 基础参数配置
base_params = struct(...
    'num_episodes',40,...
    'max_steps_per_episode',200,...
    'batch_size',32,...
    'update_frequency',10,...
    'learning_rate',0.001,...
    'gamma',0.95,...
    'epsilon',1.0,...
    'epsilon_decay',0.995,...
    'epsilon_min',0.05);

%% 1) 仅下层 DQN vs 分层 RL
fprintf('\n=== 消融 1: 下层 DQN vs 分层 RL ===\n');
env = rl_environment();
[agent_dqn,res_dqn] = train_dqn(env,base_params);
[agent_hrl,res_hrl] = train_hierarchical_rl(env,scenario);
fprintf('DQN AvgReward %.2f | Hierarchical RL AvgReward %.2f\n', ...
        res_dqn.final_avg_reward, res_hrl.final_avg_reward);

%% 2) 不同 Manager hidden size
fprintf('\n=== 消融 2: 不同 Manager hidden size ===\n');
hsizes = [32,64,128];
for h = hsizes
    env_h = rl_environment();
    % 修改全局变量以控制 train_hierarchical_rl 内隐藏层
    global HRL_HIDDEN_SIZE; %#ok<TLEV>
    HRL_HIDDEN_SIZE = h;    % train_hierarchical_rl 内部如检测到此变量则采用
    [~, res_h] = train_hierarchical_rl(env_h, scenario);
    fprintf('Hidden %3d -> AvgReward %.2f\n', h, res_h.final_avg_reward);
end
clear global HRL_HIDDEN_SIZE;

%% 3) 无 RSSI / 无 IMU 特征
fprintf('\n=== 消融 3: 特征遮蔽 ===\n');
env_no_rssi = make_mask_env('rssi');
[~,res_rssi] = train_hierarchical_rl(env_no_rssi,scenario);

env_no_imu  = make_mask_env('imu');
[~,res_imu] = train_hierarchical_rl(env_no_imu,scenario);

fprintf('无 RSSI AvgReward %.2f | 无 IMU AvgReward %.2f | 原始 %.2f\n', ...
        res_rssi.final_avg_reward, res_imu.final_avg_reward, res_hrl.final_avg_reward);

%% --------- 辅助函数 --------
function env_mask = make_mask_env(flag)
    base_env = rl_environment();
    env_mask = base_env; % value copy
    original_get = base_env.get_current_state;

    % 定义新的 get_current_state
    function s = new_get_state()
        s = original_get();
        switch flag
            case 'rssi', s(4)=0;
            case 'imu',  s(1)=0; s(5)=0;
        end
    end

    env_mask.get_current_state = @new_get_state;
end 