# ********************************************************************************
# *  Copyright: National ICT Australia,  2007 - 2010                             *
# *  Developed at the ATP lab, Networked Systems research theme                  *
# *  Author(s): <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>  *
# *  This file is distributed under the terms in the attached LICENSE file.      *
# *  If you do not find this file, copies can be found by writing to:            *
# *                                                                              *
# *      NICTA, Locked Bag 9013, Alexandria, NSW 1435, Australia                 *
# *      Attention:  License Inquiry.                                            *
# *                                                                              *
# *******************************************************************************/


#==============================================================================
# Here default parameters of CustomizablePhysicalProcess module are changed 
# to directly assign a particular value (40) to node 6. This value will be 
# returned by sensors of this node
#==============================================================================

SN.physicalProcessName = "CustomizablePhysicalProcess"
SN.physicalProcess[0].inputType = 0  		
SN.physicalProcess[0].directNodeValueAssignment = "(0) 6:40"  
