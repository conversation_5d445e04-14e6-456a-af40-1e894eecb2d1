# ********************************************************************************
# *  Copyright: National ICT Australia,  2007 - 2010                             *
# *  Developed at the ATP lab, Networked Systems research theme                  *
# *  Author(s): <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>  *
# *  This file is distributed under the terms in the attached LICENSE file.      *
# *  If you do not find this file, copies can be found by writing to:            *
# *                                                                              *
# *      NICTA, Locked Bag 9013, Alexandria, NSW 1435, Australia                 *
# *      Attention:  License Inquiry.                                            *
# *                                                                              *
# *******************************************************************************/

[General]

# ==========================================================
# Always include the main Castalia.ini file
# ==========================================================
include ../Parameters/Castalia.ini

sim-time-limit = 51s  # 50 secs of data + 1 sec of MAC setup
record-eventlog = true

SN.numNodes = 3

#SN.wirelessChannel.pathLossMapFile = "../Parameters/WirelessChannel/BANmodels/pathLossMap.txt"
SN.wirelessChannel.pathLossFile = "../Parameters/WirelessChannel/BANmodels/pathLoss.txt"
#SN.wirelessChannel.temporalModelParametersFile = "../Parameters/WirelessChannel/BANmodels/TemporalModel.txt"
SN.wirelessChannel.collectTraceInfo = true

SN.node[*].Communication.Radio.RadioParametersFile = "../Parameters/Radio/BANRadio.txt"
SN.node[*].Communication.Radio.symbolsForRSSI = 16
SN.node[*].Communication.Radio.TxOutputPower = "-15dBm"
SN.node[*].Communication.Radio.collectTraceInfo = true

#SN.node[*].Communication.MAC.collectTraceInfo = true
SN.node[*].Application.collectTraceInfo = true

SN.node[*].ResourceManager.baselineNodePower = 0

SN.node[*].ApplicationName = "ThroughputTest"
SN.node[*].Application.startupDelay = 1  	#wait for 1sec before starting sending packets
SN.node[0].Application.latencyHistogramMax = 600
SN.node[0].Application.latencyHistogramBuckets = 30
 
SN.node[2].Application.packet_rate = 5


[Config TMAC]
SN.node[*].Communication.MACProtocolName = "TMAC"
SN.node[*].Communication.MAC.phyDataRate = 1024

[Config ZigBeeMAC]
SN.node[*].Communication.MACProtocolName = "Mac802154"
SN.node[0].Communication.MAC.isFFD = true
SN.node[0].Communication.MAC.isPANCoordinator = true
SN.node[*].Communication.MAC.phyDataRate = 1024
SN.node[*].Communication.MAC.phyBitsPerSymbol = 2

[Config GTSon]
SN.node[*].Communication.MAC.requestGTS = 3

[Config GTSoff]
SN.node[*].Communication.MAC.requestGTS = 0


[Config noTemporal]
SN.wirelessChannel.temporalModelParametersFile = ""

[Config BaselineMAC]
SN.node[*].Communication.MACProtocolName = "BaselineBANMac"
SN.node[*].Communication.MAC.phyDataRate = 1024
SN.node[0].Communication.MAC.isHub = true
SN.node[*].Communication.MAC.macBufferSize = 48

[Config pollingON]
SN.node[*].Communication.MAC.pollingEnabled = true

[Config pollingOFF]
SN.node[*].Communication.MAC.pollingEnabled = false

[Config naivePolling]
SN.node[*].Communication.MAC.naivePollingScheme = true

[Config minScheduled]
SN.node[*].Communication.MAC.scheduledAccessLength = 2

[Config maxScheduled]
SN.node[*].Communication.MAC.scheduledAccessLength = 6
SN.node[*].Communication.MAC.RAP1Length = 2

[Config varyScheduled]
SN.node[*].Communication.MAC.scheduledAccessLength = ${schedSlots=6,5,4,3}
SN.node[*].Communication.MAC.RAP1Length = ${RAPslots=2,7,12,17}
constraint = $schedSlots * 5 + $RAPslots == 32

[Config varyRAPlength]
#SN.node[*].Communication.MAC.RAP1Length = ${RAPlength=1,6,11,16,21}
SN.node[*].Communication.MAC.RAP1Length = ${RAPlength=2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22}

[Config oneNodeVaryRate]
SN.node[3].Application.packet_rate = ${rate=20,40,60,80,100}

[Config oneNodeVaryPower]
SN.node[3].Communication.Radio.TxOutputPower = ${power="-10dBm","-12dBm","-15dBm","-20dBm"}

[Config oneNodeVaryTxNum]
SN.node[3].Communication.MAC.macMaxFrameRetries = ${retries=1,2,3}

[Config allNodesVaryRate]
#SN.node[*].Application.packet_rate = ${rate=20,40,60,80,100,120}
SN.node[*].Application.packet_rate = ${rate=14,16,18,20,22,24,26,28,30}
#SN.node[*].Application.packet_rate = ${rate=100,120,140,160}

[Config setRate]
SN.node[*].Application.packet_rate = 25

[Config setPower]
SN.node[*].Communication.Radio.TxOutputPower = "-15dBm"

[Config allNodesVaryPower]
SN.node[*].Communication.Radio.TxOutputPower = ${power="-10dBm","-12dBm","-15dBm","-20dBm"}

[Config varyReTxNum]
SN.node[*].Communication.MAC.maxPacketTries = ${pktTries=1,2,3,4}
