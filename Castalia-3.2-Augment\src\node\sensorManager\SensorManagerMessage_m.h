//
// Generated file, do not edit! Created by nedtool 4.6 from src/node/sensorManager/SensorManagerMessage.msg.
//

#ifndef _SENSORMANAGERMESSAGE_M_H_
#define _SENSORMANAGERMESSAGE_M_H_

#include <omnetpp.h>

// nedtool version check
#define MSGC_VERSION 0x0406
#if (MSGC_VERSION!=OMNETPP_VERSION)
#    error Version mismatch! Probably this file was generated by an earlier version of nedtool: 'make clean' should help.
#endif



/**
 * Class generated from <tt>src/node/sensorManager/SensorManagerMessage.msg:13</tt> by nedtool.
 * <pre>
 * message SensorReadingMessage
 * {
 *     double sensedValue;
 *     string sensorType;
 *     int sensorIndex;
 * }
 * </pre>
 */
class SensorReadingMessage : public ::cMessage
{
  protected:
    double sensedValue_var;
    opp_string sensorType_var;
    int sensorIndex_var;

  private:
    void copy(const SensorReadingMessage& other);

  protected:
    // protected and unimplemented operator==(), to prevent accidental usage
    bool operator==(const SensorReadingMessage&);

  public:
    SensorReadingMessage(const char *name=NULL, int kind=0);
    SensorReadingMessage(const SensorReadingMessage& other);
    virtual ~SensorReadingMessage();
    SensorReadingMessage& operator=(const SensorReadingMessage& other);
    virtual SensorReadingMessage *dup() const {return new SensorReadingMessage(*this);}
    virtual void parsimPack(cCommBuffer *b);
    virtual void parsimUnpack(cCommBuffer *b);

    // field getter/setter methods
    virtual double getSensedValue() const;
    virtual void setSensedValue(double sensedValue);
    virtual const char * getSensorType() const;
    virtual void setSensorType(const char * sensorType);
    virtual int getSensorIndex() const;
    virtual void setSensorIndex(int sensorIndex);
};

inline void doPacking(cCommBuffer *b, SensorReadingMessage& obj) {obj.parsimPack(b);}
inline void doUnpacking(cCommBuffer *b, SensorReadingMessage& obj) {obj.parsimUnpack(b);}


#endif // ifndef _SENSORMANAGERMESSAGE_M_H_

