# ********************************************************************************
# *  Copyright: National ICT Australia,  2007 - 2010                             *
# *  Developed at the ATP lab, Networked Systems research theme                  *
# *  Author(s): <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>                      *
# *  This file is distributed under the terms in the attached LICENSE file.      *
# *  If you do not find this file, copies can be found by writing to:            *
# *                                                                              *
# *      NICTA, Locked Bag 9013, Alexandria, NSW 1435, Australia                 *
# *      Attention:  License Inquiry.                                            *
# *                                                                              *
# *******************************************************************************/

[General]

# ==========================================================
# Always include the main Castalia.ini file
# ==========================================================

include ../Parameters/Castalia.ini

sim-time-limit = 600s

SN.field_x = 100					# meters
SN.field_y = 100					# meters

SN.numNodes = 36
SN.deployment = "6x6"

SN.node[*].Communication.Radio.RadioParametersFile = "../Parameters/Radio/CC2420.txt"

SN.node[*].Communication.RoutingProtocolName = "MultipathRingsRouting"
SN.node[*].Communication.MACProtocolName = "TMAC"

SN.node[*].ApplicationName = "ValueReporting"
SN.node[*].Communication.Radio.txPowerLevelUsed = 2
SN.node[*].Communication.Routing.neighbor_RSSIThreshold = -89.3 # in dBm
SN.node[*].Communication.Routing.collectTraceInfo = true

SN.node[3].Application.isSink = true

