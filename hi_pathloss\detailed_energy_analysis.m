% 详细能耗分析报告
% 基于算法能耗对比实验结果进行深入分析
function detailed_energy_analysis()
    close all;
    clear;
    clc;
    
    fprintf('=== 详细能耗分析报告 ===\n\n');
    
    % 加载实验结果
    if exist('algorithm_energy_comparison_results.mat', 'file')
        load('algorithm_energy_comparison_results.mat');
        fprintf('✓ 成功加载实验结果\n\n');
    else
        fprintf('❌ 未找到实验结果文件，请先运行 algorithm_energy_comparison\n');
        return;
    end
    
    % 提取数据
    energy_results = results.energy_results;
    energy_std = results.energy_std;
    scenario_names = results.scenario_names;
    algorithm_names = results.algorithm_names;
    
    % 详细分析
    analyze_algorithm_performance(energy_results, energy_std, scenario_names, algorithm_names);
    analyze_scenario_characteristics(energy_results, energy_std, scenario_names, algorithm_names);
    generate_improvement_analysis(energy_results, energy_std, scenario_names, algorithm_names);
    create_enhanced_visualizations(energy_results, energy_std, scenario_names, algorithm_names);
    
    fprintf('=== 详细分析完成 ===\n');
end

function analyze_algorithm_performance(energy_results, energy_std, scenario_names, algorithm_names)
    % 算法性能详细分析
    fprintf('=== 算法性能详细分析 ===\n\n');
    
    % 1. 各算法在不同场景下的表现
    fprintf('1. 各算法在不同场景下的能耗表现:\n\n');
    
    for a = 1:length(algorithm_names)
        fprintf('%s:\n', algorithm_names{a});
        for s = 1:length(scenario_names)
            fprintf('  %s: %.3f ± %.3f mJ\n', ...
                   scenario_names{s}, energy_results(a, s), energy_std(a, s));
        end
        
        % 计算变异系数
        cv = std(energy_results(a, :)) / mean(energy_results(a, :)) * 100;
        fprintf('  场景适应性 (变异系数): %.1f%%\n', cv);
        
        % 最佳和最差场景
        [min_energy, min_idx] = min(energy_results(a, :));
        [max_energy, max_idx] = max(energy_results(a, :));
        fprintf('  最佳场景: %s (%.3f mJ)\n', scenario_names{min_idx}, min_energy);
        fprintf('  最差场景: %s (%.3f mJ)\n', scenario_names{max_idx}, max_energy);
        fprintf('  性能差异: %.3f mJ (%.1f%%)\n\n', ...
               max_energy - min_energy, (max_energy - min_energy) / min_energy * 100);
    end
    
    % 2. 算法稳定性分析
    fprintf('2. 算法稳定性分析 (基于标准差):\n\n');
    
    avg_std = mean(energy_std, 2);
    [sorted_std, std_idx] = sort(avg_std);
    
    fprintf('稳定性排名 (标准差越小越稳定):\n');
    for i = 1:length(algorithm_names)
        fprintf('%d. %s: %.3f mJ (平均标准差)\n', ...
               i, algorithm_names{std_idx(i)}, sorted_std(i));
    end
    fprintf('\n');
    
    % 3. 算法总体性能排名
    fprintf('3. 算法总体性能排名:\n\n');
    
    avg_energy = mean(energy_results, 2);
    [sorted_energy, energy_idx] = sort(avg_energy);
    
    fprintf('能耗排名 (越低越好):\n');
    for i = 1:length(algorithm_names)
        fprintf('%d. %s: %.3f mJ (平均能耗)\n', ...
               i, algorithm_names{energy_idx(i)}, sorted_energy(i));
    end
    fprintf('\n');
end

function analyze_scenario_characteristics(energy_results, energy_std, scenario_names, algorithm_names)
    % 场景特征分析
    fprintf('=== 场景特征分析 ===\n\n');
    
    % 1. 各场景的能耗特征
    fprintf('1. 各场景的能耗特征:\n\n');
    
    for s = 1:length(scenario_names)
        fprintf('%s:\n', scenario_names{s});
        
        scenario_energies = energy_results(:, s);
        scenario_stds = energy_std(:, s);
        
        fprintf('  平均能耗: %.3f mJ\n', mean(scenario_energies));
        fprintf('  能耗范围: %.3f - %.3f mJ\n', min(scenario_energies), max(scenario_energies));
        fprintf('  算法间差异: %.3f mJ\n', max(scenario_energies) - min(scenario_energies));
        
        % 最佳算法
        [min_energy, best_alg] = min(scenario_energies);
        fprintf('  最佳算法: %s (%.3f mJ)\n', algorithm_names{best_alg}, min_energy);
        
        % 算法区分度
        discrimination = std(scenario_energies) / mean(scenario_energies) * 100;
        fprintf('  算法区分度: %.1f%%\n\n', discrimination);
    end
    
    % 2. 场景复杂度分析
    fprintf('2. 场景复杂度分析:\n\n');
    
    scenario_complexity = zeros(length(scenario_names), 1);
    for s = 1:length(scenario_names)
        % 基于算法间能耗差异和标准差计算复杂度
        energy_range = max(energy_results(:, s)) - min(energy_results(:, s));
        avg_std = mean(energy_std(:, s));
        scenario_complexity(s) = energy_range + avg_std;
    end
    
    [sorted_complexity, complexity_idx] = sort(scenario_complexity, 'descend');
    
    fprintf('场景复杂度排名 (基于算法差异和不确定性):\n');
    for i = 1:length(scenario_names)
        fprintf('%d. %s: %.3f (复杂度指数)\n', ...
               i, scenario_names{complexity_idx(i)}, sorted_complexity(i));
    end
    fprintf('\n');
end

function generate_improvement_analysis(energy_results, energy_std, scenario_names, algorithm_names)
    % 改进潜力分析
    fprintf('=== 改进潜力分析 ===\n\n');
    
    % 1. 分层RL相对于其他算法的改进空间
    hierarchical_idx = find(strcmp(algorithm_names, '分层RL'));
    fixed_power_idx = find(strcmp(algorithm_names, '固定功率'));
    simple_dqn_idx = find(strcmp(algorithm_names, 'DQN'));
    
    if ~isempty(hierarchical_idx)
        fprintf('1. 分层RL算法改进分析:\n\n');
        
        for s = 1:length(scenario_names)
            fprintf('%s:\n', scenario_names{s});
            
            hierarchical_energy = energy_results(hierarchical_idx, s);
            
            if ~isempty(fixed_power_idx)
                fixed_energy = energy_results(fixed_power_idx, s);
                improvement_vs_fixed = (fixed_energy - hierarchical_energy) / fixed_energy * 100;
                fprintf('  相对固定功率: %.1f%% ', improvement_vs_fixed);
                if improvement_vs_fixed > 0
                    fprintf('(改进)\n');
                else
                    fprintf('(需优化)\n');
                end
            end
            
            if ~isempty(simple_dqn_idx)
                dqn_energy = energy_results(simple_dqn_idx, s);
                improvement_vs_dqn = (dqn_energy - hierarchical_energy) / dqn_energy * 100;
                fprintf('  相对DQN: %.1f%% ', improvement_vs_dqn);
                if improvement_vs_dqn > 0
                    fprintf('(改进)\n');
                else
                    fprintf('(需优化)\n');
                end
            end
            
            fprintf('\n');
        end
    end
    
    % 2. 各场景下的最优性分析
    fprintf('2. 各场景下的最优性分析:\n\n');
    
    for s = 1:length(scenario_names)
        [min_energy, best_idx] = min(energy_results(:, s));
        [max_energy, worst_idx] = max(energy_results(:, s));
        
        fprintf('%s:\n', scenario_names{s});
        fprintf('  最优算法: %s (%.3f mJ)\n', algorithm_names{best_idx}, min_energy);
        fprintf('  最差算法: %s (%.3f mJ)\n', algorithm_names{worst_idx}, max_energy);
        
        % 改进潜力
        improvement_potential = (max_energy - min_energy) / max_energy * 100;
        fprintf('  改进潜力: %.1f%%\n\n', improvement_potential);
    end
    
    % 3. 推荐优化策略
    fprintf('3. 推荐优化策略:\n\n');
    
    if ~isempty(hierarchical_idx)
        hierarchical_energies = energy_results(hierarchical_idx, :);
        [worst_scenario_energy, worst_scenario] = max(hierarchical_energies);
        [best_scenario_energy, best_scenario] = min(hierarchical_energies);
        
        fprintf('分层RL算法优化建议:\n');
        fprintf('  优势场景: %s (%.3f mJ) - 保持当前策略\n', ...
               scenario_names{best_scenario}, best_scenario_energy);
        fprintf('  劣势场景: %s (%.3f mJ) - 需要针对性优化\n', ...
               scenario_names{worst_scenario}, worst_scenario_energy);
        
        if worst_scenario == 1 % 静态场景
            fprintf('  静态场景优化建议: 增强节能策略，减少探索\n');
        elseif worst_scenario == 2 % 动态场景
            fprintf('  动态场景优化建议: 提高适应性，优化转换策略\n');
        elseif worst_scenario == 3 % 周期性场景
            fprintf('  周期性场景优化建议: 利用周期性模式，预测性调整\n');
        end
        fprintf('\n');
    end
end

function create_enhanced_visualizations(energy_results, energy_std, scenario_names, algorithm_names)
    % 创建增强的可视化图表
    fprintf('4. 生成增强可视化图表...\n\n');

    % 设置科学绘图参数
    set(0, 'DefaultAxesFontSize', 14);
    set(0, 'DefaultTextFontSize', 14);
    set(0, 'DefaultAxesFontName', 'Times New Roman');
    set(0, 'DefaultTextFontName', 'Times New Roman');

    % 图1: 详细对比图（包含误差棒和数值标签）
    figure('Position', [100, 100, 1200, 800]);

    % 创建子图布局
    subplot(2, 2, 1);

    % 分组柱状图
    bar_data = energy_results';
    h = bar(bar_data, 'grouped');

    % 设置颜色
    colors = [0.3, 0.7, 0.9;    % 固定功率 - 浅蓝色
              0.9, 0.5, 0.2;    % DQN - 橙色
              0.2, 0.8, 0.3];   % 分层RL - 绿色

    for i = 1:length(h)
        h(i).FaceColor = colors(i, :);
        h(i).EdgeColor = 'black';
        h(i).LineWidth = 1.2;
    end

    % 添加误差棒
    hold on;
    for i = 1:size(bar_data, 1)
        for j = 1:size(bar_data, 2)
            x_pos = i + (j - 2) * 0.27;
            errorbar(x_pos, bar_data(i, j), energy_std(j, i), 'k', 'LineWidth', 1.5, 'CapSize', 6);

            % 添加数值标签
            text(x_pos, bar_data(i, j) + energy_std(j, i) + 0.15, ...
                sprintf('%.2f', bar_data(i, j)), ...
                'HorizontalAlignment', 'center', 'FontSize', 11, 'FontWeight', 'bold');
        end
    end

    xlabel('运动场景', 'FontSize', 14);
    ylabel('平均能耗 (mJ)', 'FontSize', 14);
    title('(a) 算法能耗对比', 'FontSize', 16, 'FontWeight', 'bold');
    set(gca, 'XTickLabel', scenario_names);
    legend(algorithm_names, 'Location', 'best', 'FontSize', 12);
    grid on;
    grid minor;

    % 图2: 相对性能图
    subplot(2, 2, 2);

    % 以固定功率为基准的相对性能
    fixed_power_idx = 1; % 假设固定功率是第一个
    relative_performance = zeros(size(energy_results));
    for s = 1:size(energy_results, 2)
        baseline = energy_results(fixed_power_idx, s);
        relative_performance(:, s) = (energy_results(:, s) - baseline) / baseline * 100;
    end

    bar(relative_performance', 'grouped');
    xlabel('运动场景', 'FontSize', 14);
    ylabel('相对能耗变化 (%)', 'FontSize', 14);
    title('(b) 相对于固定功率的性能变化', 'FontSize', 16, 'FontWeight', 'bold');
    set(gca, 'XTickLabel', scenario_names);
    legend(algorithm_names, 'Location', 'best', 'FontSize', 12);
    grid on;
    yline(0, 'r--', 'LineWidth', 2);

    % 图3: 算法稳定性对比
    subplot(2, 2, 3);

    avg_std = mean(energy_std, 2);
    bar(avg_std, 'FaceColor', [0.6, 0.4, 0.8], 'EdgeColor', 'black');
    xlabel('算法', 'FontSize', 14);
    ylabel('平均标准差 (mJ)', 'FontSize', 14);
    title('(c) 算法稳定性对比', 'FontSize', 16, 'FontWeight', 'bold');
    set(gca, 'XTickLabel', algorithm_names, 'XTickLabelRotation', 45);
    grid on;

    % 添加数值标签
    for i = 1:length(avg_std)
        text(i, avg_std(i) + 0.01, sprintf('%.3f', avg_std(i)), ...
            'HorizontalAlignment', 'center', 'FontSize', 11, 'FontWeight', 'bold');
    end

    % 图4: 场景复杂度分析
    subplot(2, 2, 4);

    scenario_range = max(energy_results) - min(energy_results);
    bar(scenario_range, 'FaceColor', [0.8, 0.6, 0.2], 'EdgeColor', 'black');
    xlabel('运动场景', 'FontSize', 14);
    ylabel('算法间能耗差异 (mJ)', 'FontSize', 14);
    title('(d) 场景复杂度分析', 'FontSize', 16, 'FontWeight', 'bold');
    set(gca, 'XTickLabel', scenario_names, 'XTickLabelRotation', 45);
    grid on;

    % 添加数值标签
    for i = 1:length(scenario_range)
        text(i, scenario_range(i) + 0.05, sprintf('%.2f', scenario_range(i)), ...
            'HorizontalAlignment', 'center', 'FontSize', 11, 'FontWeight', 'bold');
    end

    % 调整子图间距
    sgtitle('算法能耗详细对比分析', 'FontSize', 18, 'FontWeight', 'bold');

    % 保存图表
    saveas(gcf, 'detailed_energy_analysis.png');
    saveas(gcf, 'detailed_energy_analysis.fig');

    % 图5: 雷达图对比
    figure('Position', [200, 200, 800, 600]);

    % 准备雷达图数据
    metrics = {'静态能耗', '动态能耗', '周期能耗', '平均稳定性'};

    % 归一化数据 (越小越好，所以用倒数)
    normalized_data = zeros(length(algorithm_names), 4);
    for a = 1:length(algorithm_names)
        normalized_data(a, 1:3) = 1 ./ energy_results(a, :); % 能耗倒数
        normalized_data(a, 4) = 1 / mean(energy_std(a, :));  % 稳定性倒数
    end

    % 归一化到[0,1]
    for i = 1:4
        normalized_data(:, i) = normalized_data(:, i) / max(normalized_data(:, i));
    end

    % 绘制雷达图
    theta = linspace(0, 2*pi, 5);

    for a = 1:length(algorithm_names)
        values = [normalized_data(a, :), normalized_data(a, 1)]; % 闭合
        polarplot(theta, values, 'o-', 'LineWidth', 2.5, 'MarkerSize', 8, 'Color', colors(a, :));
        hold on;
    end

    % 设置雷达图
    thetaticks([0, 90, 180, 270]);
    thetaticklabels(metrics);
    title('算法综合性能雷达图', 'FontSize', 16, 'FontWeight', 'bold');
    legend(algorithm_names, 'Location', 'best', 'FontSize', 12);

    % 保存雷达图
    saveas(gcf, 'algorithm_performance_radar.png');
    saveas(gcf, 'algorithm_performance_radar.fig');

    fprintf('✓ 增强可视化图表已生成:\n');
    fprintf('  - detailed_energy_analysis.png\n');
    fprintf('  - detailed_energy_analysis.fig\n');
    fprintf('  - algorithm_performance_radar.png\n');
    fprintf('  - algorithm_performance_radar.fig\n\n');
end
