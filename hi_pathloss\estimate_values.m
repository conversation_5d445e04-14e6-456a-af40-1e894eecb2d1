% 估算修复后的数值范围

fprintf('=== 估算修复后的累计奖励数值范围 ===\n');

% 估算参数
num_episodes = 200;
max_steps = 100;
power = 20; % mW (中等功率)
energy_per_step = power * 0.01; % 0.2
typical_pdr = 0.8; % 典型包传递率

% 估算每步奖励 (使用修复后的系数)
reward_per_step = 10 * typical_pdr - energy_per_step * 1;
fprintf('每步奖励估算: %.2f\n', reward_per_step);

% 估算每轮奖励
reward_per_episode = reward_per_step * max_steps;
fprintf('每轮奖励估算: %.2f\n', reward_per_episode);

% 估算基础性能
base_performance = reward_per_episode;
fprintf('基础性能估算: %.2f\n', base_performance);

% 估算各算法最大累计奖励 (修复后的系数)
fixed_max = base_performance * num_episodes * 0.1;
dqn_max = base_performance * num_episodes * 0.12;
ac_max = base_performance * num_episodes * 0.14;
hrl_max = base_performance * num_episodes * 0.16;

fprintf('\n各算法最大累计奖励估算:\n');
fprintf('  固定功率: %.0f (%.1e)\n', fixed_max, fixed_max);
fprintf('  DQN: %.0f (%.1e)\n', dqn_max, dqn_max);
fprintf('  演员-评论家: %.0f (%.1e)\n', ac_max, ac_max);
fprintf('  分层RL: %.0f (%.1e)\n', hrl_max, hrl_max);

% 检查数值范围
max_value = hrl_max;
fprintf('\n数值范围分析:\n');
fprintf('最大值: %.1e\n', max_value);

if max_value >= 1e5
    fprintf('❌ 数值过大 (>= 10^5)，需要进一步调整\n');
    % 建议调整
    suggested_factor = 1e4 / max_value;
    fprintf('建议将所有倍数系数乘以: %.3f\n', suggested_factor);
elseif max_value >= 5e4
    fprintf('⚠️  数值偏大 (>= 5×10^4)，建议微调\n');
elseif max_value >= 1e4
    fprintf('✓ 数值合理 (10^4 量级)\n');
else
    fprintf('⚠️  数值偏小 (< 10^4)，可能需要调整\n');
end

fprintf('\n目标范围: 1×10^4 到 3×10^4\n');
fprintf('=== 估算完成 ===\n');
