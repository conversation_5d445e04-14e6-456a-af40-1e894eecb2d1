% 适应性实验环境接口
% 为适应性实验提供统一的环境接口

function env = rl_environment()
    % 创建强化学习环境
    
    env = struct();
    
    % 环境基本参数
    env.state_dim = 8;
    env.action_dim = 4;
    env.max_steps = 300;
    env.current_step = 1;
    
    % 功率等级设置
    env.power_levels = [10, 15, 20, 25]; % mW
    
    % 初始化数据
    env.time_vector = [];
    env.imu_data = [];
    env.emg_data = [];
    env.ecg_data = [];
    env.rssi_data = [];
    
    % 环境状态
    env.current_state = [];
    env.episode_step = 0;
    
    % 奖励参数
    env.energy_weight = 0.5;
    env.pdr_weight = 0.3;
    env.delay_weight = 0.2;
    
    % 环境方法
    env.reset = @() reset_environment(env);
    env.step = @(action) step_environment(env, action);
    env.get_current_state = @() get_current_state(env);
    
    fprintf('RL环境创建完成\n');
end

function state = reset_environment(env)
    % 重置环境
    
    env.current_step = 1;
    env.episode_step = 0;
    
    % 初始化默认数据（如果没有加载运动数据）
    if isempty(env.time_vector)
        initialize_default_data(env);
    end
    
    % 获取初始状态
    state = get_current_state(env);
    
    fprintf('环境已重置\n');
end

function initialize_default_data(env)
    % 初始化默认数据
    
    env.time_vector = 0:0.1:(env.max_steps-1)*0.1;
    
    % 生成默认IMU数据
    env.imu_data = 0.5 + 0.2 * sin(2*pi*0.1*env.time_vector) + 0.1 * randn(size(env.time_vector));
    env.imu_data = max(0, env.imu_data);
    
    % 生成默认EMG数据
    env.emg_data = 25 + 10 * sin(2*pi*0.05*env.time_vector) + 5 * randn(size(env.time_vector));
    env.emg_data = max(0, env.emg_data);
    
    % 生成默认ECG数据（心率）
    env.ecg_data = 75 + 15 * sin(2*pi*0.02*env.time_vector) + 5 * randn(size(env.time_vector));
    env.ecg_data = max(60, min(150, env.ecg_data));
    
    % 生成默认RSSI数据
    env.rssi_data = -70 + 10 * sin(2*pi*0.03*env.time_vector) + 5 * randn(size(env.time_vector));
    env.rssi_data = max(-100, min(-40, env.rssi_data));
end

function state = get_current_state(env)
    % 获取当前状态
    
    if env.current_step > length(env.time_vector)
        env.current_step = length(env.time_vector);
    end
    
    % 构建状态向量
    state = zeros(env.state_dim, 1);
    
    % 当前时刻的生物信号
    state(1) = env.imu_data(env.current_step);
    state(2) = env.emg_data(env.current_step);
    state(3) = env.ecg_data(env.current_step);
    state(4) = env.rssi_data(env.current_step);
    
    % 历史信息（如果有的话）
    if env.current_step > 1
        state(5) = env.imu_data(env.current_step - 1);
        state(6) = env.emg_data(env.current_step - 1);
    else
        state(5) = state(1);
        state(6) = state(2);
    end
    
    % 时间信息
    state(7) = env.current_step / env.max_steps; % 归一化时间
    state(8) = env.episode_step / env.max_steps; % 归一化步数
    
    env.current_state = state;
end

function [next_state, reward, done, info] = step_environment(env, action)
    % 执行一步环境交互
    
    % 确保动作在有效范围内
    action = max(1, min(env.action_dim, round(action)));
    
    % 获取当前功率
    current_power = env.power_levels(action);
    
    % 计算通信性能
    [pdr, delay] = calculate_communication_performance(env, current_power);
    
    % 计算能耗
    energy = calculate_energy_consumption(env, current_power);
    
    % 计算奖励
    reward = calculate_reward(env, energy, pdr, delay);
    
    % 更新步数
    env.current_step = env.current_step + 1;
    env.episode_step = env.episode_step + 1;
    
    % 检查是否结束
    done = (env.current_step > env.max_steps) || (env.episode_step >= env.max_steps);
    
    % 获取下一状态
    if ~done
        next_state = get_current_state(env);
    else
        next_state = env.current_state; % 保持当前状态
    end
    
    % 信息结构
    info = struct();
    info.energy = energy;
    info.pdr = pdr;
    info.delay = delay;
    info.power = current_power;
    info.action = action;
end

function [pdr, delay] = calculate_communication_performance(env, power)
    % 计算通信性能
    
    % 获取当前信道状态
    current_rssi = env.rssi_data(env.current_step);
    
    % 计算信噪比
    noise_power = -90; % dBm
    signal_power = current_rssi + 10*log10(power); % 考虑发射功率
    snr = signal_power - noise_power;
    
    % 基于SNR计算PDR
    if snr > 20
        pdr = 0.95 + 0.05 * rand();
    elseif snr > 10
        pdr = 0.8 + 0.15 * (snr - 10) / 10 + 0.05 * rand();
    elseif snr > 0
        pdr = 0.5 + 0.3 * snr / 10 + 0.05 * rand();
    else
        pdr = 0.1 + 0.4 * max(0, (snr + 10) / 10) + 0.05 * rand();
    end
    
    pdr = max(0, min(1, pdr));
    
    % 基于SNR和运动强度计算延迟
    motion_intensity = env.imu_data(env.current_step);
    base_delay = 10; % ms
    
    % SNR越低延迟越高
    snr_delay = max(0, (20 - snr) * 2);
    
    % 运动强度越高延迟越高（由于信道变化）
    motion_delay = motion_intensity * 5;
    
    delay = base_delay + snr_delay + motion_delay + 2 * rand();
    delay = max(5, delay);
end

function energy = calculate_energy_consumption(env, power)
    % 计算能耗
    
    % 基础能耗（与功率成正比）
    transmission_time = 0.1; % 秒
    base_energy = power * transmission_time; % mJ
    
    % 处理能耗
    processing_energy = 2 + rand(); % mJ
    
    % 运动相关的额外能耗
    motion_intensity = env.imu_data(env.current_step);
    motion_energy = motion_intensity * 1; % mJ
    
    energy = base_energy + processing_energy + motion_energy;
    energy = max(1, energy);
end

function reward = calculate_reward(env, energy, pdr, delay)
    % 计算奖励函数
    
    % 能耗奖励（越低越好）
    energy_reward = -energy / 50; % 归一化
    
    % PDR奖励（越高越好）
    pdr_reward = pdr * 100;
    
    % 延迟奖励（越低越好）
    delay_reward = -delay / 20; % 归一化
    
    % 加权组合
    reward = env.energy_weight * energy_reward + ...
             env.pdr_weight * pdr_reward + ...
             env.delay_weight * delay_reward;
    
    % 添加小的随机扰动
    reward = reward + 0.1 * randn();
end

% 辅助函数：加载BVH文件的简化版本
function [skeleton, time] = loadbvh(filename)
    % 简化的BVH加载函数
    
    fprintf('尝试加载BVH文件: %s\n', filename);
    
    % 检查文件是否存在
    if ~exist(filename, 'file')
        error('BVH文件不存在: %s', filename);
    end
    
    try
        % 尝试读取文件
        fid = fopen(filename, 'r');
        if fid == -1
            error('无法打开文件: %s', filename);
        end
        
        % 读取文件头信息
        line = fgetl(fid);
        frames = 0;
        frame_time = 0.00833333; % 默认120fps
        
        while ischar(line)
            if contains(line, 'Frames:')
                frames = sscanf(line, 'Frames: %d');
            elseif contains(line, 'Frame Time:')
                frame_time = sscanf(line, 'Frame Time: %f');
            elseif contains(line, 'MOTION')
                break;
            end
            line = fgetl(fid);
        end
        
        % 生成时间向量
        time = (0:frames-1) * frame_time;
        
        % 创建简化的骨骼结构
        skeleton = struct();
        skeleton.name = 'root';
        
        % 读取运动数据（简化版）
        motion_data = [];
        while ~feof(fid)
            line = fgetl(fid);
            if ischar(line) && ~isempty(line)
                data = str2num(line);
                if ~isempty(data)
                    motion_data = [motion_data; data];
                end
            end
        end
        
        fclose(fid);
        
        % 存储运动数据
        if ~isempty(motion_data)
            skeleton.Dxyz = motion_data(:, 1:3)'; % 位置数据
            if size(motion_data, 2) >= 6
                skeleton.rxyz = motion_data(:, 4:6)'; % 旋转数据
            else
                skeleton.rxyz = zeros(3, size(motion_data, 1));
            end
        else
            % 生成默认数据
            skeleton.Dxyz = randn(3, frames);
            skeleton.rxyz = randn(3, frames);
        end
        
        fprintf('BVH文件加载成功: %d帧, %.3f秒\n', frames, time(end));
        
    catch ME
        fclose(fid);
        fprintf('BVH文件加载失败: %s\n', ME.message);
        
        % 生成默认数据
        frames = 1000;
        time = (0:frames-1) * 0.00833333;
        skeleton = struct();
        skeleton.name = 'root';
        skeleton.Dxyz = randn(3, frames);
        skeleton.rxyz = randn(3, frames);
        
        fprintf('使用默认骨骼数据\n');
    end
end

% 辅助函数：获取最佳动作（从训练模块引用）
function action = get_best_action(agent, state)
    % 获取最优动作
    
    try
        if isfield(agent, 'upper_weights') && isfield(agent, 'lower_weights')
            % 分层RL智能体
            upper_output = forward_pass(agent.upper_weights, state);
            policy_weights = softmax(upper_output);
            
            lower_input = [state; policy_weights];
            lower_output = forward_pass(agent.lower_weights, lower_input);
            
            [~, action] = max(lower_output);
            
        elseif isfield(agent, 'q_weights')
            % DQN智能体
            q_values = forward_q_network(agent.q_weights, state);
            [~, action] = max(q_values);
            
        elseif isfield(agent, 'select_action')
            % 其他类型的智能体
            action = agent.select_action(state);
            
        else
            % 默认随机动作
            action = randi(4);
        end
        
    catch
        % 出错时随机选择
        action = randi(4);
    end
end

function output = forward_pass(weights, input)
    % 前向传播
    z1 = weights.W1 * input + weights.b1;
    a1 = max(0, z1); % ReLU
    z2 = weights.W2 * a1 + weights.b2;
    a2 = max(0, z2); % ReLU
    output = weights.W3 * a2 + weights.b3;
end

function q_values = forward_q_network(weights, state)
    % Q网络前向传播
    z1 = weights.W1 * state + weights.b1;
    a1 = max(0, z1); % ReLU
    q_values = weights.W2 * a1 + weights.b2;
end

function y = softmax(x)
    % Softmax函数
    exp_x = exp(x - max(x));
    y = exp_x / sum(exp_x);
end
